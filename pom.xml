<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.6.1</version>
    <relativePath/>
  </parent>

  <groupId>com.rootcloud.ids</groupId>
  <artifactId>ids</artifactId>
  <version>1.0.0-RELEASE</version>
  <name>ids</name>
  <description>cloud platform for zentoo</description>
  <packaging>pom</packaging>

  <modules>
    <module>ids-common</module>
    <module>ids-ucenter</module>
    <module>ids-auth</module>
    <module>ids-iam</module>
  </modules>

  <properties>
    <ids.version>1.0.0-RELEASE</ids.version>
    <esmp.version>0.0.32-SNAPSHOT</esmp.version>

    <spring-boot.version>2.6.1</spring-boot.version>
    <spring-cloud.version>2021.0.0</spring-cloud.version>
    <spring-cloud-alibaba.version>2.2.1.RELEASE</spring-cloud-alibaba.version>
    <spring-cloud-starter.version>3.1.0</spring-cloud-starter.version>

    <druid.version>1.2.8</druid.version>
    <mybatis-plus.version>3.4.3</mybatis-plus.version>
    <mybatis-plus-generator.version>3.5.1</mybatis-plus-generator.version>
    <lombok.version>1.18.22</lombok.version>
    <hutool.version>5.7.16</hutool.version>
    <hibernate-validator.version>7.0.1.Final</hibernate-validator.version>
    <knife4j.version>2.0.9</knife4j.version>
    <commons-pool2.version>2.11.1</commons-pool2.version>
    <mysql.version>8.0.27</mysql.version>
    <pagehelper-spring-boot.version>1.4.1</pagehelper-spring-boot.version>
    <sleuth.version>3.1.0</sleuth.version>
    <freemarker.version>2.3.31</freemarker.version>
    <nimbus-jose-jwt.version>9.10.1</nimbus-jose-jwt.version>
    <spring-cloud-starter-netflix-ribbon.version>2.2.9.RELEASE</spring-cloud-starter-netflix-ribbon.version>
    <hibernate-validator.version>6.0.18.Final</hibernate-validator.version>
    <bucket4j-core.version>4.4.1</bucket4j-core.version>
    <httpclient.version>4.5.13</httpclient.version>
    <java-jwt.version>3.4.0</java-jwt.version>
    <fastjson.version>1.2.66</fastjson.version>
    <genlian-sdk-iam.version>1.0-SNAPSHOT</genlian-sdk-iam.version>
    <common-lang.version>2.6</common-lang.version>
    <jackson.version>2.12.4</jackson.version>
    <flyway.version>8.0.4</flyway.version>

    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <sonar.coverage.exclusions>
      **/com/rootcloud/ids/auth/**,
      **/com/rootcloud/ids/common/**,
      **/com/rootcloud/ids/idsgateway/**,
      **/com/rootcloud/ids/iam/**,
      **/com/rootcloud/ids/ucenter/service/**
    </sonar.coverage.exclusions>
  </properties>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <groupId>com.rootcloud.esmp</groupId>
        <artifactId>esmp-common</artifactId>
        <version>${esmp.version}</version>
      </dependency>
      <dependency>
        <groupId>com.rootcloud.esmp</groupId>
        <artifactId>esmp-iam</artifactId>
        <version>${esmp.version}</version>
      </dependency>
      <dependency>
        <groupId>com.rootcloud.esmp</groupId>
        <artifactId>esmp-web</artifactId>
        <version>${esmp.version}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring-cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-alibaba-dependencies</artifactId>
        <version>${spring-cloud-alibaba.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter</artifactId>
        <version>${spring-cloud-starter.version}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <version>${spring-boot.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-sleuth</artifactId>
        <version>${sleuth.version}</version>
      </dependency>

      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>${mysql.version}</version>
        <scope>runtime</scope>
      </dependency>

      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-starter</artifactId>
        <version>${druid.version}</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-generator</artifactId>
        <version>${mybatis-plus-generator.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.freemarker</groupId>
        <artifactId>freemarker</artifactId>
        <version>${freemarker.version}</version>
        <scope>compile</scope>
      </dependency>

      <dependency>
        <groupId>com.github.pagehelper</groupId>
        <artifactId>pagehelper-spring-boot-starter</artifactId>
        <version>${pagehelper-spring-boot.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator</artifactId>
        <version>${hibernate-validator.version}</version>
      </dependency>

      <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-spring-boot-starter</artifactId>
        <version>${knife4j.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-pool2</artifactId>
        <version>${commons-pool2.version}</version>
      </dependency>

      <dependency>
        <groupId>com.nimbusds</groupId>
        <artifactId>nimbus-jose-jwt</artifactId>
        <version>${nimbus-jose-jwt.version}</version>
      </dependency>

      <dependency>
        <groupId>com.rootcloud.ids</groupId>
        <artifactId>ids-common</artifactId>
        <version>${ids.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-validator</artifactId>
        <version>${hibernate-validator.version}</version>
      </dependency>
      <!-- Redis -->
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
        <version>${spring-boot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.vladimir-bukhtoyarov</groupId>
        <artifactId>bucket4j-core</artifactId>
        <version>${bucket4j-core.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${httpclient.version}</version>
      </dependency>
      <dependency>
        <groupId>com.auth0</groupId>
        <artifactId>java-jwt</artifactId>
        <version>${java-jwt.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>${fastjson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.rootcloud.ids</groupId>
        <artifactId>ids-iam</artifactId>
        <version>${ids.version}</version>
      </dependency>

      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-core</artifactId>
        <version>${flyway.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <repositories>
    <repository>
      <id>aliyun-repository</id>
      <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>

    <repository>
      <id>nexus</id>
      <url>http://nexus.lifehub.top/repository/maven-public/</url>
      <releases>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
    </repository>

    <repository>
      <id>root-cloud-nexus</id>
      <url>https://nexus.rootcloudapp.com/repository/maven-public/</url>
      <releases>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
    </repository>
  </repositories>

</project>