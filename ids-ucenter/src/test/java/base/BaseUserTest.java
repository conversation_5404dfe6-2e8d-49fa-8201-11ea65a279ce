package base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rootcloud.ids.ucenter.UcenterApplication;
import com.rootcloud.ids.ucenter.service.base.IBaseUserService;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDeptEquipmentResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseUserEquipmentAddRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseUserEquipmentRemoveRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseUserPossessPageRest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/13 17:16
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = UcenterApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Ignore
public class BaseUserTest {

    @Autowired
    private IBaseUserService iBaseUserService;

    @Test
    public void pageNoPossessTest() {
        BaseUserPossessPageRest baseUserPossessPageRest = new BaseUserPossessPageRest();
        baseUserPossessPageRest.setPossess(false);
        Page<BaseDeptEquipmentResp> result = iBaseUserService.pagePossess(baseUserPossessPageRest);
        log.info("result:{}",result.toString());
    }

    @Test
    public void listPossessTest() {
        log.info("result:{}",iBaseUserService.listTenantPossess());
    }

    @Test
    public void addAuthorityTest() {
        BaseUserEquipmentAddRest rest = new BaseUserEquipmentAddRest();
        boolean result = iBaseUserService.addAuthority(rest);
        log.info("result:{}",result);
    }

    @Test
    public void removeAuthorityTest() {
        BaseUserEquipmentRemoveRest rest = new BaseUserEquipmentRemoveRest();
        boolean result = iBaseUserService.removeAuthority(rest);
        log.info("result:{}",result);
    }


}
