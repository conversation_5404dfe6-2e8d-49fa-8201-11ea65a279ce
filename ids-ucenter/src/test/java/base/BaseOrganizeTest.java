package base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rootcloud.ids.common.core.result.PageResult;
import com.rootcloud.ids.common.core.result.Result;
import com.rootcloud.ids.ucenter.UcenterApplication;
import com.rootcloud.ids.ucenter.service.base.IBaseOrganizationService;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDepartmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseOrganizationResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseUserListResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseOrganizeUserInfoRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseOrganizeUserPageRest;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.bind.annotation.GetMapping;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/13 17:16
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = UcenterApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Ignore
public class BaseOrganizeTest {

    @Autowired
    private IBaseOrganizationService iBaseOrganizationService;

    @Test
    public void listTest() {
        List<BaseOrganizationResp> result = iBaseOrganizationService.list();
        log.info("result:{}",result.toString());
    }

    @Test
    public void pageByParamTest() {
        BaseOrganizeUserPageRest baseOrganizeUserPageRest = new BaseOrganizeUserPageRest();
        baseOrganizeUserPageRest.setId("62136968aee8d7004f1d11e9");
        baseOrganizeUserPageRest.setOrganizationName("泵送事业部");
        Page<BaseUserListResp> result = iBaseOrganizationService.pageUserByParam(baseOrganizeUserPageRest);
        log.info("result:{}",result.toString());
    }

    @Test
    public void userInfoTest() {
        BaseOrganizeUserInfoRest baseOrganizeUserInfoRest = new BaseOrganizeUserInfoRest();
        baseOrganizeUserInfoRest.setUserId("622f600ded2c7f005835984c");
        baseOrganizeUserInfoRest.setCid("62136968aee8d7004f1d11e9");
        BaseUserListResp result = iBaseOrganizationService.userInfo(baseOrganizeUserInfoRest);
        log.info("result:{}",result.toString());
    }


}
