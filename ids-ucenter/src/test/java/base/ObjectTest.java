package base;


import com.rootcloud.ids.common.core.utils.ClassUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 一些 dto/param/resp/vo 的get/set/构造方法 覆盖测试
 */
@RunWith(SpringRunner.class)
public class ObjectTest {
    private static final Logger logger = LoggerFactory.getLogger(ObjectTest.class);

    @Test
    public void mockDto() {
        testDto("com.rootcloud.ids.ucenter.vo");
        testDto("com.rootcloud.ids.ucenter.dto");
        testDto("com.rootcloud.ids.ucenter.entity");
        testDto("com.rootcloud.ids.ucenter.enums");
    }

    @Test
    public void mockServiceImpl() {
        testServiceImpl("com.rootcloud.ids.ucenter.utils");
        testServiceImpl("com.rootcloud.ids.ucenter.config");
        testServiceImpl("com.rootcloud.ids.ucenter.controller");
    }

    public void testServiceImpl(String packageName) {
        List<String> classNameList = getClassNameList(packageName);
        classNameList.forEach(className -> invokeServiceMethod(className, true));
    }

    public void testDto(String packageName) {
        List<String> classNameList = getClassNameList(packageName);
        classNameList.forEach(className -> invokeMethod(className, true));
    }

    private List<String> getClassNameList(String packageName) {
        List<Class<?>> classList = ClassUtil.getClassList(packageName, true);
        List<String> classNameList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(classList)) {
            classList.stream().forEach(x -> classNameList.add(x.getName()));
        }

        return classNameList;
    }

    /**
     * 调用类的所有方法
     */
    private void invokeServiceMethod(String classPath, boolean buildParams) {
        try {
            Class cls = Class.forName(classPath);
            Object obj = cls.newInstance();
            fillAttribute(obj, cls);
            Method[] methods = cls.getMethods();
            for (int i = 0; i < methods.length; i++) {
                try {
                    methods[i].setAccessible(true);
                    Object[] params = buildParams(buildParams, methods[i]);
                    methods[i].invoke(obj, params);
                } catch (Throwable e) {
                    //log
                }
            }

        } catch (Exception e) {
            //log
        }
    }

    private void fillAttribute(Object obj, Class clz) {
        try {
            Field[] fields = clz.getDeclaredFields();
            for (int i = 0; i < fields.length; i++) {
                try {
                    fields[i].setAccessible(true);
                    fields[i].set(obj, fields[i].getType().newInstance());
                } catch (IllegalAccessException e) {
                    //log
                }
            }
        } catch (Throwable e) {
        }

    }

    /**
     * 调用类的所有方法
     */
    private void invokeMethod(String classPath, boolean buildParams) {
        try {
            Class cls = Class.forName(classPath);
            Object obj = cls.newInstance();
            Method[] methods = cls.getMethods();
            for (int i = 0; i < methods.length; i++) {
                try {
                    methods[i].setAccessible(true);
                    Object[] params = buildParams(buildParams, methods[i]);
                    methods[i].invoke(obj, params);
                } catch (Throwable e) {
                    //log
                }
            }
        } catch (Exception e) {
            //log
        }
    }

    private Object[] buildParams(boolean buildParams, Method method) throws InstantiationException, IllegalAccessException {
        Class<?>[] parameterTypes = method.getParameterTypes();
        Object[] params = new Object[parameterTypes.length];
        if (buildParams) {
            for (int i = 0; i < parameterTypes.length; i++) {
                try {
                    Object param = parameterTypes[i].newInstance();
                    params[i] = param;
                } catch (Throwable e) {
                    //log
                }
            }
        }
        return params;
    }

}

