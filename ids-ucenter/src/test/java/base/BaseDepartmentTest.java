package base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rootcloud.ids.ucenter.UcenterApplication;
import com.rootcloud.ids.ucenter.enums.base.DefaultFlagEnum;
import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import com.rootcloud.ids.ucenter.service.base.IBaseDepartmentService;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDepartmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDeptEquipmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseEquipmentResp;
import com.rootcloud.esmp.common.dto.cache.BaseProductResp;
import com.rootcloud.ids.ucenter.vo.rest.base.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/13 17:03
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = UcenterApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Ignore
public class BaseDepartmentTest {

    @Autowired
    private IBaseDepartmentService departmentService;

    @Test
    public void listDepartmentTest() {
        List<BaseDepartmentResp> result = departmentService.listDepartment(true, "62136968aee8d7004f1d11e9");
        log.info("result:{}",result.toString());
    }

    @Test
    public void pagePossessTest() {
        BaseDeptEquipmentPageRest param = new BaseDeptEquipmentPageRest();
        param.setPossess(true);
        Page<BaseDeptEquipmentResp> result = departmentService.pageDept(param);
        log.info("result:{}",result.toString());
    }

    @Test
    public void pageNoPossessTest() {
        BaseDeptEquipmentPageRest param = new BaseDeptEquipmentPageRest();
        param.setPossess(false);
        Page<BaseDeptEquipmentResp> result = departmentService.pageDept(param);
        log.info("result:{}",result.toString());
    }

    @Test
    public void addEquipmentTest() {
        BaseDeptEquipmentAddRest baseDeptEquipmentAddRest = new BaseDeptEquipmentAddRest();
        List<BaseDeptEquipmentAddDTO> list = new ArrayList<>();
        BaseDeptEquipmentAddDTO baseDeptEquipmentAddDTO = new BaseDeptEquipmentAddDTO();
        baseDeptEquipmentAddDTO.setEquipmentId(1511994524509904897L);
        baseDeptEquipmentAddDTO.setDefaultFlag(DefaultFlagEnum.DEFAULT.getCode());
        list.add(baseDeptEquipmentAddDTO);
        baseDeptEquipmentAddRest.setDeptId("625554af574a6a0058eeb6bd");
        baseDeptEquipmentAddRest.setEquipmentList(list);
        baseDeptEquipmentAddRest.setDeptType(DeptTypeEnum.DEPARTMENT);
        boolean result = departmentService.addEquipment(baseDeptEquipmentAddRest);
        log.info("result:{}",result);
    }

    @Test
    public void getEquipmentTest() {
        BaseDeptEquipmentRest baseDeptEquipmentRest = new BaseDeptEquipmentRest();
        baseDeptEquipmentRest.setId("625554af574a6a0058eeb6bd");
        baseDeptEquipmentRest.setDeptType(DeptTypeEnum.DEPARTMENT);
        List<BaseEquipmentResp> result = departmentService.getEquipment(baseDeptEquipmentRest);
        log.info("result:{}",result.toString());
    }

    @Test
    public void removeEquipmentTest() {
        BaseDeptEquipmentDelRest baseDeptEquipmentDelRest = new BaseDeptEquipmentDelRest();
        baseDeptEquipmentDelRest.setEquipmentId(1511994283450671105L);
        baseDeptEquipmentDelRest.setDeptId("622f5fb2fd477e00592c0298");
        baseDeptEquipmentDelRest.setCid("62136968aee8d7004f1d11e9");
        boolean result = departmentService.removeEquipment(baseDeptEquipmentDelRest);
        log.info("result:{}",result);
    }

}
