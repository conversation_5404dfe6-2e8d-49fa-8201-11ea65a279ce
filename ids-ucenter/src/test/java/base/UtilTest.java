package base;

import static java.time.temporal.ChronoUnit.SECONDS;

import cn.hutool.json.JSONUtil;
import com.rootcloud.ids.ucenter.utils.Util;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class UtilTest {

  @Test
  public void test_local_datetime_desc_comparator() {
    LocalDateTime l1 = null;
    LocalDateTime l2 = LocalDateTime.now();
    LocalDateTime l3 = l2.plus(1, SECONDS);
    LocalDateTime l4 = l3.plus(1, SECONDS);
    LocalDateTime l5 = null;
    List<LocalDateTime> list = new ArrayList<>();
    list.add(l1);
    list.add(l2);
    list.add(l3);
    list.add(l4);
    list.add(l5);
    System.out.println("排序前："+ JSONUtil.toJsonPrettyStr(list));
    list.sort(Util.LOCAL_DATETIME_DESC_COMPARATOR);
    System.out.println("排序后："+JSONUtil.toJsonPrettyStr(list));
  }

  @Test
  public void test_local_datetime_asc_comparator() {
    LocalDateTime l1 = null;
    LocalDateTime l2 = LocalDateTime.now();
    LocalDateTime l3 = l2.plus(1, SECONDS);
    LocalDateTime l4 = l3.plus(1, SECONDS);
    LocalDateTime l5 = null;
    List<LocalDateTime> list = new ArrayList<>();
    list.add(l1);
    list.add(l2);
    list.add(l3);
    list.add(l4);
    list.add(l5);
    System.out.println("排序前："+ JSONUtil.toJsonPrettyStr(list));
    list.sort(Util.LOCAL_DATETIME_ASC_COMPARATOR);
    System.out.println("排序后："+JSONUtil.toJsonPrettyStr(list));
  }

}
