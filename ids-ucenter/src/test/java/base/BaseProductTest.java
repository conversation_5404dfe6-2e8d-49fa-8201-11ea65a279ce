package base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rootcloud.ids.ucenter.UcenterApplication;
import com.rootcloud.ids.ucenter.service.base.IBaseProductService;
import com.rootcloud.ids.ucenter.utils.convertor.base.BaseProductRespConvertor;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseEquipmentModelResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseProductDetailResp;
import com.rootcloud.esmp.common.dto.cache.BaseProductResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseEquipmentModelPageParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseProEquipmentParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseProductParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseProductUpParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/11 15:04
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = UcenterApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Ignore
public class BaseProductTest {

    @Autowired
    private IBaseProductService baseProductService;

    @Test
    public void listProductTest() {
        List<BaseProductResp> result = baseProductService.listProduct();
        log.info("result:{}",result.toString());
    }

    @Test
    public void getByIdTest(){
        BaseProductDetailResp result = baseProductService.getInfo(1511994315520319490L);
        log.info("result:{}",result.toString());
    }

    @Test
    public void addTest(){
        BaseProductParam baseProductParam = new BaseProductParam();
        baseProductParam.setId(1501750874561015888L);
        baseProductParam.setProductName("石油机械");
        long result = baseProductService.add(baseProductParam);
        log.info("result:{}",result);
    }

    @Test
    public void addEquipmentTest(){
        BaseProEquipmentParam baseProEquipmentParam = new BaseProEquipmentParam();
        baseProEquipmentParam.setId(1507265732051988481L);
        baseProEquipmentParam.setProductName("设备机型名称");
        long result = baseProductService.addEquipment(baseProEquipmentParam);
        log.info("result:{}",result);
    }

    @Test
    public void updateNameTest(){
        BaseProductUpParam baseProductUpParam = new BaseProductUpParam();
        baseProductUpParam.setId(1507261683231617025L);
        baseProductUpParam.setProductName("更改名称");
        boolean result = baseProductService.updateById(baseProductUpParam);
        log.info("result:{}",result);
    }

    @Test
    public void pageModelByParamTest(){
        BaseEquipmentModelPageParam baseEquipmentModelPageParam = new BaseEquipmentModelPageParam();
        baseEquipmentModelPageParam.setId(1507265732051988481L);
        baseEquipmentModelPageParam.setProductName("石油机械机械A");
        baseEquipmentModelPageParam.setPageSize(10);
        baseEquipmentModelPageParam.setPage(1);
        Page<BaseEquipmentModelResp> result = baseProductService.pageModelByParam(baseEquipmentModelPageParam);
        log.info("result:{}",result.toString());
    }

    @Test
    public void listModelTest(){
        List<BaseProductResp> result = baseProductService.listModel(1501750874561015899L);
        log.info("result:{}",result.toString());
    }

    @Test
    public void listMachineryTest(){
        log.info("result:{}",baseProductService.allConstructionMachinery().stream().map(BaseProductRespConvertor::change2ProductResp).collect(Collectors.toList()));
    }


}
