package i18n;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.ImmutableTable;
import com.rootcloud.esmp.common.enums.IamPermissionActionEnum;
import com.rootcloud.esmp.common.enums.PermissionQueryEnum;
import com.rootcloud.esmp.common.i18n.I18nCommonCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import com.rootcloud.ids.common.i18n.I18nCode;
import java.util.Locale;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

public class I18nTest {

  public static void main(String[] args) throws Exception {
    //List<String> modules = Collections.singletonList("ids-ucenter");
    //String codeStart = "SYS_100158";
    //int enumStartRowNum = 164;
    //int zhPropertiesInsertRow = 158;
    //int enPropertiesInsertRow = 158;
    //ChineseFinder.multiModuleScan(
    //    codeStart,
    //    enumStartRowNum,
    //    zhPropertiesInsertRow,
    //    enPropertiesInsertRow,
    //    "/ids-common",
    //    modules);

    ImmutableTable.Builder<IamPermissionActionEnum, PermissionQueryEnum, String> permissionSqlMap =
        ImmutableTable.builder();
    permissionSqlMap.put(IamPermissionActionEnum.MAINTENANCE, PermissionQueryEnum.CROSS_TENANT, "db/history/sql");
    System.out.println(JSONUtil.toJsonStr(permissionSqlMap.build()));

    System.out.println("(( a == 1 and {{abc}}.abc = 2 and {{abc}}.ddd = 3))".replace("{{abc}}", ""));

    ResourceLoader resourceLoader = new DefaultResourceLoader();
    Resource resource = resourceLoader.getResource("i18n/messages_en.properties");
    System.out.println(resource.exists());

    resource = resourceLoader.getResource("messages_en.properties");
    System.out.println(resource.exists());

    resource = resourceLoader.getResource("abc.properties");
    System.out.println(resource.exists());

    resource = resourceLoader.getResource("abc/abc.properties");
    System.out.println(resource.exists());

    resource = resourceLoader.getResource("templates/controller.java.btl");
    System.out.println(resource.exists());

    LocaleContextHolder.setDefaultLocale(new Locale("en"));
    System.out.println(I18nUtil.getCurrentLanguage());
    System.out.println(I18nCommonCode.COM_100003.message());
    System.out.println(I18nCode.SYS_100003.message());
  }
}
