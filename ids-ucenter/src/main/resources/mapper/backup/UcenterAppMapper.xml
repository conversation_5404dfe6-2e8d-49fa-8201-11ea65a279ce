<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rootcloud.ids.ucenter.dao.backup.UcenterAppMapper">

    <select id="query" resultType="com.rootcloud.ids.ucenter.vo.backup.SysAppQueryVO">
        SELECT * FROM ucenter_app
        <where>
            <if test="cnd.appName != null and cnd.appName != ''">
                AND app_name like concat('%',#{cnd.appName},'%'),
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
