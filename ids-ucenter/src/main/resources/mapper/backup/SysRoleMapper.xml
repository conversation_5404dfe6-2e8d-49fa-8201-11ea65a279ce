<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rootcloud.ids.ucenter.dao.backup.UcenterRoleMapper">

    <select id="query" resultType="com.rootcloud.ids.ucenter.vo.backup.SysRoleQueryVO">
        SELECT * FROM ucenter_role
        <where>
            <if test="cnd.roleName != null and cnd.roleName != ''">
                AND role_name like concat('%',#{cnd.roleName},'%'),
            </if>
            <if test="cnd.englishName != null and cnd.englishName != ''">
                AND english_name like concat('%',#{cnd.englishName},'%'),
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
