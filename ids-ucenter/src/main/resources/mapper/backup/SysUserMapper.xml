<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rootcloud.ids.ucenter.dao.backup.UcenterUserMapper">

    <select id="query" resultType="com.rootcloud.ids.ucenter.vo.backup.SysUserQueryVO">
        SELECT * FROM ucenter_user
        <where>
            <if test="cnd.account != null and cnd.account != ''">
                AND account like concat('%',#{cnd.account},'%'),
            </if>
            <if test="cnd.userName != null and cnd.userName != ''">
                AND user_name like concat('%',#{cnd.userName},'%'),
            </if>
            <if test="cnd.phone != null and cnd.phone != ''">
                AND phone = #{cnd.phone},
            </if>
            <if test="cnd.sex != null">
                AND sex = #{cnd.sex},
            </if>
            <if test="cnd.email != null and cnd.email != ''">
                AND email = #{cnd.email},
            </if>
            <if test="cnd.cardType != null and cnd.cardType != ''">
                AND card_type = #{cnd.cardType},
            </if>
            <if test="cnd.cardNo != null and cnd.cardNo != ''">
                AND id_card like concat('%',#{cnd.idCard},'%'),
            </if>
            <if test="cnd.registFrom != null">
                AND regist_from = #{cnd.registFrom},
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
