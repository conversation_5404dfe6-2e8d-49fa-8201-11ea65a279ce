<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rootcloud.ids.ucenter.dao.backup.UcenterMenuMapper">

    <select id="query" resultType="com.rootcloud.ids.ucenter.vo.backup.SysMenuQueryVO">
        SELECT * FROM ucenter_menu
        <where>
            <if test="cnd.menuName != null and cnd.menuName != ''">
                AND menu_name like concat('%',#{cnd.menuName},'%'),
            </if>
            <if test="cnd.englishName != null and cnd.englishName != ''">
                AND english_name like concat('%',#{cnd.englishName},'%'),
            </if>
            <if test="cnd.showName != null and cnd.showName != ''">
                AND show_name like concat('%',#{cnd.showName},'%'),
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="findMenuByUserId" resultType="com.rootcloud.ids.ucenter.vo.backup.SysMenuVO">
        SELECT c.*FROM ucenter_menu c WHERE c.menu_id IN (
            SELECT b.menu_id FROM ucenter_group_menu b WHERE b.group_id IN (
                SELECT a.group_id FROM ucenter_user_group a WHERE a.user_id=#{userId}))
    </select>

</mapper>
