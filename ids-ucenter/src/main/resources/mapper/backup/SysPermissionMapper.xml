<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rootcloud.ids.ucenter.dao.backup.UcenterPermissionMapper">
    <select id="findRolePermissionByUserId" resultType="com.rootcloud.ids.ucenter.vo.backup.SysPermissionVO">
        SELECT c.* FROM ucenter_permission c WHERE c.permission_id IN (
            SELECT DISTINCT b.permission_id FROM ucenter_role_permission b WHERE b.role_id IN (
                SELECT a.role_id FROM ucenter_user_role a WHERE a.user_id=#{userId}))
    </select>
    <select id="findGroupPermissionByUserId" resultType="com.rootcloud.ids.ucenter.vo.backup.SysPermissionVO">
        SELECT c.* FROM ucenter_permission c WHERE c.permission_id IN (
            SELECT DISTINCT b.permission_id FROM ucenter_group_permission b WHERE b.group_id IN (
                SELECT a.group_id FROM ucenter_user_group a WHERE a.user_id=#{userId}))
    </select>
    <select id="findUserPermissionByUserId" resultType="com.rootcloud.ids.ucenter.vo.backup.SysPermissionVO">
        SELECT c.* FROM ucenter_permission c WHERE c.permission_id IN (
            SELECT DISTINCT b.permission_id FROM ucenter_user_permission b WHERE b.user_id  =#{userId})
    </select>
</mapper>
