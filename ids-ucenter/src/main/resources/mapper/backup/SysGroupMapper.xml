<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rootcloud.ids.ucenter.dao.backup.UcenterGroupMapper">

    <select id="query" resultType="com.rootcloud.ids.ucenter.vo.backup.SysGroupQueryVO">
        SELECT * FROM ucenter_group
        <where>
            <if test="cnd.groupName != null and cnd.groupName != ''">
                AND group_name like concat('%',#{cnd.groupName},'%'),
            </if>
            <if test="cnd.englishName != null and cnd.englishName != ''">
                AND english_name like concat('%',#{cnd.englishName},'%'),
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
