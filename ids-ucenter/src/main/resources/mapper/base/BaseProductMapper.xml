<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rootcloud.ids.ucenter.dao.base.BaseProductMapper">

  <select id="getProductName" resultType="string">
    SELECT String_agg(t.product_name, '/')
    FROM   ( WITH RECURSIVE r AS (
               SELECT id,
                      parent_id,
    <choose>
      <when test="isEnglish">
                      CASE
                        WHEN product_name_en IS NOT NULL AND product_name_en != '' THEN product_name_en
                        ELSE product_name
                      END AS product_name
      </when>
      <otherwise>
                      product_name
      </otherwise>
    </choose>
               FROM  ucenter_base_product
               WHERE id = #{id}

               UNION ALL

               SELECT a.id,
                      a.parent_id,
    <choose>
      <when test="isEnglish">
                      CASE
                        WHEN a.product_name_en IS NOT NULL AND a.product_name_en != '' THEN a.product_name_en
                        ELSE a.product_name
                      END AS product_name
      </when>
      <otherwise>
                      a.product_name
      </otherwise>
    </choose>
               FROM   ucenter_base_product a, r WHERE a.id = r.parent_id
             )
             SELECT * FROM r ORDER BY id LIMIT 2
           ) t
  </select>

  <select id="findSuperProductNamesByProducts" resultType="com.rootcloud.ids.ucenter.dao.base.BaseProductMapper$ProductName">
    SELECT r.oid                           AS id,
           String_agg(r.product_name, '/') AS name
    FROM   ( WITH RECURSIVE ancestors AS (
               SELECT id,
                      parent_id,
                      id           AS oid,
    <choose>
      <when test="isEnglish">
                      CASE
                        WHEN product_name_en IS NOT NULL AND product_name_en != '' THEN product_name_en
                        ELSE product_name
                      END          AS product_name
      </when>
      <otherwise>
                      product_name
      </otherwise>
    </choose>
               FROM   ucenter_base_product
               WHERE  id IN (<foreach collection="products" item="product" separator=",">#{product.id}</foreach>)

               UNION ALL

               SELECT p.id,
                      p.parent_id,
                      a.oid,
    <choose>
      <when test="isEnglish">
                      CASE
                        WHEN p.product_name_en IS NOT NULL AND p.product_name_en != '' THEN p.product_name_en
                        ELSE p.product_name
                      END          AS product_name
      </when>
      <otherwise>
                      p.product_name
      </otherwise>
    </choose>
               FROM   ucenter_base_product p
                      INNER JOIN ancestors a
                              ON p.id = a.parent_id
             )
             SELECT a.*,
                    ROW_NUMBER() OVER (PARTITION BY a.oid ORDER BY a.id) AS level
             FROM   ancestors a
             ORDER  BY a.id
           ) r
    WHERE  r.level &lt;= 2
    GROUP  BY r.oid
  </select>

</mapper>
