server:
  port: 8087

SPRING_DATASOURCE_USERNAME: postgres
SPRING_DATASOURCE_PASSWORD: postgres@sghl
POSTGRESQL_IP: ***********
POSTGRESQL_PORT: 30806

OS_IAM_DOMAIN: http://iam-identity-service.nc-qa.rootcloudapp.com
OS_IAM_SUPERTOKEN: ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
IAM_AUTHORIZATION_SERVER_ADDRESS: http://iam-authorization-service.nc-qa.rootcloudapp.com
IAM_RESOURCE_REGISTER_ADDRESS: http://iam-authorization-service.nc-qa.rootcloudapp.com

XXL_JOB_ADMIN_ADDRESSES: http://***********:32727/xxl-job-admin/
XXL_JOB_ADMIN_PORT: 9999
APP_LOG_DOMAIN: http://logging-service.nc-qa.rootcloudapp.com/

spring:
  flyway:
    #启用或者禁用flyway
    enabled: false
    #flyway的clean命令会删除指定的schema下的所有表，生产务必禁用
    clean-disabled: true
    #SQL脚本目录，多个目录,分割
    #locations: classpath:migration
    #metadata 版本控制信息表
    table: flyway_schema_history
    # 如果没有 flyway_schema_history 这个 metadata 表， 在执行 flyway migrate 命令之前, 必须先执行 flyway baseline 命令
    # 设置为 true 后 flyway 将在需要 baseline 的时候, 自动执行一次 baseline。
    baseline-on-migrate: true
    # 指定 baseline 的版本号,默认值为 1, 低于该版本号的 SQL 文件, migrate 时会被忽略
    baseline-version: 1
    # 字符编码 默认 UTF-8
    encoding: UTF-8
    # 是否允许不按顺序迁移 开发建议 true  生产建议 false
    out-of-order: false
    # 需要 flyway 管控的 schema list,这里我们配置为flyway  缺省的话, 使用spring.datasource.url 配置的那个 schema,
    # 可以指定多个schema, 但仅会在第一个schema下建立 metadata 表, 也仅在第一个schema应用migration sql 脚本.
    # 但flyway Clean 命令会依次在这些schema下都执行一遍. 所以 确保生产 spring.flyway.clean-disabled 为 true
    #schemas: education_portal
    # 执行迁移时是否自动调用验证   当你的 版本不符合逻辑 比如 你先执行了 DML 而没有 对应的DDL 会抛出异常
    validate-on-migrate: true
    # 不替换占位符
    placeholder-replacement: false
