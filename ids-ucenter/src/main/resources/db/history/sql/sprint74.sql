ALTER TABLE "public"."ucenter_base_dept_pro_rel"
    ADD COLUMN "auth_type" int2 DEFAULT 1;

COMMENT ON COLUMN "public"."ucenter_base_dept_pro_rel"."auth_type" IS '#权限类型#';

ALTER TABLE "public"."ucenter_base_dept_pro_rel"
    ADD COLUMN "cid" varchar(100),
  ADD COLUMN "owner_cid" varchar(100);

COMMENT ON COLUMN "public"."ucenter_base_dept_pro_rel"."cid" IS '#当前部门所属租户id#';

COMMENT ON COLUMN "public"."ucenter_base_dept_pro_rel"."owner_cid" IS '#所有权租户id#';


update "public"."ucenter_base_dept_pro_rel" SET auth_type = 2
where dept_type = 2

ALTER TABLE "public"."ucenter_operation_log"
ALTER COLUMN "log_content" TYPE varchar(4000) COLLATE "pg_catalog"."default";

ALTER TABLE "public"."ucenter_operation_log"
ALTER COLUMN "log_detail" TYPE varchar(4000) COLLATE "pg_catalog"."default";