
-- 判断哪些机型没有租户ID
-- select bp.id, bp.product_name, pt.tenant_id from ucenter_base_product bp
-- left join (select product_id as machine_type_id, max(owner_cid) as tenant_id from ucenter_base_dept_pro_rel ubdpr
-- 	where delete_flag = 0 group by product_id) pt on bp.id = pt.machine_type_id
-- where bp.delete_flag = 0 and product_type = 2 and pt.tenant_id is null order by bp.id, bp.product_name


-- 机型表增加分类名称租户ID字段
alter table ucenter_base_product add tenant_id varchar(200);
comment on column public.ucenter_base_product.tenant_id is '#租户ID#';


-- 机型初始化租户ID
with machine_type_tenant as (
	select product_id as machine_type_id, owner_cid as tenant_id from ucenter_base_dept_pro_rel ubdpr
	where delete_flag = 0 group by product_id ,owner_cid
)
update ucenter_base_product bp set tenant_id = pt.tenant_id
from machine_type_tenant pt where bp.id = pt.machine_type_id and bp.delete_flag = 0 and product_type = 2;


-- 验证
-- with machine_type_tenant as (
-- 	select product_id as machine_type_id, owner_cid as tenant_id from ucenter_base_dept_pro_rel ubdpr
-- 	where delete_flag = 0 group by product_id ,owner_cid
-- )
-- select pt.tenant_id, bp.tenant_id, bp.* from ucenter_base_product bp
-- left join machine_type_tenant pt on bp.id = pt.machine_type_id
-- where bp.delete_flag = 0 and product_type = 2 and pt.tenant_id != bp.tenant_id;


-- 设备型号初始化租户ID
with machine_type_tenant as (
	select product_id as machine_type_id, owner_cid as tenant_id from ucenter_base_dept_pro_rel ubdpr
	where delete_flag = 0 group by product_id ,owner_cid
)
update ucenter_base_product bp set tenant_id = pt.tenant_id
from machine_type_tenant pt where bp.parent_id = pt.machine_type_id and bp.delete_flag = 0 and product_type = 3;

-- 验证
-- with machine_type_tenant as (
-- 	select product_id as machine_type_id, owner_cid as tenant_id from ucenter_base_dept_pro_rel ubdpr where delete_flag = 0 group by product_id ,owner_cid
-- )
-- select pt.tenant_id, bp.tenant_id , bp.* from ucenter_base_product bp
-- left join machine_type_tenant pt on bp.parent_id = pt.machine_type_id
-- where bp.delete_flag = 0 and product_type = 3 and pt.tenant_id != bp.tenant_id;



-- 将creator与modifier字段长度小于130的修改为130
DO $$
<<BODY>>
DECLARE
v_sql VARCHAR;
v_sql_cursor refcursor;
BEGIN
    -- 打开游标
	open v_sql_cursor for
		SELECT 'ALTER TABLE ' || table_name || ' alter COLUMN ' || column_name || ' type character varying(130);'
		FROM information_schema.columns WHERE table_catalog = 'ids' AND table_schema = 'public' AND column_name IN ('creator', 'modifier')
		AND character_maximum_length < 130 AND is_updatable = 'YES' ORDER BY table_name, column_name;
	LOOP fetch v_sql_cursor into v_sql;

		if found then
			RAISE NOTICE 'The current sql is %', v_sql;
			EXECUTE v_sql;
			COMMIT ;
		else exit;
		end if;

	END LOOP;
	-- 关闭游标
	CLOSE v_sql_cursor;
END BODY $$;
