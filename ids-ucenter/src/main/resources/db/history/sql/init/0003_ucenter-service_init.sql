--
-- PostgreSQL database ids
--
-- 创建数据库

CREATE DATABASE ids;

\c ids
--
-- Name: ucenter_base_department; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ucenter_base_department (
    id character varying(100) NOT NULL,
    parent_id character varying(100),
    dept_type smallint,
    dept_name character varying(32),
    english_name character varying(128),
    remark character varying(256),
    version integer,
    creator character varying(130),
    create_time timestamp(0) without time zone,
    modifier character varying(130),
    update_time timestamp(0) without time zone,
    delete_flag integer
);


--
-- Name: TABLE ucenter_base_department; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.ucenter_base_department IS '组织机构表';
COMMENT ON COLUMN public.ucenter_base_department.id IS '#主键id#';
COMMENT ON COLUMN public.ucenter_base_department.parent_id IS '#父级id#';
COMMENT ON COLUMN public.ucenter_base_department.dept_type IS '#部门类型#';
COMMENT ON COLUMN public.ucenter_base_department.dept_name IS '#部门名称#';
COMMENT ON COLUMN public.ucenter_base_department.english_name IS '#部门英文名称#';
COMMENT ON COLUMN public.ucenter_base_department.remark IS '#备注#';
COMMENT ON COLUMN public.ucenter_base_department.version IS '#数据版本#';
COMMENT ON COLUMN public.ucenter_base_department.creator IS '#创建人#';
COMMENT ON COLUMN public.ucenter_base_department.create_time IS '#创建时间#';
COMMENT ON COLUMN public.ucenter_base_department.modifier IS '#修改人#';
COMMENT ON COLUMN public.ucenter_base_department.update_time IS '#修改时间#';
COMMENT ON COLUMN public.ucenter_base_department.delete_flag IS '#逻辑删除标识#';


--
-- Name: ucenter_base_dept_pro_rel; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ucenter_base_dept_pro_rel (
    id bigint,
    dept_id character varying(100),
    product_id bigint,
    dept_type smallint,
    version integer,
    creator character varying(130),
    create_time timestamp(0) without time zone,
    modifier character varying(130),
    update_time timestamp(0) without time zone,
    delete_flag integer,
    auth_type smallint,
    cid character varying(100),
    owner_cid character varying(100)
);

--
-- Name: ucenter_base_pro_user_rel; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ucenter_base_pro_user_rel (
    id bigint,
    user_id character varying(100),
    product_id bigint,
    version integer,
    creator character varying(130),
    create_time timestamp(0) without time zone,
    modifier character varying(130),
    update_time timestamp(0) without time zone,
    delete_flag integer
);


--
-- Name: ucenter_base_product; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ucenter_base_product (
    id bigint NOT NULL,
    parent_id bigint DEFAULT 0 NOT NULL,
    product_name character varying(64) NOT NULL,
    product_status smallint NOT NULL,
    product_type smallint NOT NULL,
    version integer,
    creator character varying(130),
    create_time timestamp(0) without time zone,
    modifier character varying(130),
    update_time timestamp(0) without time zone,
    delete_flag integer,
    product_code smallint,
    product_business_code character varying(32),
    release_status smallint,
    product_version bigint,
    publish_flag boolean,
    product_publish_status character varying,
    topology_id bigint,
    description character varying(500) DEFAULT NULL::character varying,
    topology_version integer,
    has_published integer,
    product_name_en character varying(200)
);


--
-- Name: TABLE ucenter_base_product; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.ucenter_base_product IS '产品分类表';
COMMENT ON COLUMN public.ucenter_base_product.id IS '#分类id#';
COMMENT ON COLUMN public.ucenter_base_product.parent_id IS '#父级id#';
COMMENT ON COLUMN public.ucenter_base_product.product_name IS '#分类名称#';
COMMENT ON COLUMN public.ucenter_base_product.product_status IS '#产品状态#';
COMMENT ON COLUMN public.ucenter_base_product.product_type IS '#分类层级#';
COMMENT ON COLUMN public.ucenter_base_product.version IS '#数据版本#';
COMMENT ON COLUMN public.ucenter_base_product.creator IS '#创建人#';
COMMENT ON COLUMN public.ucenter_base_product.create_time IS '#创建时间#';
COMMENT ON COLUMN public.ucenter_base_product.modifier IS '#修改人#';
COMMENT ON COLUMN public.ucenter_base_product.update_time IS '#修改时间#';
COMMENT ON COLUMN public.ucenter_base_product.delete_flag IS '#逻辑删除标识#';
COMMENT ON COLUMN public.ucenter_base_product.product_code IS '#code编码#';
COMMENT ON COLUMN public.ucenter_base_product.product_business_code IS '#业务code编码#';
COMMENT ON COLUMN public.ucenter_base_product.release_status IS '#发布状态 0:未发布 1:已发布 #';
COMMENT ON COLUMN public.ucenter_base_product.product_version IS '设备型号的版本号';
COMMENT ON COLUMN public.ucenter_base_product.publish_flag IS '是否已经发布过了';
COMMENT ON COLUMN public.ucenter_base_product.product_publish_status IS 'draft 草稿；active 发布';
COMMENT ON COLUMN public.ucenter_base_product.topology_id IS '总线拓扑结构id';
COMMENT ON COLUMN public.ucenter_base_product.description IS '描述信息';
COMMENT ON COLUMN public.ucenter_base_product.topology_version IS '总线拓扑结构版本';
COMMENT ON COLUMN public.ucenter_base_product.has_published IS '是否发布过 0 已发布过 1未发布过';
COMMENT ON COLUMN public.ucenter_base_product.product_name_en IS '#分类名称英文名#';


--
-- Name: ucenter_base_user; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ucenter_base_user (
    id bigint NOT NULL,
    tenant_id character varying(100) NOT NULL,
    dept_id character varying(100) NOT NULL,
    account character varying(18),
    user_name character varying(64),
    nick_name character varying(32),
    phone character varying(18),
    sex character varying(2),
    email character varying(64),
    creator character varying(130),
    create_time timestamp(0) without time zone,
    modifier character varying(130),
    update_time timestamp(0) without time zone,
    delete_flag integer
);


--
-- Name: TABLE ucenter_base_user; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.ucenter_base_user IS '用户表';
COMMENT ON COLUMN public.ucenter_base_user.id IS '#主键id#';
COMMENT ON COLUMN public.ucenter_base_user.tenant_id IS '#租户id#';
COMMENT ON COLUMN public.ucenter_base_user.dept_id IS '#部门id#';
COMMENT ON COLUMN public.ucenter_base_user.account IS '#账号#';
COMMENT ON COLUMN public.ucenter_base_user.user_name IS '#用户姓名#';
COMMENT ON COLUMN public.ucenter_base_user.nick_name IS '#昵称#';
COMMENT ON COLUMN public.ucenter_base_user.phone IS '#手机号#';
COMMENT ON COLUMN public.ucenter_base_user.sex IS '#性别#';
COMMENT ON COLUMN public.ucenter_base_user.email IS '#邮箱#';
COMMENT ON COLUMN public.ucenter_base_user.creator IS '#创建人#';
COMMENT ON COLUMN public.ucenter_base_user.create_time IS '#创建时间#';
COMMENT ON COLUMN public.ucenter_base_user.modifier IS '#修改人#';
COMMENT ON COLUMN public.ucenter_base_user.update_time IS '#修改时间#';
COMMENT ON COLUMN public.ucenter_base_user.delete_flag IS '#逻辑删除标识#';


--
-- Name: ucenter_base_user_dept_rel; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ucenter_base_user_dept_rel (
    id bigint NOT NULL,
    user_id character varying(100),
    dept_id character varying(100),
    version integer,
    creator character varying(130),
    create_time timestamp(0) without time zone,
    modifier character varying(130),
    update_time timestamp(0) without time zone,
    delete_flag integer
);


--
-- Name: TABLE ucenter_base_user_dept_rel; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.ucenter_base_user_dept_rel IS '部门-用户-关系';
COMMENT ON COLUMN public.ucenter_base_user_dept_rel.id IS '#主键id#';
COMMENT ON COLUMN public.ucenter_base_user_dept_rel.user_id IS '#用户id#';
COMMENT ON COLUMN public.ucenter_base_user_dept_rel.dept_id IS '#部门id#';
COMMENT ON COLUMN public.ucenter_base_user_dept_rel.version IS '#数据版本#';
COMMENT ON COLUMN public.ucenter_base_user_dept_rel.creator IS '#创建人#';
COMMENT ON COLUMN public.ucenter_base_user_dept_rel.create_time IS '#创建时间#';
COMMENT ON COLUMN public.ucenter_base_user_dept_rel.modifier IS '#修改人#';
COMMENT ON COLUMN public.ucenter_base_user_dept_rel.update_time IS '#修改时间#';
COMMENT ON COLUMN public.ucenter_base_user_dept_rel.delete_flag IS '#逻辑删除标识#';


--
-- Name: ucenter_operation_log; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ucenter_operation_log (
    id bigint NOT NULL,
    log_time timestamp(6) without time zone NOT NULL,
    log_operation_module character varying(100) NOT NULL,
    log_operation_type character varying(30) NOT NULL,
    log_content character varying(4000) NOT NULL,
    operation_result character varying(500) NOT NULL,
    operation_operator character varying(50) NOT NULL,
    operation_ip character varying(15) NOT NULL,
    creator character varying(25) NOT NULL,
    create_time timestamp(6) without time zone NOT NULL,
    version integer,
    modifier character varying(25),
    update_time timestamp(6) without time zone,
    delete_flag smallint,
    service_id character varying(32),
    log_detail character varying(4000),
    synced boolean DEFAULT false,
    user_id character varying(100),
    client_id character varying(100),
    tenant_id character varying(100),
    organization_id character varying(100),
    equipment_type_id character varying(100)
);


--
-- Name: TABLE ucenter_operation_log; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.ucenter_operation_log IS '操作日志表';
COMMENT ON COLUMN public.ucenter_operation_log.id IS '#主键id#';
COMMENT ON COLUMN public.ucenter_operation_log.log_time IS '#日志时间#';
COMMENT ON COLUMN public.ucenter_operation_log.log_operation_module IS '#操作模块#';
COMMENT ON COLUMN public.ucenter_operation_log.log_operation_type IS '#操作类型#';
COMMENT ON COLUMN public.ucenter_operation_log.log_content IS '#操作内容#';
COMMENT ON COLUMN public.ucenter_operation_log.operation_result IS '#操作结果#';
COMMENT ON COLUMN public.ucenter_operation_log.operation_operator IS '#操作人#';
COMMENT ON COLUMN public.ucenter_operation_log.operation_ip IS '#IP地址#';
COMMENT ON COLUMN public.ucenter_operation_log.creator IS '#记录创建人#';
COMMENT ON COLUMN public.ucenter_operation_log.create_time IS '#记录创建时间#';
COMMENT ON COLUMN public.ucenter_operation_log.version IS '#记录版本#';
COMMENT ON COLUMN public.ucenter_operation_log.modifier IS '#记录修改人#';
COMMENT ON COLUMN public.ucenter_operation_log.update_time IS '#记录修改时间#';
COMMENT ON COLUMN public.ucenter_operation_log.delete_flag IS '#记录删除标志#ENUM#0:正常:normal,1:删除:delete#';
COMMENT ON COLUMN public.ucenter_operation_log.service_id IS '#业务id#';
COMMENT ON COLUMN public.ucenter_operation_log.log_detail IS '#日志详情#';
COMMENT ON COLUMN public.ucenter_operation_log.synced IS '#同步到日志平台#ENUM#true:已同步:synced,false:未同步:unsynced#';
COMMENT ON COLUMN public.ucenter_operation_log.user_id IS '#用户id#';
COMMENT ON COLUMN public.ucenter_operation_log.client_id IS '#应用客户端id#';
COMMENT ON COLUMN public.ucenter_operation_log.tenant_id IS '#租户id#';
COMMENT ON COLUMN public.ucenter_operation_log.organization_id IS '#组织id#';


--
-- Name: ucenter_base_user pk_ucenter_base_user; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ucenter_base_user
    ADD CONSTRAINT pk_ucenter_base_user PRIMARY KEY (id);

--
-- Name: ucenter_operation_log pk_ucenter_operation_log; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ucenter_operation_log
    ADD CONSTRAINT pk_ucenter_operation_log PRIMARY KEY (id);


--
-- Name: ucenter_base_product pk_ucenter_product; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ucenter_base_product
    ADD CONSTRAINT pk_ucenter_product PRIMARY KEY (id);


--
-- Name: ucenter_base_user_dept_rel pk_ucenter_user_dept_rel; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ucenter_base_user_dept_rel
    ADD CONSTRAINT pk_ucenter_user_dept_rel PRIMARY KEY (id);

--
-- Name: idx_ucenter_operation_log_oper_cont; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_ucenter_operation_log_oper_cont ON public.ucenter_operation_log USING btree (log_content);


--
-- Name: index_product_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_product_name ON public.ucenter_base_product USING btree (product_name);
