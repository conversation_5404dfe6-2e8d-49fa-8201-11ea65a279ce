\c ids

-- Date: 2021-09-01 10:44:44
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015900, 0, '其他工程专用机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 14, 'e8d1ad2a32974c04', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015865, 0, '军用工程机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 8, 'e8d1ad2a32974d98', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Military engineering machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015886, 0, '凿岩机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 21, 'e8d1ad2a32974d66', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Rock drilling machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015841, 0, '压实机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 4, 'e8d1ad2a32974d68', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Compaction machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015879, 0, '工程机械专用零部件', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 13, 'e8d1ad2a32974d62', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Specialized parts for engineering machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015817, 0, '工程起重机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 2, 'e8d1ad2a32974d97', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Engineering lifting machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015810, 0, '挖掘机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 1, 'e8d1ad2a32974d96', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Excavator');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015875, 0, '掘进机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 11, 'e8d1ad2a32974d60', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Tunneling machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015859, 0, '桩工机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 7, 'e8d1ad2a32974d71', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Processing Machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015866, 0, '消防装备机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 9, 'e8d1ad2a32974d99', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Firefighting equipment machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015827, 0, '混凝土机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 3, 'e8d1ad2a32974d67', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Concrete machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015848, 0, '港口机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 6, 'e8d1ad2a32974d70', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Port machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015873, 0, '环保机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 10, 'e8d1ad2a32974d59', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Environmental protection machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015899, 0, '电动化机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 17, 'e8d1ad2a32974c03', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Electrified machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015887, 0, '电梯及扶梯', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 22, 'e8d1ad2a32974c01', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Elevators and escalators');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015888, 0, '石油机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 23, 'e8d1ad2a32974c02', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Oil machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015914, 0, '矿山机械', 1, 1, 0, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 24, 'e8d1ad2a32974c05', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Mining machine');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015882, 0, '筑工机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 20, 'e8d1ad2a32974d65', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Processing machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015880, 0, '装修机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 18, 'e8d1ad2a32974d63', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Construction machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015842, 0, '路面机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 5, 'e8d1ad2a32974d69', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Road machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015881, 0, '钢筋及预应力机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 19, 'e8d1ad2a32974d64', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Reinforcement and prestressing machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015878, 0, '高空作业机械', 1, 1, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, 12, 'e8d1ad2a32974d61', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Aerial work machinery');
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015869, 1501750874561015866, '专勤类消防车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d49', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015813, 1501750874561015810, '中挖', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d03', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015871, 1501750874561015866, '举高喷射消防车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d51', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015897, 1501750874561015888, '井口自动化', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d81', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015892, 1501750874561015888, '仪表设备', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d76', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015854, 1501750874561015848, '伸缩臂叉车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d38', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015822, 1501750874561015817, '伸缩臂履带起重机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d11', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015836, 1501750874561015827, '充填泵', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d23', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015820, 1501750874561015817, '全地面起重机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d09', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015910, 1501750874561015900, '刮板机系列产品', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d92', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015826, 1501750874561015817, '剪叉式高空作业车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d15', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015901, 1501750874561015900, '半挂牵引车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d83', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015867, 1501750874561015866, '压缩空气泡沫消防车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d47', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015894, 1501750874561015888, '压裂泵及配件', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d78', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015889, 1501750874561015888, '压裂设备', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d73', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015844, 1501750874561015842, '压路机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d29', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015851, 1501750874561015848, '叉车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d35', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015862, 1501750874561015859, '双轮铣槽机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d45', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015824, 1501750874561015817, '多功能履带起重机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d13', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015864, 1501750874561015859, '多功能旋挖钻机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d46', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015812, 1501750874561015810, '大挖', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d02', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015907, 1501750874561015900, '宽体自卸车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d89', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015811, 1501750874561015810, '小挖', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d01', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015819, 1501750874561015817, '尖头塔式起重机1', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', '霍力强(gw_huolq)', '2022-09-20 18:41:47', 0, NULL, 'e8d1ad2a32974d08', 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015855, 1501750874561015848, '岸边集装箱起重机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d39', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015877, 1501750874561015875, '工程掘进机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d55', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015913, 1501750874561015900, '巷道修复机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d95', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015837, 1501750874561015827, '布料杆', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d24', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015840, 1501750874561015827, '干混砂浆搅拌站', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d27', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015845, 1501750874561015842, '平地机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d30', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015818, 1501750874561015817, '平头塔式起重机1', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', '霍力强(gw_huolq)', '2022-09-20 18:41:55', 0, NULL, 'e8d1ad2a32974d07', 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015870, 1501750874561015866, '战勤保障类消防车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d50', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015852, 1501750874561015848, '抓钢（料）机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d36', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015833, 1501750874561015827, '拖泵', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d20', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015876, 1501750874561015875, '掘进机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', '叶家彬(yejb2)', '2023-06-09 16:33:55', 0, NULL, 'e8d1ad2a32974d54', 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015829, 1501750874561015827, '搅拌车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d17', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015843, 1501750874561015842, '摊铺机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d28', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015839, 1501750874561015827, '新能源搅拌车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d26', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015909, 1501750874561015900, '新能源自卸车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d91', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015860, 1501750874561015859, '旋挖钻机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d43', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015868, 1501750874561015866, '无人机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d48', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015885, 1501750874561015882, '智能钢筋装备', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d58', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015904, 1501750874561015900, '机械传动矿用自卸车(刚性矿卡)', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d86', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015821, 1501750874561015817, '桁架臂履带起重机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d10', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015849, 1501750874561015848, '正面吊', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d33', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015823, 1501750874561015817, '汽车起重机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d12', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015847, 1501750874561015842, '沥青搅拌站', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d32', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015828, 1501750874561015827, '泵车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015912, 1501750874561015900, '液压支架', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d94', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015896, 1501750874561015888, '液氮泵设备', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d80', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015830, 1501750874561015827, '混凝土搅拌站', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d18', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015891, 1501750874561015888, '混砂设备', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d75', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015893, 1501750874561015888, '混配设备', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d77', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015835, 1501750874561015827, '湿喷机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d22', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015903, 1501750874561015900, '燃气车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d85', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015906, 1501750874561015900, '电传动矿用自卸车(电动轮)', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d88', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015890, 1501750874561015888, '电驱压裂设备', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d74', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015872, 1501750874561015866, '登高平台消防车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d52', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015838, 1501750874561015827, '砂浆专用搅拌站', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d25', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015834, 1501750874561015827, '砂浆泵', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d21', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015832, 1501750874561015827, '砂浆/混凝土双用搅拌站', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d72', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015884, 1501750874561015882, '移动反击式破碎机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d57', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015853, 1501750874561015848, '纯电动港口牵引车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d37', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015908, 1501750874561015900, '自卸车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d90', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015874, 1501750874561015873, '蒸气压缩机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d53', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015815, 1501750874561015810, '装载机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d05', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015831, 1501750874561015827, '车载泵', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d19', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015856, 1501750874561015848, '轨道式集装箱门式起重机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d40', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015814, 1501750874561015810, '轮式挖掘机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d04', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015857, 1501750874561015848, '轮胎式集装箱门式起重机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d41', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015902, 1501750874561015900, '载货车', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d84', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015861, 1501750874561015859, '连续墙抓斗', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d44', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015895, 1501750874561015888, '连续油管设备', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d79', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015816, 1501750874561015810, '迷你挖', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d06', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015911, 1501750874561015900, '采煤机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d93', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015846, 1501750874561015842, '铣刨机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d31', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015905, 1501750874561015900, '铰接式矿用自卸车(铰接车)', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d87', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015858, 1501750874561015848, '门座式起重机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d42', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015825, 1501750874561015817, '随车起重机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d14', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015850, 1501750874561015848, '集装箱空箱堆高机', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d34', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015883, 1501750874561015882, '预制件专用运输', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d56', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ucenter_base_product" ("id", "parent_id", "product_name", "product_status", "product_type", "version", "creator", "create_time", "modifier", "update_time", "delete_flag", "product_code", "product_business_code", "release_status", "product_version", "publish_flag", "product_publish_status", "topology_id", "description", "topology_version", "has_published", "product_name_en") VALUES (1501750874561015898, 1501750874561015888, '高压管汇', 1, 2, NULL, '系统默认', '2022-03-10 10:44:44', NULL, NULL, 0, NULL, 'e8d1ad2a32974d82', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
