
-- 新增商用车设备类型数据
INSERT INTO public.ucenter_base_product (id,parent_id,product_name,product_status,product_type,
creator,create_time,delete_flag,product_code,product_business_code,description,product_name_en)
SELECT (select max(id)+1 from ucenter_base_product),0,'商用车',1,1,
'系统默认',now(),0,25,'4859ba2584874d83','新增机械大类下的商用车设备类型,与工程起重机械同层级','Business car'
WHERE NOT EXISTS (
    SELECT 1 FROM public.ucenter_base_product WHERE public.ucenter_base_product.product_name='商用车' and
	public.ucenter_base_product.product_business_code='4859ba2584874d83'
);