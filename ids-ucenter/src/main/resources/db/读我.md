### 说明
###### 项目使用Flyway管理数据库版本，项目启动时Flyway会自动执行db/migration目录下的sql文件。

### 数据库版本管理流程
###### 1，开发环境（测试环境）不使用Flyway管理数据库，开发人员自行执行sql文件，验证功能。
###### 2，标品环境、非功能环境、预发环境、生产环境使用Flyway自动执行sql文件，更新数据库。

### 目录说明
###### src/main/resources/db/migration：Flyway管理的sql文件目录，项目启动时，这里的sql会被自动执行（没执行的情况下）。
###### src/main/resources/db/history：SQL文件收集目录，开发时可先将sql文件放在此目录下，待功能完成验收后，将sql文件拷贝到migration目录下。

### Flyway的sql文件命名约定
###### 格式：V<版本号>__<描述性名称>.sql
###### <版本号>: 迁移脚本的版本号，主版本号目前始终为1，次版本号为sprint号，修订号每个sprint从1开始递增（始终占用3个字符，不够时前面补0）。
###### __ (双下划线): 用于分隔版本号和描述性名称。
###### <描述性名称>: 描述性的名称，通常用下划线或其他合适的字符分隔单词，能简短表达sql干的事情即可。

### 文件名命名约定举例说明
V1_107_001__add_site_country.sql
###### 该文件为sprint107的第一个数据库变更脚本，描述性名称为add_site_country：表示该sql会增加站点国家表。

### 注意事项
###### 1，src/main/resources/db/migration中的文件不能出现删除表、修改表名、删除字段，修改字段名，修改字段类型、改小字段长度的SQL。
###### 2，src/main/resources/db/migration中的文件一旦发到预发/生产环境，就不准删除、修改。
###### 3，发布失败时回滚服务版本、环境变量，不回滚数据库，在失败的版本上处理好问题后生成新版本继续发布。




