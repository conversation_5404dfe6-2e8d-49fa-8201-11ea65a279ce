server:
  port: 80
  max-http-header-size: 20480
  compression:
    enabled: true

spring:
  application:
    name: ids-ucenter
  datasource:
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    url: jdbc:postgresql://${POSTGRESQL_IP}:${POSTGRESQL_PORT}/ids
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10 #最大连接数,默认值10.
      minimum-idle: 5 #最小空闲连接，默认值10.
      connection-timeout: 60000 #连接超时时间(毫秒),默认值30秒.
      idle-timeout: 600000
      max-lifetime: 1500000 #连接最大存活时间,默认值30分钟.设置应该比mysql设置的超时时间短
      connection-test-query: select 1 #连接测试
  #  redis:
  #    database: 0
  #    host: ${REDIS_HOST}
  #    port: ${REDIS_PORT}
  #    timeout: 4s
  #    password: ${REDIS_PASSWORD}
  #    lettuce:
  #      pool:
  #        # 连接池中的最大空闲连接 默认8
  #        max-idle: 8
  #        # 连接池中的最小空闲连接 默认0
  #        min-idle: 0
  #        # 连接池最大连接数 默认8 ，负数表示没有限制
  #        max-active: 8
  #        # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认-1
  #        max-wait: -1
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  flyway:
    #启用或者禁用flyway
    enabled: true
    #flyway的clean命令会删除指定的schema下的所有表，生产务必禁用
    clean-disabled: true
    #SQL脚本目录，多个目录,分割
    #locations: classpath:migration
    #metadata 版本控制信息表
    table: flyway_schema_history
    # 如果没有 flyway_schema_history 这个 metadata 表， 在执行 flyway migrate 命令之前, 必须先执行 flyway baseline 命令
    # 设置为 true 后 flyway 将在需要 baseline 的时候, 自动执行一次 baseline。
    baseline-on-migrate: true
    # 指定 baseline 的版本号,默认值为 1, 低于该版本号的 SQL 文件, migrate 时会被忽略
    baseline-version: 1
    # 字符编码 默认 UTF-8
    encoding: UTF-8
    # 是否允许不按顺序迁移 开发建议 true  生产建议 false
    out-of-order: false
    # 需要 flyway 管控的 schema list,这里我们配置为flyway  缺省的话, 使用spring.datasource.url 配置的那个 schema,
    # 可以指定多个schema, 但仅会在第一个schema下建立 metadata 表, 也仅在第一个schema应用migration sql 脚本.
    # 但flyway Clean 命令会依次在这些schema下都执行一遍. 所以 确保生产 spring.flyway.clean-disabled 为 true
    #schemas: education_portal
    # 执行迁移时是否自动调用验证   当你的 版本不符合逻辑 比如 你先执行了 DML 而没有 对应的DDL 会抛出异常
    validate-on-migrate: true
    # 不替换占位符
    placeholder-replacement: false

mybatis-plus:
  mapper-locations: classpath*:mapper/**/**.xml
  #实体扫描，多个package用逗号或者分号分隔
  #  typeAliasesPackage: com.pm.facade.*.entity
  #  typeEnumsPackage: com.baomidou.springboot.db.entity.enums
  global-config:
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
      id-type: AUTO
      #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
      field-strategy: not_empty
      #驼峰下划线转换
      column-underline: true
      #数据库大写下划线转换
      #capital-mode: true
      #逻辑删除配置
      logic-delete-field: delete_flag
      db-type: mysql
      #刷新mapper 调试神器
      refresh: false
    #自定义填充策略接口实现
    #meta-object-handler: com.baomidou.springboot.xxx
    #自定义SQL注入器
  #sql-injector: com.baomidou.springboot.xxx
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    map-underscore-to-camel-case: true
    cache-enabled: false
  type-enums-package: com.rootcloud.ids.ucenter.enums;com.rootcloud.esmp.common.enums

pagehelper:
  reasonable: true # 禁用合理化时
  support-methods-arguments: true
  params: count=countSql
  row-bounds-with-count: true
  helper-dialect: mysql


os:
  iam:
    domain: ${OS_IAM_DOMAIN}
    superToken: ${OS_IAM_SUPERTOKEN}
    clientId: web_app
    clientSecret: changeit
    authorization-server-address: ${IAM_AUTHORIZATION_SERVER_ADDRESS:http://iam-authorization-service}
    ## 为什么要分开？是因为联邦环境下，只能在集团环境注册权限
    resource-register-address: ${IAM_RESOURCE_REGISTER_ADDRESS:http://iam-authorization-service}
  gateway:
    domain: http://ids-gateway
#定时任务的配置
xxl:
  job:
    admin:
      addresses: ${XXL_JOB_ADMIN_ADDRESSES}
    accessToken:
    executor:
      appname: ids-ucenter
      address:
      ip:
      port: ${XXL_JOB_ADMIN_PORT}
      logpath:
      logretentiondays: 30
    switch: true

app:
  log:
    domain: ${APP_LOG_DOMAIN:http://logging-service.rootcloud-v4}  # http://logging-service.rootcloud-v4
    uri: ${APP_LOG_URI:/api/v1/logging} # /api/v1/logging
  baseProduct:
    domain: http://ids-ucenter
    uri: /api/base/user/listPossess
# 是否过滤未发布的设备型号 true过滤
equipmentModel:
  unPublishedExclude: true


i18n:
  language: ${DEFAULT_LANGUAGE:zh-CN}