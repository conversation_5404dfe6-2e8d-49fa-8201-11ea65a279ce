package com.rootcloud.ids.ucenter.utils.convertor.base;

import com.rootcloud.ids.ucenter.entity.base.BaseDeptProRel;
import com.rootcloud.ids.ucenter.enums.base.AuthTypeEnum;
import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;

/**
 * <AUTHOR>
 */
public class BaseDeptProRelRespConvertor {

    public static BaseDeptProRel change2Entity(String deptId, Long productId, DeptTypeEnum deptType, AuthTypeEnum authType,String cid, String ownerCid) {
        BaseDeptProRel entity = new BaseDeptProRel();
        entity.setDeptId(deptId);
        entity.setProductId(productId);
        entity.setDeptType(deptType);
        entity.setAuthType(authType);
        entity.setCid(cid);
        entity.setOwnerCid(ownerCid);
        return entity;
    }
}
