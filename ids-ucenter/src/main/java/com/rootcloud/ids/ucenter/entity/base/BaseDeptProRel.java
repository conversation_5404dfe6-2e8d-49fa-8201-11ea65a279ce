package com.rootcloud.ids.ucenter.entity.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rootcloud.ids.common.core.base.BaseEntity;
import com.rootcloud.ids.ucenter.enums.base.AuthTypeEnum;
import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 产品分类-部门-关系
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ucenter_base_dept_pro_rel")
@ApiModel(value = "BaseDeptProRel对象", description = "产品分类-部门-关系")
public class BaseDeptProRel extends BaseEntity {

    @ApiModelProperty("#主键id#")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("#部门id#")
    private String deptId;

    @ApiModelProperty("#机型id#")
    private Long productId;

    @ApiModelProperty("#部门类型#")
    private DeptTypeEnum deptType;

    @ApiModelProperty("#权限类型#ENUM#1:所有权:OWNER,2:使用权:USE#")
    private AuthTypeEnum authType;

    @ApiModelProperty("#当前部门所属租户id#")
    private String cid;

    @ApiModelProperty("#所有权租户id#")
    private String ownerCid;
}
