package com.rootcloud.ids.ucenter.service.backup.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.rootcloud.ids.ucenter.dto.backup.SysMenuCondition;
import com.rootcloud.ids.ucenter.dto.backup.SysMenuSaveDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysMenuUpdateDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterMenu;
import com.rootcloud.ids.ucenter.dao.backup.UcenterMenuMapper;
import com.rootcloud.ids.ucenter.service.backup.IUcenterMenuService;
import com.rootcloud.ids.ucenter.vo.backup.SysMenuQueryVO;
import com.rootcloud.ids.ucenter.vo.backup.SysMenuVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 系统菜单服务实现类
 * @ClassName SysMenuServiceImpl
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Service
public class UcenterMenuServiceImpl extends ServiceImpl<UcenterMenuMapper, UcenterMenu> implements
    IUcenterMenuService {

    @Override
    public Long save(SysMenuSaveDTO menuSaveDTO) {
        UcenterMenu menu = new UcenterMenu();
        BeanUtil.copyProperties(menuSaveDTO, menu);
        this.save(menu);
        return menu.getMenuId();
    }

    @Override
    public Boolean update(SysMenuUpdateDTO menuUpdateDTO) {
        UcenterMenu menu = new UcenterMenu();
        BeanUtil.copyProperties(menuUpdateDTO, menu);
        return this.updateById(menu);
    }

    @Override
    public Boolean batchDelete(Long[] menuIds) {
        return this.removeByIds(Convert.toList(Long.class, menuIds));
    }

    @Override
    public SysMenuVO findById(Long menuId) {
        SysMenuVO menuVO = new SysMenuVO();
        UcenterMenu menu = this.getById(menuId);
        BeanUtil.copyProperties(menu, menuVO);
        return menuVO;
    }

    @Override
    public PageInfo<SysMenuQueryVO> query(SysMenuCondition cnd) {
        PageHelper.startPage(cnd.getPage(), cnd.getPageSize());
        List<SysMenuQueryVO> menuVOList = baseMapper.query(cnd);
        PageInfo<SysMenuQueryVO> pageInfo = new PageInfo(menuVOList);
        return pageInfo;
    }

    @Override
    public List<SysMenuVO> getChilds(Long menuId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("parentId", menuId);
        List<UcenterMenu> menuList = this.list(queryWrapper);
        List<SysMenuVO> menuVOList = new ArrayList<>();
        BeanUtil.copyProperties(menuList, menuVOList);
        return menuVOList;
    }

    @Override
    public List<SysMenuVO> getAllChilds(Long menuId) {
        List<SysMenuVO> result = new ArrayList();
        result = getAllChilds(menuId, result);
        return result;
    }

    @Override
    public List<SysMenuVO> findMenuByUserId(Long userId) {
        return baseMapper.findMenuByUserId(userId);
    }

    private List<SysMenuVO> getAllChilds(Long menuId, List<SysMenuVO> result) {
        List<SysMenuVO> list = getChilds(menuId);
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                result.add(list.get(i));
                getAllChilds(list.get(i).getMenuId(), result);
            }
        }
        return result;
    }

}
