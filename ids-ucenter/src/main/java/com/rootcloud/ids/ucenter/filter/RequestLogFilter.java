package com.rootcloud.ids.ucenter.filter;

import static com.rootcloud.ids.common.core.constant.GlobalConstants.API_REQUEST_ID_HEADER;
import static com.rootcloud.ids.common.core.constant.GlobalConstants.LOG_REQUEST_ID;

import cn.hutool.http.Method;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.rootcloud.esmp.common.log.HttpServletRequestBodyWrapper;
import com.rootcloud.esmp.common.log.HttpServletResponseBodyWrapper;
import com.rootcloud.esmp.common.log.LoggingLevel;
import com.rootcloud.esmp.common.log.MetricUtil;
import java.io.IOException;
import java.util.Optional;
import java.util.UUID;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@WebFilter(filterName = "requestLogFilter", urlPatterns = "/*")
public class RequestLogFilter implements Filter {

  private boolean isJson(HttpServletRequest request) {
    if (request.getContentType() != null) {
      return request.getContentType().equals(MediaType.APPLICATION_JSON_VALUE) ||
          request.getContentType().equals(MediaType.APPLICATION_JSON_UTF8_VALUE);
    }
    return false;
  }

  private long beforeRequest(HttpServletRequest request) {
    long startTime = System.currentTimeMillis();
    try {
      String requestId = request.getHeader(API_REQUEST_ID_HEADER);
      if (StringUtils.isBlank(requestId)) {
        requestId = UUID.randomUUID().toString().replaceAll("-", "");
      }
      MDC.put(LOG_REQUEST_ID, requestId);
      LoggingLevel.INFO.log(log, MetricUtil.beforeRequest(request, startTime));
    } catch (Exception ex) {
      log.error("输出请求日志失败: " + ex.getMessage(), ex);
    }
    return startTime;
  }

  private void afterRequest(long startTime, HttpServletRequest request, HttpServletResponse response, Exception ex) {
    try {
      long endTime = System.currentTimeMillis();
      Optional.ofNullable(MDC.get(LOG_REQUEST_ID))
          .ifPresent(requestId -> response.addHeader(API_REQUEST_ID_HEADER, requestId));
      LoggingLevel.INFO
          .log(log, MetricUtil.afterRequest(request, response, startTime, endTime, ex));
    } catch (Exception e) {
      log.error("输出响应日志失败: " + e.getMessage(), e);
    } finally {
      MDC.remove(LOG_REQUEST_ID);
    }
  }

  @Override
  public void doFilter(ServletRequest request, ServletResponse servletResponse,
      FilterChain filterChain) throws IOException, ServletException {
    HttpServletRequest httpServletRequest = (HttpServletRequest) request;
    HttpServletResponse httpServletResponse = (HttpServletResponse) servletResponse;

    if (isJson(httpServletRequest)) {
      String methodType = httpServletRequest.getMethod();
      if (Method.POST.name().equals(methodType)) {
        httpServletRequest = new HttpServletRequestBodyWrapper(httpServletRequest);
      }
      //httpServletResponse = new HttpServletResponseBodyWrapper(httpServletResponse);
    }

    long startTime = beforeRequest(httpServletRequest);
    Exception error = null;
    try {
      filterChain.doFilter(httpServletRequest, httpServletResponse);
    } catch (Exception ex) {
      error = ex;
      throw ex;
    } finally {
      afterRequest(startTime, httpServletRequest, httpServletResponse, error);
    }
  }
}
