/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.ids.ucenter.service.auth;

import com.rootcloud.esmp.iam.dto.permission.IamResourcePropertyValueDto;
import com.rootcloud.esmp.iam.dto.permission.IamResourcePropertyValueQueryDto;
import com.rootcloud.esmp.iam.dto.permission.PagingResult;
import com.rootcloud.esmp.common.enums.ProductTypeEnum;
import java.util.List;

public interface IIamAuthorizationService {

  PagingResult<List<IamResourcePropertyValueDto>> permissionTree(ProductTypeEnum productType, IamResourcePropertyValueQueryDto param);

  PagingResult<List<IamResourcePropertyValueDto>> permissionValues(IamResourcePropertyValueQueryDto param);
}
