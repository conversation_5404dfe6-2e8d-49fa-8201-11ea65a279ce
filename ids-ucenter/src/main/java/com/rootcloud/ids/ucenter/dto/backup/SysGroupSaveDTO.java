package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * @Description 组织保存DTO
 * @ClassName SysGroupSaveDTO
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "组织保存DTO")
@Getter
@Setter
public class SysGroupSaveDTO extends BaseDTO {

    @ApiModelProperty(value = "组织名称")
    @NotBlank(message = "组织名称不能为空")
    private String groupName;

    @ApiModelProperty(value = "英文名称")
    @NotBlank(message = "英文名称不能为空")
    private String englishName;

    @ApiModelProperty(value = "备注")
    private String remark;

}
