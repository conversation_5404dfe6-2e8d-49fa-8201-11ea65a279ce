package com.rootcloud.ids.ucenter.vo.rest.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: kang.ling
 * @create: 2022-03-28 16:28
 **/
@Data
@ApiModel(value = "BaseUserEquipmentRemoveRest", description = "移除权限参数")
public class BaseUserEquipmentRemoveRest {

    @ApiModelProperty("#组织ID#")
    @NotBlank(message = "组织id不能为空")
    private String cid;

    @ApiModelProperty("#用户id#")
    @NotNull(message = "用户id不能为空")
    private String userId;

    @ApiModelProperty("#机型id#")
    @NotNull(message = "机型id不能为空")
    private Long equipmentId;
}
