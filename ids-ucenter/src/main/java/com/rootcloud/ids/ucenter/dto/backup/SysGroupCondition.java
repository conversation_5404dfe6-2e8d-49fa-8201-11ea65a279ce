package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 组织查询条件
 * @ClassName SysGroupCondition
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "组织查询条件")
@Getter
@Setter
public class SysGroupCondition extends BasePageQuery {

    @ApiModelProperty(value = "组织名称")
    private String groupName;

    @ApiModelProperty(value = "英文名称")
    private String englishName;

}
