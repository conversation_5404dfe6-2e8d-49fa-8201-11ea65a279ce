package com.rootcloud.ids.ucenter.utils;

import cn.hutool.core.collection.CollUtil;
import com.rootcloud.esmp.iam.client.IamDepartmentClient;
import com.rootcloud.esmp.common.dto.iam.department.DepartmentsDTO;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDepartmentResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseDeptEquipmentDelRest;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class IamUtils {

    /**
     * 获取组织下所有部门信息
     */
    public static List<BaseDepartmentResp> getOrgDept(List<BaseDepartmentResp> departmentResps) {
        List<BaseDepartmentResp> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(departmentResps)) {
            list.addAll(departmentResps);
            getSubDepartCode(departmentResps, list);
        }
        return list;
    }

    private static void getSubDepartCode(List<BaseDepartmentResp> departCodeList, List<BaseDepartmentResp> list) {
        for (BaseDepartmentResp departCode : departCodeList) {
            List<BaseDepartmentResp> subList = departCode.getChildren();
            if (CollUtil.isNotEmpty(subList)) {
                getSubDepartCode(subList, list);
            }
            Optional.ofNullable(subList).ifPresent(list::addAll);
        }
    }

    /**
     * 获取部门信息
     */
    public static List<DepartmentsDTO> getDept(List<DepartmentsDTO> departmentResps) {
        List<DepartmentsDTO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(departmentResps)) {
            list.addAll(departmentResps);
            getSubDepart(departmentResps, list);
        }
        return list;
    }

    private static void getSubDepart(List<DepartmentsDTO> departList, List<DepartmentsDTO> list) {
        for (DepartmentsDTO depart : departList) {
            List<DepartmentsDTO> subList = depart.getChildren();
            if (CollUtil.isNotEmpty(subList)) {
                getSubDepart(subList, list);
            }
            Optional.ofNullable(subList).ifPresent(list::addAll);
        }
    }

    public static List<String> getSubDept(BaseDeptEquipmentDelRest param) {
        List<String> subDept = null;
        List<DepartmentsDTO> result = IamDepartmentClient.departments(true, param.getCid(), "{\"where\":{\"parents\":\"" + param.getDeptId() + "\"}}");
        if (CollUtil.isNotEmpty(result)) {
            List<DepartmentsDTO> subDeptList = getDept(result);
            if (CollUtil.isNotEmpty(subDeptList)) {
                subDept = subDeptList.stream().map(DepartmentsDTO::getId).collect(Collectors.toList());
            }
        }
        return subDept;
    }

    public static List<String> getSubDept(String cid) {
        List<String> subDept = null;
        List<DepartmentsDTO> result = IamDepartmentClient.departments(true, cid, null);
        if (CollUtil.isNotEmpty(result)) {
            List<DepartmentsDTO> subDeptList = getDept(result);
            if (CollUtil.isNotEmpty(subDeptList)) {
                subDept = subDeptList.stream().map(DepartmentsDTO::getId).collect(Collectors.toList());
            }
        }
        return subDept;
    }
}
