package com.rootcloud.ids.ucenter.utils.convertor.base;

import cn.hutool.core.collection.CollUtil;
import com.rootcloud.esmp.common.dto.iam.department.DepartmentsDTO;
import com.rootcloud.ids.ucenter.entity.base.BaseProduct;
import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDepartmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseEquipmentResp;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @since 2022/3/1 3:25 下午
 */
public class BaseDepartmentRespConvertor {

    public static List<BaseDepartmentResp> change2OrganizationRespList(Iterator<DepartmentsDTO> entityList) {
        List<BaseDepartmentResp> list = new ArrayList<>();
        if(entityList != null && entityList.hasNext()) {
            entityList.forEachRemaining(entity -> list.add(change2OrganizationResp(entity)));
        }
        return list;
    }

    private static BaseDepartmentResp change2OrganizationResp(DepartmentsDTO dto) {
        BaseDepartmentResp resp = null;
        if(dto != null) {
            resp = new BaseDepartmentResp();
            resp.setId(dto.getId());
            resp.setParentId(dto.getParent());
            resp.setName(dto.getName());
            resp.setCid(dto.getOrganization());
            resp.setDeptType(DeptTypeEnum.DEPARTMENT);
            if (CollUtil.isNotEmpty(dto.getChildren())){
                resp.setChildren(change2OrganizationRespList(dto.getChildren().listIterator()));
            }
        }
        return resp;
    }

    public static List<BaseEquipmentResp> change2ODeptRespList(Iterator<BaseProduct> entityList) {
        List<BaseEquipmentResp> list = new ArrayList<>();
        if(entityList != null && entityList.hasNext()) {
            entityList.forEachRemaining(entity -> list.add(change2DeptResp(entity)));
        }
        return list;
    }

    private static BaseEquipmentResp change2DeptResp(BaseProduct dto) {
        BaseEquipmentResp resp = null;
        if(dto != null) {
            resp = new BaseEquipmentResp();
            resp.setId(dto.getId());
            resp.setParentId(dto.getParentId());
            resp.setProductName(dto.getProductName());
            resp.setProductType(dto.getProductType());
            resp.setProductStatus(dto.getProductStatus());
        }
        return resp;
    }
}
