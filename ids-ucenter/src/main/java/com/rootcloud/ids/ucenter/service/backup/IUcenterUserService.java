package com.rootcloud.ids.ucenter.service.backup;

import com.rootcloud.ids.ucenter.dto.backup.SysUserCondition;
import com.rootcloud.ids.ucenter.dto.backup.SysUserResetPasswordDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysUserSaveDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysUserUpdateDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterUser;
import com.rootcloud.ids.ucenter.vo.backup.SysUserQueryVO;
import com.rootcloud.ids.ucenter.vo.backup.SysUserVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;

/**
 * @Description 系统用户服务接口类
 * @InterfaceName ISysUserService
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public interface IUcenterUserService extends IService<UcenterUser> {

    /**
     * 保存用户
     * @param userSaveDTO
     * @return
     */
    Long save(SysUserSaveDTO userSaveDTO);

    /**
     * 更新用户
     * @param userUpdateDTO
     * @return
     */
    Boolean update(SysUserUpdateDTO userUpdateDTO);

    /**
     * 根据ID查询用户
     * @param userId
     * @return
     */
    SysUserVO findById(Long userId);

    /**
     * 根据账户查询用户
     * @param accout
     * @return
     */
    SysUserVO findByAccount(String accout);

    /**
     * 批量删除
     * @param userIds
     */
    Boolean batchDelete(Long[] userIds);

    /**
     * 重置密码
     * @param userResetPasswordDTO
     * @return
     */
    Boolean resetPassword(SysUserResetPasswordDTO userResetPasswordDTO);

    /**
     * 条件查询
     * @param cnd
     * @return
     */
    PageInfo<SysUserQueryVO> query(SysUserCondition cnd);

}
