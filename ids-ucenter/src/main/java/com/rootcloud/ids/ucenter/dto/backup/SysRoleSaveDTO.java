package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * @Description 角色保存DTO对象
 * @ClassName SysRoleSaveDTO
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "角色保存DTO对象")
@Getter
@Setter
public class SysRoleSaveDTO extends BaseDTO {

    @ApiModelProperty(value = "角色名称")
    @NotBlank(message = "角色名称不能为空")
    private String roleName;

    @ApiModelProperty(value = "英文名称")
    @NotBlank(message = "英文名称不能为空")
    private String englishName;

    @ApiModelProperty(value = "备注")
    private String remark;
}
