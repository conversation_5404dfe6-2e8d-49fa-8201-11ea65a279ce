package com.rootcloud.ids.ucenter.service.backup.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.rootcloud.ids.ucenter.dto.backup.SysRoleCondition;
import com.rootcloud.ids.ucenter.dto.backup.SysRoleSaveDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysRoleUpdateDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterRole;
import com.rootcloud.ids.ucenter.dao.backup.UcenterRoleMapper;
import com.rootcloud.ids.ucenter.service.backup.IUcenterRoleService;
import com.rootcloud.ids.ucenter.vo.backup.SysRoleQueryVO;
import com.rootcloud.ids.ucenter.vo.backup.SysRoleVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 系统角色服务实现类
 * @ClassName SysRoleServiceImpl
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Service
public class UcenterRoleServiceImpl extends ServiceImpl<UcenterRoleMapper, UcenterRole> implements
    IUcenterRoleService {

    @Override
    public Long save(SysRoleSaveDTO roleSaveDTO) {
        UcenterRole role = new UcenterRole();
        BeanUtil.copyProperties(roleSaveDTO, role);
        this.save(role);
        return role.getRoleId();
    }

    @Override
    public Boolean update(SysRoleUpdateDTO roleUpdateDTO) {
        UcenterRole role = new UcenterRole();
        BeanUtil.copyProperties(roleUpdateDTO, role);
        return this.updateById(role);
    }

    @Override
    public SysRoleVO findById(Long roleId) {
        SysRoleVO roleVO = new SysRoleVO();
        UcenterRole role = this.getById(roleId);
        BeanUtil.copyProperties(role, roleVO);
        return roleVO;
    }

    @Override
    public Boolean batchDelete(Long[] roleIds) {
        return this.removeByIds(Convert.toList(Long.class, roleIds));
    }

    @Override
    public PageInfo<SysRoleQueryVO> query(SysRoleCondition cnd) {
        PageHelper.startPage(cnd.getPage(), cnd.getPageSize());
        List<SysRoleQueryVO> roleVOList = this.baseMapper.query(cnd);
        PageInfo<SysRoleQueryVO> pageInfo = new PageInfo(roleVOList);
        return pageInfo;
    }

}
