package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BaseDTO;
import com.rootcloud.ids.common.core.enums.UserCardTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @Description 用户更新DTO对象
 * @ClassName SysUserUpdateDTO
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "用户更新DTO对象")
@Getter
@Setter
public class SysUserUpdateDTO extends BaseDTO {

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "真实姓名")
    private String userName;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty(value = "电子邮箱")
    private String email;

    @ApiModelProperty(value = "头像链接")
    private String portrait;

    @ApiModelProperty(value = "证件类型（1：身份证，2：军官证，3：驾驶证）")
    private UserCardTypeEnum cardType;

    @ApiModelProperty(value = "证件号码")
    private String cardNo;

}
