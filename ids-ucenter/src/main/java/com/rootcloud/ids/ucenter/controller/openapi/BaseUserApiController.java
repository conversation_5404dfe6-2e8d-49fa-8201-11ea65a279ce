package com.rootcloud.ids.ucenter.controller.openapi;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: ling.kang
 * @create: 2022-03-01 10:08
 **/
@Api(value = "BaseUserApiController", tags = "openApi - 用户管理")
@RestController
@Slf4j
@RequestMapping("/api/open/user")
public class BaseUserApiController {

//    @Autowired
//    private IBaseUserService iBaseUserService;
//
//    @ApiOperation(value = "获取个人", notes = "产品分类(机械大类、机型、设备号)详情")
//    @GetMapping(value = "/getById")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "token", defaultValue = "", value = "token", required = true),
//    })
//    public Result<List<BaseProductApiResp>> listPossess(@RequestParam("token") String token) {
//        return Result.success(iBaseUserService.listPossess(token));
//    }

}
