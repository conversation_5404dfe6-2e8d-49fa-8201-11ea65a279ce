package com.rootcloud.ids.ucenter.service.dmp.impl;

import static com.rootcloud.esmp.common.Constants.LOCALE_HEADER;
import static com.rootcloud.ids.common.core.constant.GlobalConstants.API_REQUEST_ID_HEADER;
import static com.rootcloud.ids.common.core.constant.GlobalConstants.LOG_REQUEST_ID;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import com.rootcloud.ids.common.web.config.LanguageLocalConfig;
import com.rootcloud.ids.ucenter.entity.operationlog.OperationLog;
import com.rootcloud.ids.ucenter.enums.operationlog.UcenterOperationModuleEnum;
import com.rootcloud.ids.ucenter.enums.operationlog.UcenterOperationTypeEnum;
import com.rootcloud.ids.ucenter.service.dmp.IDmpJobService;
import com.rootcloud.ids.ucenter.service.operationlog.IOperationLogService;
import com.rootcloud.ids.ucenter.utils.Constants;
import java.util.Optional;
import java.util.UUID;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.List;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Service
public class DmpJobServiceImpl implements IDmpJobService {

  private Logger logger = LoggerFactory.getLogger(DmpJobServiceImpl.class);


  @Autowired
  private IOperationLogService operationLogService;

  @Override
  public void UcenterJobPushUcenterLogToLogging() {
    //查询未同步给日志平台的所有日志信息
    LambdaQueryWrapper<OperationLog> operationLogLambdaQueryWrapper = new LambdaQueryWrapper<>();
    operationLogLambdaQueryWrapper.eq(OperationLog::getSynced, false);
    List<OperationLog> operationLogs = operationLogService.list(operationLogLambdaQueryWrapper);
    //循环遍历日志数据推送到日志平台
    operationLogs.stream().forEach(operationLog -> {
      //构建调用日志平台入参
      JSONObject logJSON = new JSONObject();
      logJSON.put("id", operationLog.getId().toString());
      logJSON.put("type","operation");
      logJSON.put("timestamp", operationLog.getLogTime().getTime());
      //构建payload参数
      JSONObject payloadJSON = new JSONObject();
      payloadJSON.put("userId", operationLog.getUserId() + "");
      payloadJSON.put("organizationId", operationLog.getTenantId());
      payloadJSON.put("tenantId", operationLog.getTenantId());
      payloadJSON.put("clientId", operationLog.getClientId());
      payloadJSON.put("status", Integer.parseInt(
          Constants.OPERATIONLOG_SUCCESS.equals(operationLog.getOperationResult()) ? "1" : "0"));
      payloadJSON.put("serviceProvider", "pmt");
      payloadJSON.put("ip", operationLog.getOperationIp());
      String action = getAction(operationLog);
      payloadJSON.put("action", getAction(operationLog));
      payloadJSON.put("resourceId", operationLog.getServiceId().toString());
      if(!StringUtils.isEmpty(action)){
        String[] split = action.split(":");
        payloadJSON.put("resourceType", split[2]);
      }
      logJSON.put("payload", payloadJSON);
      String domain = SpringUtil.getProperty("app.log.domain");
      String uri = SpringUtil.getProperty("app.log.uri");
       try {
        // String result = HttpUtil.post(domain+uri,logJSON);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        //param参数
        StringEntity stringEntity = new StringEntity(
            logJSON.toString());
        stringEntity.setContentType("application/json;charset=UTF-8");

        HttpPost httpPost = new HttpPost(domain + uri);
        httpPost.setEntity(stringEntity);
        httpPost.setHeader("Content-type", "application/json;charset=UTF-8");
        addHeader(httpPost);
        HttpResponse response = httpClient.execute(httpPost);
        if (response.getStatusLine().toString().equals("HTTP/1.1 200 OK")) {
          logger.debug("【远程调用】OperationLoggingAspect|收集日志接口成功");
          //修改dmp日志信息sync为true
          updateOperationLog(operationLog, true);
        } else {
          throw new Exception(EntityUtils.toString(response.getEntity(), "UTF-8"));
        }
      } catch (Exception e) {
        logger.error("【远程调用】OperationLoggingAspect|收集日志接口异常：{}", e.getMessage());
      }
    });
  }

  private void addHeader(HttpPost request) {
    String requestId = Optional.ofNullable(request.getFirstHeader(API_REQUEST_ID_HEADER))
        .map(Header::getValue)
        .orElseGet(() -> MDC.get(LOG_REQUEST_ID));
    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(requestId)) {
      requestId = UUID.randomUUID().toString().replaceAll("-", "");
    }
    request.addHeader(API_REQUEST_ID_HEADER, requestId);
    final String language = I18nUtil.getCurrentLanguage();
    request.addHeader(LOCALE_HEADER, language);

    logger.info("Request-id: {}, Language: {}", requestId, language);
  }

  /**
   *
   */
  private void updateOperationLog(OperationLog operationLog, boolean synced) {
    //更新前先去数据库判断是否已被同步
    LambdaUpdateWrapper<OperationLog> operationLogLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
    operationLogLambdaUpdateWrapper.set(true, OperationLog::getSynced, synced);
    operationLogLambdaUpdateWrapper.eq(OperationLog::getId, operationLog.getId());
    operationLogLambdaUpdateWrapper.eq(OperationLog::getSynced, false);
    operationLogService.update(operationLogLambdaUpdateWrapper);
  }

  /**
   *
   */
  private String getAction(OperationLog operationLog) {
    StringBuilder action = new StringBuilder("rc:pmt:");
    String module = UcenterOperationModuleEnum.getNameByCode(
        Integer.parseInt(operationLog.getLogOperationModule()));
    String type = UcenterOperationTypeEnum.getNameByCode(
        Integer.parseInt(operationLog.getLogOperationType()));
    action.append(
        module.substring(0, 1) + module.substring(1) + ":"
            + type.substring(0, 1) + type.substring(1));
    return action.toString();
  }
}
