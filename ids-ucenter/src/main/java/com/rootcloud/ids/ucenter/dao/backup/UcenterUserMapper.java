package com.rootcloud.ids.ucenter.dao.backup;

import com.rootcloud.ids.ucenter.dto.backup.SysUserCondition;
import com.rootcloud.ids.ucenter.entity.backup.UcenterUser;
import com.rootcloud.ids.ucenter.vo.backup.SysUserQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 用户Mapper接口
 * @InterfaceName SysUserMapper
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public interface UcenterUserMapper extends BaseMapper<UcenterUser> {

    public List<SysUserQueryVO> query(@Param(value = "cnd") SysUserCondition cnd);

}
