package com.rootcloud.ids.ucenter.entity.backup;

import com.rootcloud.ids.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 组织实体对象
 * @ClassName SysGroup
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Getter
@Setter
@TableName("ucenter_group")
public class UcenterGroup extends BaseEntity {

    /**
     * 组织ID
     */
    @TableId(value = "group_id", type = IdType.ASSIGN_ID)
    private Long groupId;
    /**
     * 组织名称
     */
    private String groupName;
    /**
     * 英文名称
     */
    private String englishName;
    /**
     * 备注
     */
    private String remark;

}
