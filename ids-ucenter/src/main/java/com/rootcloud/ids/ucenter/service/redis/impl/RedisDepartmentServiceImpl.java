package com.rootcloud.ids.ucenter.service.redis.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.rootcloud.ids.common.redis.utils.RedisUtils;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDepartmentResp;
import com.rootcloud.ids.ucenter.service.redis.IRedisDepartmentService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 组织列表缓存实现类
 * @since 2022/3/1 3:00 下午
 */
@Service
public class RedisDepartmentServiceImpl extends RedisUtils implements IRedisDepartmentService {

    private static final String KEY = "IAM:DEPT:";

    private static final Integer EXP = 60 * 30;

    @Override
    public List<BaseDepartmentResp> get(String id) {
        Object obj = super.get(id);
        if (null == obj) {
            return null;
        }
        return JSONUtil.toList(obj.toString(), BaseDepartmentResp.class);
    }

    @Override
    public Boolean set(String id, List<BaseDepartmentResp> value) {
        if (CollUtil.isEmpty(value)) {
            return false;
        }
        return super.set(getKey(id), JSONUtil.toJsonStr(value), EXP);
    }

    private String getKey(String id) {
        return KEY + id;
    }

}
