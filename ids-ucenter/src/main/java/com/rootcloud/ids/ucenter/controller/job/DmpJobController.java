package com.rootcloud.ids.ucenter.controller.job;

import static com.rootcloud.ids.common.core.constant.GlobalConstants.LOG_REQUEST_ID;

import com.rootcloud.esmp.common.i18n.I18nUtil;
import com.rootcloud.ids.ucenter.config.XxlJobConfig;
import com.rootcloud.ids.ucenter.service.dmp.IDmpJobService;
import com.rootcloud.ids.ucenter.utils.DateUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Date;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "xxl.job.switch")
public class DmpJobController {

  private Logger logger = LoggerFactory.getLogger(XxlJobConfig.class);

  @Autowired
  private IDmpJobService dmpJobService;


  /**
   * 定时任务推送日志给日志平台
   */
  @XxlJob("UcenterJobPushUcenterLogToLogging")
  public void UcenterJobPushUcenterLogToLogging() throws Exception {
    try {
      final String requestId = UUID.randomUUID().toString().replaceAll("-", "");
      MDC.put(LOG_REQUEST_ID, requestId);
      log.info("定时任务的语言环境, 系统默认: {}", I18nUtil.getCurrentLanguage());

      dmpJobService.UcenterJobPushUcenterLogToLogging();
      logger.debug(DateUtil.getFormatTimeString(new Date(), DateUtil.DATE_FORMATE_STRING_A)
          + "执行了：推送日志信息给日志平台dmpJobPushDmpLogToLogging发生了异常");
    } catch (Exception e) {
      logger.debug("推送日志信息给日志平台dmpJobPushDmpLogToLogging发生了异常" + e, e);
    } finally {
      log.info("结束定时任务：UcenterJobPushUcenterLogToLogging");
      MDC.remove(LOG_REQUEST_ID);
    }
  }
}
