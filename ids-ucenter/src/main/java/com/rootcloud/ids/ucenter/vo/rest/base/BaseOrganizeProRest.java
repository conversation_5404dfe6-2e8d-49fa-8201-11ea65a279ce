package com.rootcloud.ids.ucenter.vo.rest.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-02-28 16:49
 **/
@Data
@ApiModel(value = "BaseOrganizeProRest", description = "权限组织列表")
public class BaseOrganizeProRest implements Serializable {

    private static final long serialVersionUID = 2967694873994066529L;
    @ApiModelProperty(value = "#机型ID#", required = true)
    @NotNull(message = "机型ID不能为空")
    private Long equipmentId;

    @ApiModelProperty(value = "#组织ID#")
    @NotBlank(message = "组织ID不能为空")
    private String cid;

    @ApiModelProperty(value = "#组织名称#")
    private String name;

    @ApiModelProperty(value = "#有没有授权，不传默认没有#")
    private Boolean authorization = false;

}
