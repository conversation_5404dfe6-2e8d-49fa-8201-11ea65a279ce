package com.rootcloud.ids.ucenter;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * @Description 用户中心应用启动类
 * @ClassName UcenterApplication
 * <AUTHOR>
 * @Date 2021/12/16
 *
 * @Version 1.0
 */
@MapperScan(basePackages = "com.rootcloud.ids.ucenter.dao")
@ComponentScan(basePackages = {"com.rootcloud.ids.ucenter","com.rootcloud.ids.common.web.config"})
@SpringBootApplication
public class UcenterApplication {

    public static void main(String[] args) {

        SpringApplication springApplication = new SpringApplication(UcenterApplication.class);
        springApplication.setBannerMode(Banner.Mode.OFF);
        springApplication.run(args);
    }

}
