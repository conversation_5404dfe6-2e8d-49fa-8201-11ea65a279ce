package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 权限更新DTO对象
 * @ClassName SysPermissionUpdateDTO
 * <AUTHOR>
 * @Date 2021/12/23
 * @Version 1.0
 */
@ApiModel(value = "权限更新DTO")
@Getter
@Setter
public class SysPermissionUpdateDTO extends BaseDTO {

    @ApiModelProperty(value = "权限ID")
    private Long permissionId;

    @ApiModelProperty(value = "所属模块")
    private String module;

    @ApiModelProperty(value = "request请求方式")
    private String method;

    @ApiModelProperty(value = "权限名称")
    private String permissionName;

    @ApiModelProperty(value = "权限值")
    private String permissionValue;

    @ApiModelProperty(value = "描述信息")
    private String remark;


}
