package com.rootcloud.ids.ucenter.controller.base;


import cn.hutool.core.util.StrUtil;
import com.rootcloud.ids.common.core.result.PageResult;
import com.rootcloud.ids.common.core.result.Result;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import com.rootcloud.ids.common.web.exception.BizException;
import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import com.rootcloud.ids.ucenter.service.base.IBaseDepartmentService;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDepartmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDeptEquipmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseEquipmentResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseDeptEquipmentAddRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseDeptEquipmentDelRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseDeptEquipmentPageRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseDeptEquipmentRest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 组织机构表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Api(value = "BaseDepartmentController", tags = "[BASE]部门管理")
@Slf4j
@RestController
@RequestMapping("/api/base/department")
public class BaseDepartmentController {

    @Autowired
    private IBaseDepartmentService departmentService;

    @ApiOperation(value = "所选组织下所有部门", notes = "所选组织下所有部门")
    @GetMapping(value = "/listDepartment")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", defaultValue = "", value = "组织ID", required = true),
    })
    public Result<List<BaseDepartmentResp>> listDepartment(@RequestParam("id") @NotBlank(message = "组织ID不允许为空") String id) {
        log.info("listDepartment:{}",id);
        return Result.success(departmentService.listDepartment(true, id));
    }

    @ApiOperation(value = "部门已拥有机型列表分页查询", notes = "部门已拥有机型列表分页查询")
    @GetMapping(value = "/pagePossess")
    public PageResult<BaseDeptEquipmentResp> pagePossess(@Valid BaseDeptEquipmentPageRest param) {
        log.info("pagePossess:{}",param);
        param.setPossess(true);
        return PageResult.success(departmentService.pageDept(param));
    }

    @ApiOperation(value = "部门未拥有机型列表分页查询", notes = "部门未拥有机型列表分页查询")
    @GetMapping(value = "/pageNoPossess")
    public PageResult<BaseDeptEquipmentResp> pageNoPossess(@Valid BaseDeptEquipmentPageRest param) {
        log.info("pageNoPossess:{}",param);
        param.setPossess(false);
        if(param.getDeptType() == DeptTypeEnum.DEPARTMENT && StrUtil.isEmpty(param.getCid())){
            throw new BizException(I18nUtil.message(I18nCode.SYS_100107));
        }
        return PageResult.success(departmentService.pageDept(param));
    }

    @ApiOperation(value = "所有权机型列表分页查询", notes = "所有权机型列表分页查询")
    @GetMapping(value = "/pageOwner")
    public PageResult<BaseDeptEquipmentResp> pageOwner(@Valid BaseDeptEquipmentPageRest param) {
        log.info("pageOwner:{}",param);
        return PageResult.success(departmentService.pageOwner(param));
    }

    @ApiOperation(value = "跨组织权限列表分页查询", notes = "跨组织权限列表分页查询")
    @GetMapping(value = "/pageUse")
    public PageResult<BaseDeptEquipmentResp> pageUse(@Valid BaseDeptEquipmentPageRest param) {
        log.info("pageUse:{}",param);
        return PageResult.success(departmentService.pageUse(param));
    }

    @ApiOperation(value = "新增部门机型权限", notes = "新增部门机型权限")
    @PostMapping(value = "/addEquipment")
    public Result<Boolean> addEquipment(@RequestBody @Valid BaseDeptEquipmentAddRest param) {
        log.info("addEquipment:{}",param);
        return Result.success(departmentService.addEquipment(param));
    }

    @ApiOperation(value = "部门所拥有机型", notes = "部门所拥有机型")
    @PostMapping(value = "/getEquipment")
    public Result<List<BaseEquipmentResp>> getEquipment(@RequestBody @Valid BaseDeptEquipmentRest param) {
        log.info("getEquipment:{}",param);
        return Result.success(departmentService.getEquipment(param));
    }

    @ApiOperation(value = "部门机型删除", notes = "部门机型删除")
    @PostMapping(value = "/removeEquipment")
    public Result<Boolean> removeEquipment(@RequestBody BaseDeptEquipmentDelRest param) {
        log.info("removeEquipment:{}", param);
        return Result.success(departmentService.removeEquipment(param));
    }

    @ApiOperation(value = "部门已拥有机型列表分页查询-设备类型过滤", notes = "部门已拥有机型列表分页查询-设备类型过滤")
    @GetMapping(value = "/pagePossessIds")
    public Result<Collection<Long>> pagePossessIds(@Valid BaseDeptEquipmentPageRest param) {
        log.info("pagePossessIds:{}", param);
        param.setPossess(true);
        return Result.success(departmentService.pagePossessIds(param));
    }

    @ApiOperation(value = "部门未拥有机型列表分页查询-设备类型过滤", notes = "部门未拥有机型列表分页查询-设备类型过滤")
    @GetMapping(value = "/pageNoPossessIds")
    public Result<Collection<Long>> pageNoPossessIds(@Valid BaseDeptEquipmentPageRest param) {
        log.info("pageNoPossess:{}", param);
        param.setPossess(false);
        if (param.getDeptType() == DeptTypeEnum.DEPARTMENT && StrUtil.isEmpty(param.getCid())) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100107));
        }
        return Result.success(departmentService.pagePossessIds(param));
    }
}
