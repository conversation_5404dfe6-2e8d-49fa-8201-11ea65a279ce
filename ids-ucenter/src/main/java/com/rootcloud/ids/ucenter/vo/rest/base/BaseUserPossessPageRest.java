package com.rootcloud.ids.ucenter.vo.rest.base;

import com.rootcloud.ids.common.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-02-28 16:49
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "BaseUserPossessPageRest", description = "用户权限查询")
public class BaseUserPossessPageRest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 2967694873994066529L;
    @ApiModelProperty(value = "#用户ID#",required = true)
    @NotBlank(message = "userId不能为空")
    private String userId;

    @ApiModelProperty(value = "#组织ID#",required = true)
    @NotBlank(message = "cid不能为空")
    private String cid;

    @ApiModelProperty("#机械大类id#")
    private Long conMacId;

    @ApiModelProperty(value = "#搜索字段#")
    private String searchKey;

    /**
     * 是否已拥有
     */
    private Boolean possess;

}
