package com.rootcloud.ids.ucenter.service.redis;

import com.rootcloud.esmp.common.dto.cache.BaseProductResp;
import com.rootcloud.esmp.common.dto.cache.UserDTO;
import com.rootcloud.esmp.common.dto.cache.TokenAndOrganizationUserCachedDTO;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2022/2/28
 */
public interface IRedisLoginService {

    /**
     * 获取用户信息
     *
     * @param tokenMd5 token md5
     * @return TokenAndOrganizationUserCachedDTO
     */
    TokenAndOrganizationUserCachedDTO getUserInfo(String tokenValue);

    boolean refreshUserCached();

    String getRedisKeyByToken(String tokenValue);

    String getToken(HttpServletRequest request);
}
