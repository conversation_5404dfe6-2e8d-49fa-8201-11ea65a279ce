package com.rootcloud.ids.ucenter.dao.backup;

import com.rootcloud.ids.ucenter.dto.backup.SysMenuCondition;
import com.rootcloud.ids.ucenter.entity.backup.UcenterMenu;
import com.rootcloud.ids.ucenter.vo.backup.SysMenuQueryVO;
import com.rootcloud.ids.ucenter.vo.backup.SysMenuVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 菜单Mapper接口
 * @InterfaceName SysMenuMapper
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public interface UcenterMenuMapper extends BaseMapper<UcenterMenu> {

    /**
     * 条件查询
     * @param cnd
     * @return
     */
    List<SysMenuQueryVO> query(@Param(value = "cnd") SysMenuCondition cnd);

    /**
     * 查询用户的菜单信息
     * @param userId
     * @return
     */
    List<SysMenuVO> findMenuByUserId(@Param(value = "userId") Long userId);
}
