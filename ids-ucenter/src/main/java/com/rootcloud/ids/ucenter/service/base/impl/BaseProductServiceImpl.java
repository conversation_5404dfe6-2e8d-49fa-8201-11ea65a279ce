package com.rootcloud.ids.ucenter.service.base.impl;

import static com.rootcloud.esmp.common.enums.ProductTypeEnum.CONSTRUCTION_MACHINERY;
import static com.rootcloud.esmp.common.enums.ProductTypeEnum.EQUIPMENT;
import static com.rootcloud.esmp.common.enums.ProductTypeEnum.EQUIPMENT_MODEL;
import static com.rootcloud.ids.common.i18n.I18nCode.SYS_100114;
import static com.rootcloud.ids.ucenter.dao.base.BaseProductMapper.ProductName;
import static com.rootcloud.ids.ucenter.utils.convertor.base.BaseProductRespConvertor.change2ProductRespList;
import static com.rootcloud.ids.ucenter.utils.convertor.base.BaseProductRespConvertor.changeBaseProductDetailResp;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.rootcloud.esmp.common.dto.cache.BaseProductResp;
import com.rootcloud.esmp.common.dto.cache.UserDTO;
import com.rootcloud.esmp.common.enums.*;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import com.rootcloud.esmp.common.utils.OptionalX;
import com.rootcloud.esmp.common.utils.SecurityUtils;
import com.rootcloud.esmp.iam.service.auth.IDataPermissionService;
import com.rootcloud.ids.common.core.result.ResultCode;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.ids.common.mybatis.utils.PageUtils;
import com.rootcloud.ids.common.web.dto.IamJwtParseDTO;
import com.rootcloud.ids.common.web.exception.BizException;
import com.rootcloud.ids.common.web.utils.JwtUtils;
import com.rootcloud.ids.ucenter.dao.base.BaseProductMapper;
import com.rootcloud.ids.ucenter.entity.base.BaseDeptProRel;
import com.rootcloud.ids.ucenter.entity.base.BaseProUserRel;
import com.rootcloud.ids.ucenter.entity.base.BaseProduct;
import com.rootcloud.ids.ucenter.entity.base.BaseUserDeptRel;
import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import com.rootcloud.ids.ucenter.enums.operationlog.UcenterOperationModuleEnum;
import com.rootcloud.ids.ucenter.enums.operationlog.UcenterOperationTypeEnum;
import com.rootcloud.ids.ucenter.service.base.IBaseDeptProRelService;
import com.rootcloud.ids.ucenter.service.base.IBaseProUserRelService;
import com.rootcloud.ids.ucenter.service.base.IBaseProductService;
import com.rootcloud.ids.ucenter.service.base.IBaseUserDeptRelService;
import com.rootcloud.ids.ucenter.service.dmp.IDmpService;
import com.rootcloud.ids.ucenter.service.operationlog.IOperationLogService;
import com.rootcloud.ids.ucenter.service.redis.IRedisLoginService;
import com.rootcloud.ids.ucenter.service.redis.IRedisProductService;
import com.rootcloud.ids.ucenter.utils.Constants;
import com.rootcloud.ids.ucenter.utils.convertor.UUIDUtils;
import com.rootcloud.ids.ucenter.utils.convertor.base.BaseProductRespConvertor;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDepartmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseEquipmentModelResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseEquipmentPageResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseProductDetailResp;
import com.rootcloud.ids.ucenter.vo.resp.openapi.BaseProductApiResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseEquipmentModelPageParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseEquipmentModelReleaseParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseEquipmentModelUpdateParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseEquipmentPageParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseEquipmentUpParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseProEquipmentParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseProductParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseProductUpParam;
import com.rootcloud.ids.ucenter.vo.rest.base.ProductListParam;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 产品分类表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-24
 */
@Slf4j
@Service
public class BaseProductServiceImpl extends ServiceImpl<BaseProductMapper, BaseProduct> implements
    IBaseProductService {

    @Autowired
    private IBaseDeptProRelService iBaseDeptProRelService;

    @Autowired
    private IBaseProUserRelService iBaseProUserRelService;

    @Autowired
    private IBaseUserDeptRelService iBaseUserDeptRelService;

    @Autowired
    private BaseProductMapper baseProductMapper;

    @Autowired
    private IDmpService iDmpService;

    @Autowired
    private IOperationLogService operationLogService;

    @Autowired
    private IRedisProductService redisProductService;

    @Autowired
    private IDataPermissionService dataPermissionService;

    @Autowired
    private IRedisLoginService iRedisLoginService;

    /**
     * 机械大类属性结构列表
     *
     * @return
     */
    @Override
    public List<BaseProductResp> listProduct() {
        List<BaseProduct> baseProducts = this.listProduct(null);
        return change2ProductRespList(baseProducts.listIterator());
    }


    @Override
    public List<BaseProductResp> listModel(Long parentId) {
        List<BaseProduct> entityList = this.listProduct(parentId);
        return change2ProductRespList(entityList.listIterator());
    }

    @Override
    public List<BaseProductApiResp> listProductName(List<Long> ids) {
        List<BaseProduct> entityList = this.listByIds(ids);
        if (CollUtil.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        Map<Long, String> machineryMap = this.allConstructionMachinery().stream()
            .collect(Collectors.toMap(BaseProduct::getId, BaseProduct::getProductName, (v1, v2) -> v1));
        List<BaseProductApiResp> respList = BaseProductRespConvertor.change2ProductApiRespList(
            entityList.listIterator());
        respList.forEach(obj -> obj.setParentName(machineryMap.get(obj.getParentId())));
        // 获取产品大类code
        Set<Long> parentIds = entityList.stream().map(BaseProduct::getParentId).filter(v -> v > 0).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(parentIds)) {
            List<BaseProduct> parentList = this.listByIds(parentIds);
            if (CollUtil.isNotEmpty(parentList)) {
                Map<Long, EquipmentTypeEnum> productCodeMap = parentList.stream().filter(v -> Objects.nonNull(v.getProductCode())).collect(Collectors.toMap(BaseProduct::getId, BaseProduct::getProductCode, (a, b) -> a));
                respList.forEach(obj -> {
                    if (obj.getParentId() > 0 && productCodeMap.containsKey(obj.getParentId())) {
                        obj.setProductCode(productCodeMap.get(obj.getParentId()));
                    }
                });
            }
        }
        return respList;
    }

    @Override
    public List<BaseProduct> listByParentIds(Set<Long> parentIds) {
        if (CollUtil.isEmpty(parentIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<BaseProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BaseProduct::getParentId, parentIds);
        return this.list(wrapper);
    }

    private List<BaseProduct> listProduct(Long parentId) {
        LambdaQueryWrapper<BaseProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(null != parentId, BaseProduct::getParentId, parentId)
            .orderByAsc(BaseProduct::getProductCode);
        return this.list(wrapper);
    }

    /**
     * 产品分类权限结构列表
     *
     * @return
     */
    @Override
    public List<BaseProductResp> listAuthProduct() {
        //查询机械大类
        List<BaseProduct> constructionMachinerys = this.allConstructionMachinery();
        List<BaseProduct> equipments = this.getUserAuthEquipment();
        //当没有机型权限直接返回空
        if (CollUtil.isEmpty(equipments)) {
            return new ArrayList<>();
        }
        constructionMachinerys.addAll(equipments);
        return change2ProductRespList(constructionMachinerys.iterator());
    }

    /**
     * 产品分类详情查询
     *
     * @param id
     * @return
     */
    @Override
    public BaseProductDetailResp getInfo(long id) {
        BaseProduct baseProduct = this.getById(id);
        //todo 设备数（调用接口）
        return changeBaseProductDetailResp(baseProduct);
    }

    /**
     * 新增机型
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long add(BaseProductParam productParam) {
        BaseProduct baseProduct = new BaseProduct();
        //判断机械大类是否存在
        BaseProduct product = this.getById(productParam.getId());
        if (null == product || CONSTRUCTION_MACHINERY != product.getProductType()) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100108));
        }
        baseProduct.setParentId(productParam.getId());
        //校验名称重复
        BaseProduct nameProduct = this.getOne(Wrappers.lambdaQuery(BaseProduct.class)
            .eq(BaseProduct::getProductName, productParam.getProductName())
            .eq(BaseProduct::getProductType, null == productParam.getId() ?
                CONSTRUCTION_MACHINERY : EQUIPMENT)
            .ne(null != productParam.getId(), BaseProduct::getId, productParam.getId()));
        if (null != nameProduct) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100109) + productParam.getProductName() + I18nUtil.message(I18nCode.SYS_100110));
        }
        //封装新增数据
        baseProduct.setProductName(productParam.getProductName());
        //目前默认生成就是启用
        baseProduct.setProductStatus(ProductStatusEnum.ENABLE);
        baseProduct.setProductBusinessCode(UUIDUtils.generateShortUuid());
        baseProduct.setProductType(null == productParam.getId() ?
            CONSTRUCTION_MACHINERY : EQUIPMENT);
        baseProduct.setTenantId(SecurityUtils.getCurrentTenantIdOrElseThrow());
        boolean flag = this.save(baseProduct);
        if (flag) {
            //保存日志对象
            String logContent = Constants.OPERATIONLOG_NEW_EQUIPMENT() + baseProduct.getProductName();
            operationLogService.saveOperationLog(baseProduct.getId().toString(),
                UcenterOperationModuleEnum.MODEL_MAINTENANCE, UcenterOperationTypeEnum.TYPE_CREATE,
                logContent, null, null, Constants.OPERATIONLOG_SUCCESS);
        }
        //删除缓存
        iRedisLoginService.refreshUserCached();
        redisProductService.del();
        return baseProduct.getId();
    }

    /**
     * 产品分类名称(机械大类、机型、设备号)更改
     *
     * @param param
     * @return
     */
    @Override
    public boolean updateById(BaseProductUpParam param) {
        BaseProduct product = this.getById(param.getId());
        if (null == product) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100111));
        }
        String oldName = product.getProductName();
        String oldDes = product.getDesc();
        if (!param.getProductName().equals(product.getProductName())) {
            //校验名称重复
            BaseProduct nameProduct = this.getOne(Wrappers.lambdaQuery(BaseProduct.class)
                .eq(BaseProduct::getProductName, param.getProductName())
                .eq(BaseProduct::getProductType, product.getProductType())
                .ne(null != param.getId(), BaseProduct::getId, product.getId()));
            if (null != nameProduct) {
                throw new BizException(I18nUtil.message(I18nCode.SYS_100109) + param.getProductName() + I18nUtil.message(I18nCode.SYS_100110));
            }
            product.setProductName(param.getProductName());
        }
        if(Objects.nonNull(param.getRegisterType())){
            product.setRegisterType(param.getRegisterType());
        }
        product.setUpdateTime(LocalDateTime.now());
        // 修改为未发布
        product.setReleaseStatus(ReleaseStatusEnum.NOT_ENABLED);
        // 描述信息
        if (null != param.getDesc()) {
            product.setDesc(param.getDesc());
        }
        // 对应总线拓扑id
        if (null != param.getTopologyId()) {
            product.setTopologyId(param.getTopologyId());
        }
        //保存日志对象
        String logContent = "";
        String logDetail = "";
        if (!oldName.equals(param.getProductName())){
            logContent = Constants.OPERATIONLOG_UPDATE() + ProductTypeEnum.getLabel(product.getProductType().getCode())
                + Constants.OPERATIONLOG_NAME();
            logDetail = ProductTypeEnum.getLabel(product.getProductType().getCode()) + Constants.OPERATIONLOG_NAME() + " "
                + Constants.OPERATIONLOG_UPDATE_PRE() + oldName + Constants.OPERATIONLOG_UPDATE_SUF() + param.getProductName();
        }
        if (!StringUtils.equals(param.getDesc(),oldDes)){
            if (StringUtils.isNotBlank(logDetail)){
                logDetail += "\n";
            }
            logContent += Constants.OPERATIONLOG_UPDATE() + ProductTypeEnum.getLabel(product.getProductType().getCode())
                + Constants.OPERATIONLOG_DESC();
            logDetail += ProductTypeEnum.getLabel(product.getProductType().getCode()) + Constants.OPERATIONLOG_DESC() + " "
                + Constants.OPERATIONLOG_UPDATE_PRE() + oldDes + Constants.OPERATIONLOG_UPDATE_SUF() + param.getDesc();
        }
        operationLogService.saveOperationLog(product.getId().toString(),
            UcenterOperationModuleEnum.COMPONENT_EQUIPMENTMODEL, UcenterOperationTypeEnum.TYPE_UPDATE,
            logContent, logDetail, null, Constants.OPERATIONLOG_SUCCESS);
        //    iDmpService.refreshCache();
        //删除缓存
        iRedisLoginService.refreshUserCached();
        redisProductService.del();
        return this.updateById(product);
    }

    /**
     * 产品分类(机械大类、机型、设备号)删除
     *
     * @param id
     * @return
     */
    @Override
    public boolean del(Long id) {
        //todo 删除机型或机械大类需要判断该分类下是否有关联的任何数据，包括协议模型、元组数据、设备数字模型、DBC文件、设备、部件等所有数据，
        // 仅无数据时才能删除，sprint60暂不开发删除功能
        //查询子集是否存在
        List<BaseProduct> baseProducts = this.getBaseMapper()
            .selectList(Wrappers.lambdaQuery(BaseProduct.class).eq(BaseProduct::getParentId, id));
        if (baseProducts.size() > 0) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100112));
        }
        boolean result = this.removeById(id);
        //删除缓存
        iRedisLoginService.refreshUserCached();
        redisProductService.del();
        return result;
    }

    /**
     * 修改设备号
     *
     * @param param
     * @return
     */
    @Override
    public boolean updateEquipment(BaseEquipmentUpParam param) {
        return false;
    }

    /**
     * 新增设备号
     *
     * @param param
     * @return
     */
    @Override
    public long addEquipment(BaseProEquipmentParam param) {
        this.checkEquipment(param.getId());
        final String tenantId = SecurityUtils.getCurrentTenantIdOrElseThrow();

        String token = SecurityUtils.getCurrentUser().getToken();
        IamJwtParseDTO iamJwtParseDTO = null;
        try {
            iamJwtParseDTO = JwtUtils.parseJwt(token);
        } catch (ParseException e) {
            log.info("addEquipment parseJwt e:{}",e);
            throw new RuntimeException(e);
        }

        //校验名称重复
        BaseProduct nameProduct = this.getOne(Wrappers.lambdaQuery(BaseProduct.class)
            .eq(BaseProduct::getProductName, param.getProductName())
            .eq(BaseProduct::getProductType, EQUIPMENT_MODEL));
        if (null != nameProduct) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100109) + param.getProductName() + I18nUtil.message(I18nCode.SYS_100110));
        }
        // 判断机型是否存在
        Optional.ofNullable(param.getId())
            .map(id -> this.getOne(Wrappers.lambdaQuery(BaseProduct.class)
                .eq(BaseProduct::getId, param.getId())
                .eq(BaseProduct::getProductType, EQUIPMENT)
                .eq(BaseProduct::getTenantId, tenantId)))
            .orElseThrow(() -> new BizException(SYS_100114.message()));

        //封装新增数据
        BaseProduct baseProduct = new BaseProduct();
        baseProduct.setParentId(param.getId());
        baseProduct.setProductName(param.getProductName());
        baseProduct.setRegisterType(param.getRegisterType());
        //目前默认生成就是启用
        baseProduct.setProductStatus(ProductStatusEnum.ENABLE);
        baseProduct.setProductType(EQUIPMENT_MODEL);
        baseProduct.setReleaseStatus(ReleaseStatusEnum.ENABLE);
        baseProduct.setProductBusinessCode(UUIDUtils.generateShortUuid());
        baseProduct.setTopologyId(param.getTopologyId());
        baseProduct.setDesc(param.getDesc());
        baseProduct.setTopologyVersion(param.getTopologyVersion());
        baseProduct.setTenantId(tenantId);
        if(Objects.nonNull(iamJwtParseDTO)&& org.apache.commons.lang3.StringUtils.isNotBlank(iamJwtParseDTO.getDataCenterId())){
            baseProduct.setOwnerShip(iamJwtParseDTO.getDataCenterId());
        }

        boolean flag = this.save(baseProduct);
        if (flag) {
            //保存日志对象
            String logContent = Constants.OPERATIONLOG_NEW_EQUIPMENT_MODEL() + baseProduct.getProductName();
            operationLogService.saveOperationLog(baseProduct.getId().toString(),
                UcenterOperationModuleEnum.COMPONENT_EQUIPMENTMODEL, UcenterOperationTypeEnum.TYPE_CREATE,
                logContent, null, null, Constants.OPERATIONLOG_SUCCESS);
        }
        //    iDmpService.refreshCache();
        //删除缓存
        iRedisLoginService.refreshUserCached();
        redisProductService.del();
        return baseProduct.getId();
    }

    /**
     * 机型下设备号列表分页查询
     *
     * @param param
     * @return
     */
    @Override
    public Page<BaseEquipmentModelResp> pageModelByParam(BaseEquipmentModelPageParam param) {
        BaseProduct baseProduct = null;
        if (null != param.getId()) {
            //机械大类、机型是否存在
            baseProduct = this.getById(param.getId());
            if (null == baseProduct || EQUIPMENT_MODEL == baseProduct.getProductType()) {
                throw new BizException(I18nUtil.message(I18nCode.SYS_100113));
            }
        }

        PermissionQueryEnum query = Optional.ofNullable(param.getPermissionQuery())
            .orElseGet(() -> param.isAcrossTheTenant() ? PermissionQueryEnum.CROSS_TENANT : PermissionQueryEnum.CURRENT_TENANT);
        LambdaQueryWrapper<BaseProduct> wrapper = getEquipmentModelPermissionWrapper(query, null, null);
        if (wrapper == null) {
            return new Page<>(param.getPage(), param.getPageSize(), 0);
        }

        if (baseProduct != null && baseProduct.getProductType() != null) {
            switch (baseProduct.getProductType()) {
                case CONSTRUCTION_MACHINERY:
                    LambdaQueryWrapper<BaseProduct> machineryWrapper = getEquipmentPermissionWrapper(query, null);
                    if (wrapper == null) {
                        return new Page<>(param.getPage(), param.getPageSize(), 0);
                    }
                    machineryWrapper.eq(BaseProduct::getParentId, baseProduct.getId());
                    List<BaseProduct> equipments = this.list(machineryWrapper);
                    if (CollectionUtils.isEmpty(equipments)) {
                        return new Page<>(param.getPage(), param.getPageSize(), 0);
                    }
                    wrapper.in(BaseProduct::getParentId, equipments.stream().map(BaseProduct::getId)
                        .collect(Collectors.toSet()));
                    break;
                case EQUIPMENT:
                    wrapper.eq(BaseProduct::getParentId, baseProduct.getId());
                    break;
                case EQUIPMENT_MODEL:
                    wrapper.eq(BaseProduct::getId, baseProduct.getId());
                    break;
                default:
                    return new Page<>(param.getPage(), param.getPageSize(), 0);
            }
        }

        String productName = param.getProductName();
        //判断条件值是否为空，如果不为空拼接条件
        if (!org.springframework.util.StringUtils.isEmpty(productName)) {
            //模糊查询
            wrapper.and((w) -> {
                w.like(BaseProduct::getProductName, productName).or()
                    .like(BaseProduct::getProductBusinessCode, productName);
            });
        }
        if (!org.springframework.util.StringUtils.isEmpty(param.getEquipmentModelId())) {
            //模糊查询
            wrapper.eq(BaseProduct::getId,param.getEquipmentModelId());
        }

        Page<BaseProduct> page = this.page(param.batisPage(), wrapper.orderByDesc(BaseProduct::getCreateTime));
        return PageUtils.convert(page, product -> {
            BaseEquipmentModelResp resp = new BaseEquipmentModelResp();
            resp.setId(product.getId());
            resp.setProductName(getProductName(product.getId()));
            resp.setEquipmentId(product.getParentId());
            resp.setEquipmentModelName(product.getProductName());
            resp.setCreateTime(product.getCreateTime());
            resp.setUpdateTime(product.getUpdateTime());
            resp.setReleaseStatus(product.getReleaseStatus());
            resp.setProductBusinessCode(product.getProductBusinessCode());
            resp.setTenantId(product.getTenantId());
            if(org.apache.commons.lang3.StringUtils.isNotBlank(product.getOwnerShip())){
                resp.setOwnerShip(product.getOwnerShip());
            }
            resp.setRegisterType(product.getRegisterType());
            return resp;
        });
    }

    /**
     * 机型下设备号列表分页查询
     *
     * @param param
     * @return
     */
    @Override
    public Page<BaseEquipmentModelResp> pageModelByParam2(BaseEquipmentModelPageParam param) {
        BaseProduct baseProduct = null;
        if (null != param.getId()) {
            //机械大类、机型是否存在
            baseProduct = this.getById(param.getId());
            if (null == baseProduct || EQUIPMENT_MODEL == baseProduct.getProductType()) {
                throw new BizException(I18nUtil.message(I18nCode.SYS_100113));
            }
        }
        LambdaQueryWrapper<BaseProduct> wrapper = new LambdaQueryWrapper<>();
        String productName = param.getProductName();
        //判断条件值是否为空，如果不为空拼接条件
        if (StrUtil.isNotEmpty(productName)) {
            //模糊查询
            wrapper.and((w) -> {
                w.like(BaseProduct::getProductName, productName).or()
                    .like(BaseProduct::getProductBusinessCode, productName);
            });
        }
        if (null != baseProduct) {
            if (CONSTRUCTION_MACHINERY == baseProduct.getProductType()) {
                //用户数据权限机型
                List<BaseProduct> equipments = this.getUserAuthEquipment2();
                //查询当前机械大类下机型
                List<BaseProduct> products = this.list(Wrappers.lambdaQuery(BaseProduct.class)
                    .in(CollUtil.isNotEmpty(equipments), BaseProduct::getId,
                        equipments.stream().map(BaseProduct::getId).collect(Collectors.toSet()))
                    .eq(BaseProduct::getParentId, param.getId()));
                if (CollUtil.isNotEmpty(products)) {
                    wrapper.in(BaseProduct::getParentId,
                        products.stream().map(BaseProduct::getId).collect(Collectors.toSet()));
                } else {
                    return param.batisPage();
                }
            } else if (EQUIPMENT == baseProduct.getProductType()) {
                wrapper.in(BaseProduct::getParentId, param.getId());
            } else {
                return param.batisPage();
            }
        } else {
            //用户数据权限机型
            List<BaseProduct> equipments = this.getUserAuthEquipment2();
            //查询机型
            if (CollUtil.isNotEmpty(equipments)) {
                List<BaseProduct> products = this.list(Wrappers.lambdaQuery(BaseProduct.class)
                    .in(BaseProduct::getId,
                        equipments.stream().map(BaseProduct::getId).collect(Collectors.toSet())));
                //设备型号
                if (CollUtil.isNotEmpty(products)) {
                    wrapper.in(CollUtil.isNotEmpty(products), BaseProduct::getParentId,
                        products.stream().map(BaseProduct::getId).collect(Collectors.toSet()));
                }
            } else {
                return param.batisPage();
            }
        }
        wrapper.orderByDesc(BaseProduct::getCreateTime);
        Page<BaseProduct> page = this.page(param.batisPage(), wrapper);
        return PageUtils.convert(page, product -> {
            BaseEquipmentModelResp resp = new BaseEquipmentModelResp();
            resp.setId(product.getId());
            resp.setProductName(getProductName(product.getId()));
            resp.setEquipmentId(product.getParentId());
            resp.setEquipmentModelName(product.getProductName());
            resp.setTenantId(product.getTenantId());
            resp.setCreateTime(product.getCreateTime());
            resp.setUpdateTime(product.getUpdateTime());
            resp.setReleaseStatus(product.getReleaseStatus());
            resp.setProductBusinessCode(product.getProductBusinessCode());
            return resp;
        });
    }

    /**
     * 机型列表分页查询
     *
     * @param param
     * @return
     */
    @Override
    public Page<BaseEquipmentPageResp> pageEquipmentByParam(BaseEquipmentPageParam param) {
        //查询机械大类
        List<BaseProduct> constructionMachinerys = this.allConstructionMachinery();
        Map<Long, String> conMacMap = constructionMachinerys.stream().collect(Collectors.toMap(BaseProduct::getId, BaseProduct::getProductName));

        // 获得机型列表，注：由于SQL中的ID条件中包含机械大类与机型的ID，所以下面SQL中使用id or parent_id查询
        PermissionQueryEnum query = param.getPermissionQuery() == null ? PermissionQueryEnum.CROSS_TENANT
            : param.getPermissionQuery();
        final String maintenanceSql = dataPermissionService.getWhereSql(query, IamPermissionActionEnum.MAINTENANCE);
        log.info("获得机型的数据权限SQL：{}", maintenanceSql);
        LambdaQueryWrapper<BaseProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseProduct::getProductType, ProductTypeEnum.EQUIPMENT);
        wrapper.eq(null != param.getConMacId(), BaseProduct::getParentId, param.getConMacId());
        wrapper.like(StringUtil.isNotEmpty(param.getEquipmentName()), BaseProduct::getProductName, param.getEquipmentName());
        wrapper.and(StringUtils.isNotBlank(maintenanceSql), w -> w.last(maintenanceSql));

        Page<BaseProduct> page = this.page(param.batisPage(), wrapper.orderByDesc(BaseProduct::getCreateTime));
        return PageUtils.convert(page, product -> {
            BaseEquipmentPageResp resp = new BaseEquipmentPageResp();
            resp.setId(product.getId());
            resp.setConMacId(product.getParentId());
            resp.setConstructionMachineryName(conMacMap.get(product.getParentId()));
            resp.setEquipmentId(product.getId());
            resp.setEquipmentName(product.getProductName());
            resp.setProductStatus(product.getProductStatus());
            resp.setProductType(product.getProductType());
            resp.setTenantId(product.getTenantId());
            resp.setCreateTime(product.getCreateTime());
            resp.setCreator(product.getCreator());
            return resp;
        });
    }

    /**
     * 校验机型是否存在
     *
     * @param id
     */
    private BaseProduct checkEquipment(Long id) {
        BaseProduct product = this.getById(id);
        if (null == product || EQUIPMENT != product.getProductType()) {
            throw new BizException(I18nUtil.message(SYS_100114));
        }
        return product;
    }

    private List<Long> getUserProduct(String userId, List<String> tenantIdList, List<String> deptIdList, Boolean isSysAdmin, String tenantId) {
        log.info("===== BaseUserServiceImpl =====getUserProduct userId:{} ,tenantIdList:{},deptIdList:{},isSysAdmin:{}", userId, tenantIdList, deptIdList, isSysAdmin);
        List<Long> productIdList;
        if (null != isSysAdmin && isSysAdmin) {
            List<BaseDeptProRel> relList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(tenantIdList, DeptTypeEnum.ORGANIZATION);
            if (CollUtil.isEmpty(relList)) {
                return new ArrayList<>();
            }
            productIdList = relList.stream().map(BaseDeptProRel::getProductId).collect(toList());
        } else {
            productIdList = this.iBaseProUserRelService.listByUserId(userId).stream().map(BaseProUserRel::getProductId).collect(
                toList());
            if (StringUtil.isNotEmpty(tenantId) && CollUtil.isNotEmpty(productIdList)) {
                List<BaseDeptProRel> baseDeptProRels = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(tenantId), DeptTypeEnum.ORGANIZATION);
                if (CollUtil.isEmpty(baseDeptProRels)) {
                    return new ArrayList<>();
                }
                List<Long> baseDeptProRel = baseDeptProRels.stream().map(BaseDeptProRel::getProductId).collect(
                    toList());
                productIdList = productIdList.stream().filter(baseDeptProRel::contains).collect(
                    toList());
            }
        }
        log.info("===== BaseUserServiceImpl =====getUserProduct productIdList:{}", productIdList.size());
        return productIdList;
    }

    /**
     * 查询用户拥有权限范围机型
     *
     * @return
     */
    @Override
    @Deprecated
    public List<BaseProduct> getUserAuthEquipment() {
        //查询用户
        UserDTO user = SecurityUtils.getCurrentUser();
        log.info("user:{}", user.toString());
        List<BaseProduct> equipments = null;
        if (null != user.getAdmin() && user.getAdmin()) {
            //用户管理员
            List<BaseDeptProRel> relList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(user.getTenantId()), DeptTypeEnum.ORGANIZATION);
            if (CollUtil.isEmpty(relList)) {
                return new ArrayList<>();
            }
            equipments = this.list(Wrappers.lambdaQuery(BaseProduct.class)
                .eq(BaseProduct::getProductType, EQUIPMENT)
                .in(BaseProduct::getId, relList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet())));
        } else {
            //查询用户所拥有权限机型
            List<BaseProUserRel> baseProUserRelList = iBaseProUserRelService.listByUserId(user.getUserId());
            if (CollUtil.isEmpty(baseProUserRelList)) {
                return new ArrayList<>();
            }
            List<BaseDeptProRel> baseDeptProRelList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(user.getTenantId()), DeptTypeEnum.ORGANIZATION);
            if (CollUtil.isEmpty(baseDeptProRelList)) {
                return new ArrayList<>();
            }
            List<Long> baseDeptProRel = baseDeptProRelList.stream().map(BaseDeptProRel::getProductId).collect(
                toList());
            List<Long> baseProUserList = baseProUserRelList.stream().map(BaseProUserRel::getProductId).collect(
                toList());
            List<Long> productIdList = baseProUserList.stream().filter(baseDeptProRel::contains).collect(
                toList());

            if (CollUtil.isNotEmpty(productIdList)) {
                equipments = this.list(Wrappers.lambdaQuery(BaseProduct.class)
                    .eq(BaseProduct::getProductType, EQUIPMENT)
                    .in(BaseProduct::getId, productIdList));
            }
        }
        return equipments;
    }

    /**
     * 查询用户拥有权限范围机型
     */
    @Override
    public List<BaseProduct> getUserAuthEquipment2() {
        //查询用户
        UserDTO user = SecurityUtils.getCurrentUser();
        log.info("user:{}", user.toString());
        List<BaseProduct> equipments = null;
        Set<Long> productIdList = new HashSet<>();
        List<BaseDeptProRel> relList;
        if (BooleanUtil.isTrue(user.getAdmin())) {
            relList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(user.getTenantId()), DeptTypeEnum.ORGANIZATION);
        } else {
            relList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(user.getDeptIdList(), DeptTypeEnum.DEPARTMENT);
        }
        if (CollUtil.isNotEmpty(relList)) {
            productIdList = relList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet());
        }
        //加上特殊个人授权的权限
        productIdList.addAll(this.iBaseProUserRelService.listByUserId(user.getUserId()).stream().map(BaseProUserRel::getProductId).collect(Collectors.toSet()));
        if (CollUtil.isNotEmpty(productIdList)) {
            equipments = this.list(Wrappers.lambdaQuery(BaseProduct.class)
                .eq(BaseProduct::getProductType, EQUIPMENT)
                .in(BaseProduct::getId, productIdList));
        }
        return equipments;
    }

    @Override
    public List<BaseProduct> getUserAuthEquipmentByParam(String userId, List<String> deptIdList, String orgId, Boolean isSysAdmin) {
        if (StringUtil.isEmpty(userId)) {
            throw new BizException(ResultCode.PARAM_ERROR);
        }
        //查询用户是否属于特殊用户
        List<BaseUserDeptRel> baseUserDeptRel = iBaseUserDeptRelService.listUserId(userId);
        List<BaseProduct> equipments = null;
        //ids正常用户
        if (CollUtil.isEmpty(baseUserDeptRel)) {
            List<BaseDeptProRel> relList = null;
            //部门
            if (CollUtil.isNotEmpty(deptIdList)) {
                relList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(deptIdList, DeptTypeEnum.DEPARTMENT);
            } else {
                //组织
                relList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(orgId), DeptTypeEnum.ORGANIZATION);
            }
            if (CollUtil.isNotEmpty(relList)) {
                equipments = this.list(Wrappers.lambdaQuery(BaseProduct.class)
                    .eq(BaseProduct::getProductType, EQUIPMENT)
                    .in(BaseProduct::getId, relList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet())));
            }
        } else { //ids特殊用户
            //查询用户所拥有权限机型
            List<BaseProUserRel> baseProUserRels = iBaseProUserRelService.getProUserRelByUserId(userId);
            equipments = this.list(Wrappers.lambdaQuery(BaseProduct.class)
                .eq(BaseProduct::getProductType, EQUIPMENT)
                .in(CollUtil.isNotEmpty(baseProUserRels), BaseProduct::getId, baseProUserRels.stream().map(BaseProUserRel::getProductId).collect(Collectors.toSet())));
        }
        return equipments;
    }

    /**
     * 查询机型信息
     *
     * @param equipmentModelId
     * @return
     */
    @Override
    public List<BaseProductApiResp> productInfo(String equipmentModelId) {
        //查询设备型号
        List<BaseProduct> baseProducts = this.list(
            Wrappers.lambdaQuery(BaseProduct.class)
                .eq(StringUtils.isNotBlank(equipmentModelId), BaseProduct::getId,
                    StringUtils.isNotBlank(equipmentModelId) ? Long.valueOf(equipmentModelId)
                        : null)
                .eq(true, BaseProduct::getProductType, EQUIPMENT_MODEL.getCode()));
        if (CollectionUtils.isEmpty(baseProducts)) {
            return emptyList();
        }
        //查询机型
        List<BaseProduct> baseProductList = this.list(
            Wrappers.lambdaQuery(BaseProduct.class)
                .eq(baseProducts.get(0).getParentId() != null, BaseProduct::getId,
                    baseProducts.get(0).getParentId() != null ? baseProducts.get(0).getParentId()
                        : null)
                .eq(true, BaseProduct::getProductType, EQUIPMENT.getCode()));
        if (CollectionUtils.isEmpty(baseProductList)) {
            return emptyList();
        }
        return baseProductList.stream().map(x -> {
            BaseProductApiResp baseProductApiResp = new BaseProductApiResp();
            BeanUtils.copyProperties(x, baseProductApiResp);
            return baseProductApiResp;
        }).collect(toList());
    }

    /**
     * 查询组织信息
     *
     * @param productId
     * @return
     */
    @Override
    public List<BaseDepartmentResp> groupInfo(String productId) {
        List<BaseProduct> baseProductList = this.list(
            Wrappers.lambdaQuery(BaseProduct.class)
                .eq(StringUtils.isNotBlank(productId), BaseProduct::getId,
                    StringUtils.isNotBlank(productId) ? Long.valueOf(productId)
                        : null)
                .eq(true, BaseProduct::getProductType, EQUIPMENT.getCode()));

        if (CollectionUtils.isEmpty(baseProductList)) {
            return emptyList();
        }

        //查询组织信息
        return baseProductList.stream().map(x -> {
            BaseDepartmentResp baseDepartmentResp = new BaseDepartmentResp();
            baseDepartmentResp.setDeptType(DeptTypeEnum.ORGANIZATION);
            baseDepartmentResp.setId(x.getTenantId());
            baseDepartmentResp.setCid(x.getTenantId());
            baseDepartmentResp.setName(x.getTenantId());
            return baseDepartmentResp;

        }).collect(toList());
    }

    @Override
    public BaseProductDetailResp getEquipmentModelById(long id) {
        BaseProduct equipmentModelProduct = this.getById(id);
        if (null == equipmentModelProduct || EQUIPMENT_MODEL != equipmentModelProduct.getProductType()) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100115));
        }
        BaseProductDetailResp baseProductDetailResp = changeBaseProductDetailResp(equipmentModelProduct);
        baseProductDetailResp.setSupProductName(getProductName(equipmentModelProduct.getId()));
        return baseProductDetailResp;
    }

    @Override
    public List<BaseProductDetailResp> getEquipmentModelByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return emptyList();
        }

        List<BaseProduct> products = listByIds(ids).stream().filter(
            (BaseProduct product) -> EQUIPMENT_MODEL == product.getProductType()

        ).collect(toList());

        if (CollectionUtils.isEmpty(products)) {
            return emptyList();
        }

        Map<Long, String> productNames = baseMapper.findSuperProductNamesByProducts(
            products, I18nUtil.isEnglish()

        ).stream().collect(
            Collectors.toMap(ProductName::getId, ProductName::getName)
        );

        return products.stream().map((BaseProduct product) -> {
            BaseProductDetailResp resp = changeBaseProductDetailResp(product);
            resp.setSupProductName(productNames.get(product.getId()));

            return resp;

        }).collect(toList());
    }

    @Override
    public BaseProductDetailResp getEquipmentModelByCode(String code) {
        BaseProduct equipmentModelProduct = this.baseProductMapper
            .selectOne(new LambdaQueryWrapper<BaseProduct>().eq(BaseProduct::getProductBusinessCode, code));
        if (null == equipmentModelProduct || EQUIPMENT_MODEL != equipmentModelProduct.getProductType()) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100115));
        }
        BaseProductDetailResp baseProductDetailResp = changeBaseProductDetailResp(equipmentModelProduct);
        baseProductDetailResp.setSupProductName(getProductName(equipmentModelProduct.getId()));
        return baseProductDetailResp;
    }

    @Override
    public Boolean equipmentModelRelease(BaseEquipmentModelReleaseParam param) {
        BaseProduct equipmentModelProduct = this.getById(param.getId());
        if (null == equipmentModelProduct || EQUIPMENT_MODEL != equipmentModelProduct.getProductType()) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100115));
        }
        equipmentModelProduct.setReleaseStatus(ReleaseStatusEnum.codeOf(param.getReleaseStatus()));
        equipmentModelProduct.setUpdateTime(LocalDateTime.now());
        equipmentModelProduct.setModifier(SecurityUtils.getCurrentUserName());
        boolean b = this.updateById(equipmentModelProduct);
        if (equipmentModelProduct.getReleaseStatus() == ReleaseStatusEnum.ENABLE) {
            iDmpService.refreshCache();
        }
        iRedisLoginService.refreshUserCached();
        return b;
    }

    @Override
    public Boolean updateEquipmentModel(BaseEquipmentModelUpdateParam param) {
        boolean result = false;
        if (Objects.nonNull(param.getId())) {
            BaseProduct equipmentModelProduct = this.getById(param.getId());
            if (null == equipmentModelProduct || EQUIPMENT_MODEL != equipmentModelProduct.getProductType()) {
                throw new BizException(I18nUtil.message(I18nCode.SYS_100115));
            }
            equipmentModelProduct.setHasPublished(0);
            equipmentModelProduct.setUpdateTime(LocalDateTime.now());
            equipmentModelProduct.setModifier(SecurityUtils.getCurrentUserName());
           // equipmentModelProduct.setRegisterType(Optional.ofNullable(param).map(BaseEquipmentModelUpdateParam::getRegisterType).orElse(RegisterTypeEnum.ENGINEERING_MACHINES));
            result = this.updateById(equipmentModelProduct);
        } else {
            Long topologyId = param.getTopologyId();
            LambdaQueryWrapper<BaseProduct> wrapper = Wrappers.lambdaQuery(BaseProduct.class).eq(BaseProduct::getTopologyId, topologyId);
            List<BaseProduct> baseProducts = list(wrapper);
            // 更新最新版本
            baseProducts.forEach(baseProduct -> baseProduct.setTopologyVersion(param.getTopologyVersion()));
            result = updateBatchById(baseProducts);
        }

        iRedisLoginService.refreshUserCached();
        return result;
    }

    @Override
    public Collection<Long> pageEquipmentByParamFilterIds(BaseEquipmentPageParam param) {
        LambdaQueryWrapper<BaseProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseProduct::getProductType, EQUIPMENT.getCode());
        wrapper.like(StringUtil.isNotEmpty(param.getEquipmentName()), BaseProduct::getProductName, param.getEquipmentName());
        wrapper.select(BaseProduct::getParentId);
        // 全量数据
        List<BaseProduct> allProducts = this.list(wrapper);
        return allProducts.stream().map(BaseProduct::getParentId).collect(Collectors.toSet());
    }

    /**
     * 获得机型数据权限查询条件
     * @param query 指定查询范围，不能为空
     * @param wrapper 其他查询条件
     * @return
     */
    private LambdaQueryWrapper<BaseProduct> getEquipmentPermissionWrapper(@NotNull PermissionQueryEnum query, LambdaQueryWrapper<BaseProduct> wrapper) {
        // 查询有权限的机械大类与机型列表
        // 获得机型列表，注：由于SQL中的ID条件中包含机械大类与机型的ID，所以下面SQL中使用id or parent_id查询
        final String maintenanceSql = dataPermissionService.getWhereSql(query, IamPermissionActionEnum.MAINTENANCE);
        log.info("获得机型的数据权限SQL：{}", maintenanceSql);
        wrapper = Optional.ofNullable(wrapper).orElseGet(() -> new LambdaQueryWrapper<>());
        wrapper.eq(BaseProduct::getProductType, EQUIPMENT);
        wrapper.and(StringUtils.isNotBlank(maintenanceSql), w -> w.last(maintenanceSql));
        return wrapper;
    }

    /**
     * 获得机械大类数据权限查询条件
     * @param query 指定查询范围，不能为空
     * @param constructionMachineryIdsForAuthorized 被授权的机械大类ID，为空时，将通过IAM数据权限获取
     * @param wrapper 其他查询条件
     * @return
     */
    private LambdaQueryWrapper<BaseProduct> getConstructionMachineryPermissionWrapper(
        @NotNull PermissionQueryEnum query, List<Long> constructionMachineryIdsForAuthorized, LambdaQueryWrapper<BaseProduct> wrapper) {
        // 如果是Admin账号，不用过滤数据
        boolean isAdmin = OptionalX.ofNullable(SecurityUtils.getCurrentUser()).map(UserDTO::getAdmin)
            .orElse(false);
        if (isAdmin) {
            LambdaQueryWrapper<BaseProduct> where = OptionalX.ofNullable(wrapper).orElse(new LambdaQueryWrapper<>());
            where.eq(BaseProduct::getProductType, CONSTRUCTION_MACHINERY);
            return where;
        }

        // 通过机型获得机械大类列表
        if (CollectionUtil.isEmpty(constructionMachineryIdsForAuthorized)) {
            final List<BaseProduct> equipments = getEquipmentByPermission(query, null);
            constructionMachineryIdsForAuthorized = equipments.stream().map(BaseProduct::getParentId)
                .collect(toList());
        }

        return OptionalX.ofNullable(constructionMachineryIdsForAuthorized).map(ids -> {
            LambdaQueryWrapper<BaseProduct> where = OptionalX.ofNullable(wrapper)
                .orElseGet(() -> new LambdaQueryWrapper<>());
            where.eq(BaseProduct::getProductType, ProductTypeEnum.CONSTRUCTION_MACHINERY);
            where.in(BaseProduct::getId, ids);
            return where;
        }).orElse(wrapper);
    }

    /**
     * 获得设备型号数据权限的查询条件
     * @param query 指定查询范围
     * @param equipmentIdsForAuthorized 被授权的机型ID列表，为空时，将通过IAM数据权限获取
     * @param wrapper 其他条件
     * @return
     */
    public LambdaQueryWrapper<BaseProduct> getEquipmentModelPermissionWrapper(
        @NotNull PermissionQueryEnum query, List<Long> equipmentIdsForAuthorized, LambdaQueryWrapper<BaseProduct> wrapper) {
        if (CollectionUtil.isEmpty(equipmentIdsForAuthorized)) {
            final List<BaseProduct> equipments = getEquipmentByPermission(query, null);
            equipmentIdsForAuthorized = equipments.stream().map(BaseProduct::getId)
                .collect(toList());
        }
        return OptionalX.ofNullable(equipmentIdsForAuthorized).map(ids -> {
            // 通过机型、设备型号的数据权限获得设备型号列表
            final String equipmentModelSql = dataPermissionService.getWhereSql(query, IamPermissionActionEnum.EQUIPMENT_MODEL);
            log.info("获得设备型号的数据权限SQL: {}", equipmentModelSql);

            LambdaQueryWrapper<BaseProduct> where = OptionalX.ofNullable(wrapper)
                .orElseGet(() -> new LambdaQueryWrapper<>());
            where.eq(BaseProduct::getProductType, ProductTypeEnum.EQUIPMENT_MODEL);
            where.in(BaseProduct::getParentId, ids);
            where.and(StringUtils.isNotBlank(equipmentModelSql), w -> w.last(equipmentModelSql));
            return where;
        }).orElse(wrapper);
    }

    private List<BaseProduct> getEquipmentByPermission(@NotNull PermissionQueryEnum query, LambdaQueryWrapper<BaseProduct> wrapper) {
        // 查询有权限的机械大类与机型列表
        return list(getEquipmentPermissionWrapper(query, wrapper));
    }

    private List<BaseProduct> getConstructionMachineryByPermission(
        @NotNull PermissionQueryEnum query, List<Long> constructionMachineryIdsForAuthorized, LambdaQueryWrapper<BaseProduct> wrapper) {
        return OptionalX.ofNullable(getConstructionMachineryPermissionWrapper(query, constructionMachineryIdsForAuthorized, wrapper))
            .map(w -> list(w)).orElse(Lists.newArrayList());
    }

    private List<BaseProduct> getEquipmentModelByPermission(
        @NotNull PermissionQueryEnum query, List<Long> equipmentIdsForAuthorized, LambdaQueryWrapper<BaseProduct> wrapper) {
        return OptionalX.ofNullable(getEquipmentModelPermissionWrapper(query, equipmentIdsForAuthorized, wrapper))
            .map(w -> list(w)).orElse(Lists.newArrayList());
    }

    @Override
    public List<BaseProduct> getByPermission(@NotNull PermissionQueryEnum query) {
        return getByPermission(query, EQUIPMENT_MODEL, null);
    }

    @Override
    public List<BaseProduct> getByPermission(
        @NotNull PermissionQueryEnum query, @NotNull ProductTypeEnum lowestProductType, LambdaQueryWrapper<BaseProduct> otherWrapper) {
        UserDTO userDTO = SecurityUtils.getCurrentUser();
        if (null == userDTO) {
            throw new BizException(ResultCode.AUTHORIZED_ERROR);
        }
        List<BaseProduct> allBaseProducts = new ArrayList<>();
        switch (lowestProductType) {
            case CONSTRUCTION_MACHINERY:
                return getConstructionMachineryByPermission(query, null, otherWrapper);
            case EQUIPMENT:
                final List<BaseProduct> equipments = getEquipmentByPermission(query, otherWrapper);

                // 通过机型获得机械大类列表
                final List<Long> constructionMachineryIds = equipments.stream().map(BaseProduct::getParentId)
                    .distinct().collect(toList());
                final List<BaseProduct> constructionMachineryList = OptionalX.ofNullable(constructionMachineryIds)
                    .map(ids -> getConstructionMachineryByPermission(query, ids, null))
                    .orElse(Lists.newArrayList());
                allBaseProducts.addAll(constructionMachineryList);
                allBaseProducts.addAll(equipments);
                return allBaseProducts;
            case EQUIPMENT_MODEL:
                final List<BaseProduct> constructionMachineryAndEquipments = getByPermission(query, EQUIPMENT, null);

                // 通过机型、设备型号的数据权限获得设备型号列表
                final List<Long> equipmentIds = constructionMachineryAndEquipments.stream()
                    .filter(br -> EQUIPMENT.equals(br.getProductType())).map(BaseProduct::getId)
                    .distinct().collect(toList());
                List<BaseProduct> equipmentModelList = OptionalX.ofNullable(equipmentIds)
                    .map(ids -> getEquipmentModelByPermission(query, ids, otherWrapper))
                    .orElse(Lists.newArrayList());

                allBaseProducts.addAll(constructionMachineryAndEquipments);
                allBaseProducts.addAll(equipmentModelList);

                return allBaseProducts;
        }

        return allBaseProducts;
    }

    @Override
    public List<BaseProduct> onlyProductTypeByPermission(@NotNull PermissionQueryEnum query,
        @NotNull ProductTypeEnum productType,
        LambdaQueryWrapper<BaseProduct> otherWrapper) {
        List<BaseProduct> all = getByPermission(query, productType, otherWrapper);
        return all.stream().filter(bp -> bp.getProductType().equals(productType)).collect(toList());
    }

    @Override
    public Collection<Long> pageModelByParamFilterIds(BaseEquipmentModelPageParam param) {
        BaseProduct baseProduct = null;
        if (null != param.getId()) {
            //机械大类、机型是否存在
            baseProduct = this.getById(param.getId());
            if (null == baseProduct || EQUIPMENT_MODEL == baseProduct.getProductType()) {
                return new ArrayList<>();
            }
        }

        PermissionQueryEnum query = Optional.ofNullable(param.getPermissionQuery())
            .orElseGet(() -> param.isAcrossTheTenant() ? PermissionQueryEnum.CROSS_TENANT : PermissionQueryEnum.CURRENT_TENANT);
        List<BaseProduct> allBaseProduct = getByPermission(query);
        if (null != baseProduct) {
            // 有权限的机型ID列表
            List<Long> equipmentIds = new ArrayList<>();

            // 机械大类下的机型列表
            if (CONSTRUCTION_MACHINERY == baseProduct.getProductType()) {
                // 机械大类下的机型列表
                equipmentIds = allBaseProduct.stream().filter(bp -> param.getId().equals(bp.getParentId()))
                    .map(BaseProduct::getId).collect(toList());
            } else if (EQUIPMENT == baseProduct.getProductType()) {
                equipmentIds = Lists.newArrayList(param.getId());
            } else {
                return new ArrayList<>();
            }

            // 设备型号列表
            List<BaseProduct> equipmentModels = allBaseProduct.stream()
                .filter(bp -> EQUIPMENT_MODEL.equals(bp.getProductType()))
                .collect(toList());
            // 返回存在设备型号的机型ID列表
            return equipmentIds.stream()
                .filter(parentId -> equipmentModels.stream().anyMatch(em -> parentId.equals(em.getParentId())))
                .collect(toList());
        } else {
            // 所有设备型号对应的机型ID列表
            return allBaseProduct.stream()
                .filter(bp -> EQUIPMENT_MODEL.equals(bp.getProductType()))
                .map(BaseProduct::getParentId).collect(toList());
        }

        //LambdaQueryWrapper<BaseProduct> wrapper = new LambdaQueryWrapper<>();
        //wrapper.like(StringUtils.isNotBlank(param.getProductName()), BaseProduct::getProductName,
        //        param.getProductName());
        //if (null != baseProduct) {
        //    if (CONSTRUCTION_MACHINERY == baseProduct.getProductType()) {
        //        //用户数据权限机型
        //        List<BaseProduct> equipments;
        //        if (param.isAcrossTheTenant()) {
        //            equipments = this.getUserAuthEquipment2();
        //        } else {
        //            equipments = this.getUserAuthEquipment();
        //        }
        //        //查询当前机械大类下机型
        //        List<BaseProduct> products = equipments.stream().filter(obj -> param.getId().equals(obj.getParentId())).collect(Collectors.toList());
        //        if (CollUtil.isNotEmpty(products)) {
        //            wrapper.in(BaseProduct::getParentId,
        //                    products.stream().map(BaseProduct::getId).collect(Collectors.toSet()));
        //        } else {
        //            return new ArrayList<>();
        //        }
        //    } else if (EQUIPMENT == baseProduct.getProductType()) {
        //        wrapper.in(BaseProduct::getParentId, param.getId());
        //    } else {
        //        return new ArrayList<>();
        //    }
        //} else {
        //    //用户数据权限机型
        //    List<BaseProduct> equipments;
        //    if (param.isAcrossTheTenant()) {
        //        equipments = this.getUserAuthEquipment2();
        //    } else {
        //        equipments = this.getUserAuthEquipment();
        //    }
        //    //查询机型
        //    if (CollUtil.isNotEmpty(equipments)) {
        //        List<BaseProduct> products = equipments.stream().filter(obj -> obj.getProductType() == EQUIPMENT).collect(Collectors.toList());
        //        //设备型号
        //        if (CollUtil.isNotEmpty(products)) {
        //            wrapper.in(CollUtil.isNotEmpty(products), BaseProduct::getParentId,
        //                    products.stream().map(BaseProduct::getId).collect(Collectors.toSet()));
        //        }
        //    } else {
        //        return new ArrayList<>();
        //    }
        //}
        //wrapper.select(BaseProduct::getParentId);
        //return this.list(wrapper).stream().map(BaseProduct::getParentId).collect(Collectors.toSet());
    }

    @Override
    public Collection<Long> pageModelByParamFilterDeviceTypeIds(BaseEquipmentModelPageParam param) {
        Collection<Long> ids = pageModelByParamFilterIds(param);
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<BaseProduct> baseProducts = listByIds(ids);
        return baseProducts.stream().map(BaseProduct::getParentId).collect(Collectors.toSet());
    }

    @Override
    public List<BaseProductResp> listByParam(ProductListParam param) {
        if ((param.getEquipmentModelIds() != null && param.getEquipmentModelIds().isEmpty()) &&
            (param.getEquipmentModelCodeList() != null && param.getEquipmentModelCodeList().isEmpty())) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<BaseProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(param.getEquipmentModelIds() != null,
                BaseProduct::getId, param.getEquipmentModelIds())
            .in(param.getEquipmentModelCodeList() != null,
                BaseProduct::getProductBusinessCode, param.getEquipmentModelCodeList());
        List<BaseProduct> list = list(wrapper);
        return change2ProductRespList(list.iterator());
    }

    @Override
    public List<BaseProduct> allConstructionMachinery() {
        return this.list(Wrappers.lambdaQuery(BaseProduct.class)
            .eq(BaseProduct::getProductType, CONSTRUCTION_MACHINERY)
            .orderByAsc(BaseProduct::getProductCode));
    }

    @Override
    public List<BaseProduct> listMachineryPub() {
        List<BaseProduct> baseProducts = this.list(Wrappers.lambdaQuery(BaseProduct.class)
            .eq(BaseProduct::getProductType, CONSTRUCTION_MACHINERY));
        Optional.ofNullable(baseProducts).ifPresent(list -> {
            BaseProduct baseProduct = new BaseProduct();
            baseProduct.setId(Long.valueOf(-1));
            baseProduct.setProductStatus(ProductStatusEnum.ENABLE);
            baseProduct.setProductType(CONSTRUCTION_MACHINERY);
            baseProduct.setDeleteFlag(0);
            baseProduct.setParentId(Long.valueOf(0));
            baseProduct.setCreateTime(LocalDateTime.now());
            baseProduct.setCreator(I18nUtil.message(I18nCode.SYS_100116));
            //公用code特殊处理
            baseProduct.setProductCode(EquipmentTypeEnum.PUBLIC_MACHINERY);
            baseProduct.setProductName(I18nUtil.message(I18nCode.SYS_100053));
            list.add(baseProduct);
        });
        return baseProducts;
    }

    @Override
    public String getProductName(long id) {
        return baseProductMapper.getProductName(id, I18nUtil.isEnglish());
    }
}
