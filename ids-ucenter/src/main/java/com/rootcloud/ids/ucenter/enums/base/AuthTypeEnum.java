package com.rootcloud.ids.ucenter.enums.base;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * @author: ling.kang
 * @create: 2022-02-28 15:01
 **/
public enum AuthTypeEnum implements IEnum<Integer> {
    /**
     * #权限类型#ENUM#1:所有权:OWNER,2:使用权:USE#
     */
    OWNER(1, I18nCode.SYS_100032),
    OWNER_USE(2, I18nCode.SYS_100033),
    OTHER_USE(3, I18nCode.SYS_100034),
    ;
    private final int code;
    private final I18nCode label;

    AuthTypeEnum(int code, I18nCode label) {
        this.code = code;
        this.label = label;
    }

    public int getCode() {
        return code;
    }

    public String getLabel() {
        return I18nUtil.message(label);
    }

    public static String getLabel(Integer code) {
        if (code != null) {
            for (AuthTypeEnum value : AuthTypeEnum.values()) {
                if (value.code == code) {
                    return value.getLabel();
                }
            }
        }
        return null;
    }

    public static AuthTypeEnum codeOf(int code) {
        for (AuthTypeEnum value : AuthTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new RuntimeException("cant not change code: " + code + " to AuthTypeEnum.");
    }

    @Override
    public Integer getValue() {
        return code;
    }

}
