package com.rootcloud.ids.ucenter.vo.rest.base;

import com.rootcloud.esmp.common.enums.PermissionQueryEnum;
import com.rootcloud.ids.common.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/19 15:37
 */
@Data
@ApiModel(value = "BaseEquipmentPageParam", description = "机型列表参数")
public class BaseEquipmentPageParam extends BasePageQuery implements Serializable {

    @ApiModelProperty("#CROSS_TENANT: 查询有权限的所有组织数据，默认值；CURRENT_TENANT: 查询当前组织数据；DELEGATED_TENANT：查询授权给本组织的数据#")
    private PermissionQueryEnum permissionQuery;

    @ApiModelProperty("#机械大类id#")
    private Long conMacId;

    @ApiModelProperty("#机型名称#")
    private String equipmentName;

}
