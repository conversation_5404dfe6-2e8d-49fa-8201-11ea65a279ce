package com.rootcloud.ids.ucenter.vo.resp.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rootcloud.esmp.common.enums.ProductStatusEnum;
import com.rootcloud.esmp.common.enums.ProductTypeEnum;
import com.rootcloud.esmp.common.enums.RegisterTypeEnum;
import com.rootcloud.esmp.common.enums.ReleaseStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: ling.kang
 * @create: 2022-02-25 14:17
 **/
@Data
@ApiModel(value = "BaseProductDetailResp", description = "产品详情")
public class BaseProductDetailResp implements Serializable {

    private static final long serialVersionUID = 7618289024898637247L;

    @ApiModelProperty("#分类id#")
    private Long id;

    @ApiModelProperty("#父级id#")
    private Long parentId;

    @ApiModelProperty("#分类名称#")
    private String productName;

    @ApiModelProperty("#设备数#")
    private Integer equipmentNumber;

    @ApiModelProperty("#产品状态 1:启用:ENABLE,0:未启用:NOT_ENABLED#")
    private ProductStatusEnum productStatus;

    @ApiModelProperty("#分类层级# 1:机械大类:CONSTRUCTION_MACHINERY,2:机型:EQUIPMENT,3:设备号:EQUIPMENT_MODEL")
    private ProductTypeEnum productType;

    @ApiModelProperty("#创建人#")
    private String creator;

    @ApiModelProperty("#修改人#")
    private String modifier;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("#产品业务编码#")
    private String productBusinessCode;

    @ApiModelProperty("#机械大类/机型#")
    private String supProductName;

    @ApiModelProperty("#发布状态#")
    private ReleaseStatusEnum releaseStatus;

    @ApiModelProperty(value = "#总线拓扑结构ID#")
    private Long topologyId;

    @ApiModelProperty(value = "#描述信息#")
    private String desc;

    @ApiModelProperty(value = "#总线拓扑结构版本#")
    private Integer topologyVersion;

    @ApiModelProperty("#租户ID#")
    private String tenantId;

    @ApiModelProperty(value = "监管类型 0:工程机械:ENGINEERING_MACHINES 1:非工程机械:NON_ENGINEERING_MACHINES 2:其它:OTHERS")
    private RegisterTypeEnum registerType;
}
