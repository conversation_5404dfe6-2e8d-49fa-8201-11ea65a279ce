package com.rootcloud.ids.ucenter.vo.rest.base;

import com.rootcloud.esmp.common.enums.RegisterTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @author: ling.kang
 * @create: 2022-02-25 15:05
 **/
@Data
@ApiModel(value = "DbcAttributeParam", description = "机型属性更改")
public class BaseProductUpParam implements Serializable {

    private static final long serialVersionUID = -7340278387128511958L;

    @ApiModelProperty("#分类id#")
    private Long id;

    @ApiModelProperty("#分类名称#")
    @NotBlank(message = "productName不能为空")
    private String productName;

    @ApiModelProperty("#描述#")
    private String desc;

    @ApiModelProperty("#topologyId#")
    private Long topologyId;

    @ApiModelProperty(value = "监管类型 0:工程机械:ENGINEERING_MACHINES 1:非工程机械:NON_ENGINEERING_MACHINES 2:其它:OTHERS")
    private RegisterTypeEnum registerType;

}
