package com.rootcloud.ids.ucenter.service.backup.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.rootcloud.ids.ucenter.dto.backup.SysAppCondition;
import com.rootcloud.ids.ucenter.dto.backup.SysAppSaveDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysAppUpdateDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterApp;
import com.rootcloud.ids.ucenter.dao.backup.UcenterAppMapper;
import com.rootcloud.ids.ucenter.service.backup.IUcenterAppService;
import com.rootcloud.ids.ucenter.vo.backup.SysAppQueryVO;
import com.rootcloud.ids.ucenter.vo.backup.SysAppVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 系统应用服务实现类
 * @ClassName SysAppServiceImpl
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Service
public class UcenterAppServiceImpl extends ServiceImpl<UcenterAppMapper, UcenterApp> implements
    IUcenterAppService {

    @Override
    public Long save(SysAppSaveDTO appSaveDTO) {
        UcenterApp app = new UcenterApp();
        BeanUtil.copyProperties(appSaveDTO, app);
        this.save(app);
        return app.getAppId();
    }

    @Override
    public Boolean update(SysAppUpdateDTO appUpdateDTO) {
        UcenterApp app = new UcenterApp();
        BeanUtil.copyProperties(appUpdateDTO, app);
        return this.updateById(app);
    }

    @Override
    public Boolean batchDelete(Long[] appIds) {
        return this.removeByIds(Convert.toList(Long.class, appIds));
    }

    @Override
    public SysAppVO findById(Long appId) {
        SysAppVO appVO = new SysAppVO();
        UcenterApp app = this.getById(appId);
        BeanUtil.copyProperties(app, appVO);
        return appVO;
    }

    @Override
    public PageInfo<SysAppQueryVO> query(SysAppCondition cnd) {
        PageHelper.startPage(cnd.getPage(), cnd.getPageSize());
        List<SysAppQueryVO> appVOList = this.baseMapper.query(cnd);
        PageInfo<SysAppQueryVO> pageInfo = new PageInfo(appVOList);
        return pageInfo;
    }
}
