package com.rootcloud.ids.ucenter.utils.convertor.base;

import com.rootcloud.ids.ucenter.entity.base.BaseProduct;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDeptEquipmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseProductDetailResp;
import com.rootcloud.esmp.common.dto.cache.BaseProductResp;
import com.rootcloud.ids.ucenter.vo.resp.openapi.BaseProductApiResp;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @since 2022/3/1 3:25 下午
 */
public class BaseProductRespConvertor {


    public static BaseDeptEquipmentResp change2BaseDeptEquipmentResp(BaseProduct entity) {
        BaseDeptEquipmentResp resp = null;

        if (entity != null) {
            resp = new BaseDeptEquipmentResp();
            resp.setConMacId(entity.getParentId());
            resp.setEquipmentId(entity.getId());
            resp.setEquipmentName(entity.getProductName());
            resp.setTenantId(entity.getTenantId());
        }
        return resp;
    }

    public static List<BaseProductApiResp> change2ProductApiRespList(Iterator<BaseProduct> entityList) {
        List<BaseProductApiResp> list = new ArrayList<>();
        if (entityList != null && entityList.hasNext()) {
            entityList.forEachRemaining(entity -> list.add(change2ProductApiResp(entity)));
        }
        return list;
    }

    private static BaseProductApiResp change2ProductApiResp(BaseProduct entity) {
        BaseProductApiResp resp = null;
        if (entity != null) {
            resp = new BaseProductApiResp();
            resp.setId(entity.getId());
            resp.setParentId(entity.getParentId());
            resp.setProductName(entity.getProductName());
            resp.setProductStatus(entity.getProductStatus());
            resp.setProductType(entity.getProductType());
            resp.setReleaseStatus(entity.getReleaseStatus());
            resp.setTenantId(entity.getTenantId());
            resp.setProductCode(entity.getProductCode());
        }
        return resp;
    }

    public static List<BaseProductResp> change2ProductRespList(Iterator<BaseProduct> entityList) {
        List<BaseProductResp> list = new ArrayList<>();
        if (entityList != null && entityList.hasNext()) {
            entityList.forEachRemaining(entity -> list.add(change2ProductResp(entity)));
        }
        return list;
    }

    public static BaseProductResp change2ProductResp(BaseProduct entity) {
        BaseProductResp resp = null;

        if (entity != null) {
            resp = new BaseProductResp();
            resp.setId(Optional.ofNullable(entity.getId()).map(String::valueOf).orElse(null));
            resp.setParentId(Optional.ofNullable(entity.getParentId()).map(String::valueOf).orElse(null));
            resp.setProductName(entity.getProductName());
            resp.setProductStatus(entity.getProductStatus());
            resp.setProductType(entity.getProductType());
            resp.setCreator(entity.getCreator());
            resp.setModifier(entity.getModifier());
            resp.setProductCode(entity.getProductCode());
            resp.setCreateTime(entity.getCreateTime());
            resp.setUpdateTime(entity.getUpdateTime());
            resp.setReleaseStatus(entity.getReleaseStatus());
            resp.setHasPublished(Optional.ofNullable(entity.getHasPublished()).map(i -> i.intValue() == 1).orElse(false));
            resp.setProductBusinessCode(entity.getProductBusinessCode());
            resp.setTenantId(entity.getTenantId());
        }
        return resp;
    }


    public static BaseProductDetailResp changeBaseProductDetailResp(BaseProduct entity) {
        BaseProductDetailResp resp = null;

        if (entity != null) {
            resp = new BaseProductDetailResp();
            resp.setId(entity.getId());
            resp.setParentId(entity.getParentId());
            resp.setProductName(entity.getProductName());
            resp.setProductStatus(entity.getProductStatus());
            resp.setProductType(entity.getProductType());
            resp.setModifier(entity.getModifier());
            resp.setCreator(entity.getCreator());
            resp.setCreateTime(entity.getCreateTime());
            resp.setUpdateTime(entity.getUpdateTime());
            resp.setProductBusinessCode(entity.getProductBusinessCode());
            resp.setReleaseStatus(entity.getReleaseStatus());
            resp.setTopologyId(entity.getTopologyId());
            resp.setDesc(entity.getDesc());
            resp.setTopologyVersion(entity.getTopologyVersion());
            resp.setRegisterType(entity.getRegisterType());
            resp.setTenantId(entity.getTenantId());
        }
        return resp;
    }
}
