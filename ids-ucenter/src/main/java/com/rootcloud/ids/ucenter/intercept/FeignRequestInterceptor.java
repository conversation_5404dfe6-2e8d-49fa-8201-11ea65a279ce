/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.ids.ucenter.intercept;

import static com.rootcloud.esmp.common.Constants.LOCALE_HEADER;
import static com.rootcloud.ids.common.core.constant.GlobalConstants.API_REQUEST_ID_HEADER;
import static com.rootcloud.ids.common.core.constant.GlobalConstants.LOG_REQUEST_ID;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import com.rootcloud.esmp.common.utils.OptionalX;
import com.rootcloud.ids.common.web.config.LanguageLocalConfig;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class FeignRequestInterceptor implements RequestInterceptor {

  @Override
  public void apply(RequestTemplate requestTemplate) {
    try {
      String requestId = MDC.get(LOG_REQUEST_ID);
      if (StringUtils.isBlank(requestId)) {
        requestId = UUID.randomUUID().toString().replaceAll("-", "");
      }
      requestTemplate.header(API_REQUEST_ID_HEADER, requestId);
      final String language = I18nUtil.getCurrentLanguage();
      requestTemplate.header(LOCALE_HEADER, language);

      log.info("开始请求url:{}", requestTemplate.path());
      log.info("Header: {}", JSONUtil.toJsonStr(requestTemplate.headers()));
      log.info("Query: {}", JSONUtil.toJsonStr(requestTemplate.method()));
      log.info("Body: {}", OptionalX.ofNullable(requestTemplate.body()).map(String::new).orElse(null));
    } catch (Exception e) {
      log.error("输出Feign日志失败: " + e.getMessage(), e);
    }
  }
}
