package com.rootcloud.ids.ucenter.vo.backup;

import com.rootcloud.ids.common.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 系统用户Query VO对象
 * @ClassName SysUserQueryVO
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "系统用户Query VO对象")
@Getter
@Setter
public class SysUserQueryVO extends BaseVO {

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "真实姓名")
    private String userName;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "用户性别 1男，0女，2其它")
    private Integer sex;

    @ApiModelProperty(value = "用户状态 10正常，5冻结，0停用")
    private Integer state;

    @ApiModelProperty(value = "电子邮箱")
    private String email;
}
