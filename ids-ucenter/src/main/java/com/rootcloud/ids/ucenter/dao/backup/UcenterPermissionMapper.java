package com.rootcloud.ids.ucenter.dao.backup;

import com.rootcloud.ids.ucenter.entity.backup.UcenterPermission;
import com.rootcloud.ids.ucenter.vo.backup.SysPermissionVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 权限Mapper接口
 * @ClassName SysPermissionMapper
 * <AUTHOR>
 * @Date 2021/12/21
 * @Version 1.0
 */
public interface UcenterPermissionMapper extends BaseMapper<UcenterPermission> {

    /**
     * 根据用户ID查询用户角色所有权限
     * @param userId
     * @return
     */
    List<SysPermissionVO> findRolePermissionByUserId(@Param(value = "userId") Long userId);

    /**
     * 根据用户ID查询用户组织所有权限
     * @param userId
     * @return
     */
    List<SysPermissionVO> findGroupPermissionByUserId(@Param(value = "userId") Long userId);

    /**
     * 根据用户ID查询用户单独配置的所有权限
     * @param userId
     * @return
     */
    List<SysPermissionVO> findUserPermissionByUserId(@Param(value = "userId") Long userId);



}
