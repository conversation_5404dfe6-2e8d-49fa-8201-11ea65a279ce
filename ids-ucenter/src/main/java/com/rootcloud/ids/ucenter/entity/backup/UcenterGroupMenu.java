package com.rootcloud.ids.ucenter.entity.backup;

import com.rootcloud.ids.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 组织-菜单关联对象
 * @ClassName SysGroupMenu
 * <AUTHOR>
 * @Date 2021/12/25
 * @Version 1.0
 */
@Getter
@Setter
@TableName("ucenter_group_menu")
public class UcenterGroupMenu extends BaseEntity {

    /**
     * 组织ID
     */
    private Long groupId;
    /**
     * 菜单ID
     */
    private Long menuId;
}
