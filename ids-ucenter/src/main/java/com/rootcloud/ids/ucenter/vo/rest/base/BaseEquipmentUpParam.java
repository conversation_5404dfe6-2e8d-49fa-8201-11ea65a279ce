package com.rootcloud.ids.ucenter.vo.rest.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: ling.kang
 * @create: 2022-02-25 15:53
 **/
@Data
@ApiModel(value = "DbcAttributeParam", description = "设备号属性更改")
public class BaseEquipmentUpParam implements Serializable {

    private static final long serialVersionUID = -8703742263618295400L;

    @ApiModelProperty("#机型id#")
    private Long id;

    @ApiModelProperty("#型号名称#")
    private String productName;
}
