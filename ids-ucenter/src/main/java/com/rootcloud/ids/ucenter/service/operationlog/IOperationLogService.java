package com.rootcloud.ids.ucenter.service.operationlog;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rootcloud.ids.ucenter.entity.operationlog.OperationLog;
import com.rootcloud.ids.ucenter.enums.operationlog.UcenterOperationModuleEnum;
import com.rootcloud.ids.ucenter.enums.operationlog.UcenterOperationTypeEnum;
import com.rootcloud.ids.ucenter.vo.rest.operationlog.OperationLogPageParam;
import com.rootcloud.ids.ucenter.vo.resp.operationlog.OperationLogResp;

/**
 * <p>
 * 操作日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
public interface IOperationLogService extends IService<OperationLog> {
    /**
     * description: 分页查询操作日志
     *
     * @param param
     */
    Page<OperationLogResp> pageByParam(OperationLogPageParam param);

    /**
     *
     * @param serviceId
     * @param operationModule
     * @param operationType
     * @param logContent
     * @param logDetail
     * @param creator
     * @param result
     */
    void saveOperationLog(String serviceId, UcenterOperationModuleEnum operationModule,
                          UcenterOperationTypeEnum operationType, String logContent, String logDetail, String creator, String result);

}
