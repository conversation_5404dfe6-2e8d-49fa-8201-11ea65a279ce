package com.rootcloud.ids.ucenter.vo.resp.base;

import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-02-28 14:26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BaseOrganizationResp", description = "组织列表返回对象")
public class BaseOrganizationResp implements Serializable {

    private static final long serialVersionUID = -6418737894934284498L;

    @ApiModelProperty("#组织ID#")
    private String id;

    @ApiModelProperty("#组织名称#")
    private String name;

    @ApiModelProperty("#部门类型#")
    private DeptTypeEnum deptType;

    @ApiModelProperty("#部门树结构#")
    private List<BaseDepartmentResp> deptList;

    public BaseOrganizationResp(String id, String name, DeptTypeEnum deptType) {
        this.id = id;
        this.name = name;
        this.deptType = deptType;
    }
}
