package com.rootcloud.ids.ucenter.service.base.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rootcloud.ids.ucenter.dao.base.BaseDeptProRelMapper;
import com.rootcloud.ids.ucenter.entity.base.BaseDeptProRel;
import com.rootcloud.ids.ucenter.enums.base.AuthTypeEnum;
import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import com.rootcloud.ids.ucenter.service.base.IBaseDepartmentService;
import com.rootcloud.ids.ucenter.service.base.IBaseDeptProRelService;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDepartmentResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseDeptEquipmentDelRest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 产品分类-部门-关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Service
public class BaseDeptProRelServiceImpl extends ServiceImpl<BaseDeptProRelMapper, BaseDeptProRel> implements IBaseDeptProRelService {
    @Autowired
    @Lazy
    private IBaseDepartmentService baseDepartmentService;

    @Override
    public List<BaseDeptProRel> listByDeptIdsAndDeptType(List<String> ids, DeptTypeEnum deptType) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<BaseDeptProRel> wrapper = new LambdaQueryWrapper<>();
        if (ids.size() == 1) {
            wrapper.eq(BaseDeptProRel::getDeptId, ids.get(0));
        } else {
            wrapper.in(BaseDeptProRel::getDeptId, ids);
        }
        wrapper.eq(deptType != null, BaseDeptProRel::getDeptType, deptType);
        return this.list(wrapper);
    }

    @Override
    public List<BaseDeptProRel> listOrganisation(Set<Long> productIds) {
        if (CollUtil.isEmpty(productIds)) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.lambdaQuery(BaseDeptProRel.class)
                .eq(BaseDeptProRel::getDeptType, DeptTypeEnum.ORGANIZATION)
                .in(BaseDeptProRel::getProductId, productIds));
    }

    @Override
    public List<BaseDeptProRel> listNoInByCidList(List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<BaseDeptProRel> wrapper = new LambdaQueryWrapper<>();
        if (ids.size() == 1) {
            wrapper.ne(BaseDeptProRel::getCid, ids.get(0));
        } else {
            wrapper.notIn(BaseDeptProRel::getCid, ids);
        }
        return this.list(wrapper);
    }

    @Override
    public List<BaseDeptProRel> listByDeptType(DeptTypeEnum deptType) {
        LambdaQueryWrapper<BaseDeptProRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseDeptProRel::getDeptType, deptType);
        return this.list(wrapper);
    }

    @Override
    public List<BaseDeptProRel> listByProductIdsAndDeptTypeAndAuthType(Collection<Long> equipmentIds, DeptTypeEnum deptType, AuthTypeEnum authType) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<BaseDeptProRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BaseDeptProRel::getProductId, equipmentIds);
        wrapper.eq(BaseDeptProRel::getDeptType, deptType);
        wrapper.eq(BaseDeptProRel::getAuthType, authType);
        return this.list(wrapper);
    }

    @Override
    public BaseDeptProRel getByProductIdAndAuthType(Long equipmentId, AuthTypeEnum authType) {
        LambdaQueryWrapper<BaseDeptProRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseDeptProRel::getProductId, equipmentId);
        wrapper.eq(BaseDeptProRel::getAuthType, authType);
        return this.getOne(wrapper);
    }

    @Override
    public List<BaseDeptProRel> listByProductIdAndAuthType(List<Long> equipmentIds, AuthTypeEnum authType) {
        LambdaQueryWrapper<BaseDeptProRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BaseDeptProRel::getProductId, equipmentIds);
        wrapper.eq(BaseDeptProRel::getAuthType, authType);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByEquipmentIdAndCid(Long equipmentId, List<String> cidList) {
        LambdaQueryWrapper<BaseDeptProRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseDeptProRel::getProductId, equipmentId);
        wrapper.in(BaseDeptProRel::getCid, cidList);

        // 清除组织下所有部门关于此机型的权限
        List<BaseDepartmentResp> departmentResps = new ArrayList<>();
        cidList.stream().distinct().forEach(cid -> departmentResps.addAll(baseDepartmentService.listDepartment(true, cid)));
        departmentResps.forEach(departmentResp -> {
            BaseDeptEquipmentDelRest delRest = new BaseDeptEquipmentDelRest();
            delRest.setCid(departmentResp.getCid());
            delRest.setEquipmentId(equipmentId);
            delRest.setDeptId(departmentResp.getId());
            baseDepartmentService.removeEquipment(delRest);
        });

        return this.remove(wrapper);
    }

    @Override
    public List<BaseDeptProRel> listByCidAndProductId(String cid, Long productId) {
        LambdaQueryWrapper<BaseDeptProRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseDeptProRel::getProductId, productId);
        wrapper.eq(BaseDeptProRel::getCid, cid);
        return this.list(wrapper);
    }
}
