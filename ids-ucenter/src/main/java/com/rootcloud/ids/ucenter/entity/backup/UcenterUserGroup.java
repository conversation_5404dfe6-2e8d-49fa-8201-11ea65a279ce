package com.rootcloud.ids.ucenter.entity.backup;

import com.rootcloud.ids.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 用户-组织关联对象
 * @ClassName SysUserGroup
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Getter
@Setter
@TableName("ucenter_user_group")
public class UcenterUserGroup extends BaseEntity {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 组织ID
     */
    private Long groupId;

}
