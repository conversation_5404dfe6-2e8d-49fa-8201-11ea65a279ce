package com.rootcloud.ids.ucenter.controller.base;


import com.rootcloud.esmp.common.dto.cache.BaseProductResp;
import com.rootcloud.esmp.common.enums.PermissionQueryEnum;
import com.rootcloud.ids.common.core.result.PageResult;
import com.rootcloud.ids.common.core.result.Result;
import com.rootcloud.ids.ucenter.service.base.IBaseUserService;
import com.rootcloud.ids.ucenter.service.redis.IRedisLoginService;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDeptEquipmentResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseUserEquipmentAddRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseUserEquipmentRemoveRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseUserPossessPageRest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Api(value = "BaseUserController", tags = "[BASE]用户数据权限")
@Slf4j
@RestController
@RequestMapping("/api/base/user")
public class BaseUserController {

    @Autowired
    private IBaseUserService iBaseUserService;

    @Autowired
    private IRedisLoginService iRedisLoginService;

    @ApiOperation(value = "用户拥有的机型权限列表分页查询", notes = "用户拥有的机型权限列表分页查询")
    @GetMapping(value = "/pagePossess")
    public PageResult<BaseDeptEquipmentResp> pagePossess(@Valid BaseUserPossessPageRest param) {
        log.info("pagePossess:{}", param);
        param.setPossess(true);
        return PageResult.success(this.iBaseUserService.pagePossess(param));
    }

    @ApiOperation(value = "用户未拥有的机型权限列表分页查询", notes = "用户未拥有的机型权限列表分页查询")
    @GetMapping(value = "/pageNoPossess")
    public PageResult<BaseDeptEquipmentResp> pageNoPossess(@Valid BaseUserPossessPageRest param) {
        log.info("pageNoPossess:{}", param);
        param.setPossess(false);
        return PageResult.success(this.iBaseUserService.pagePossess(param));
    }

    @ApiOperation(value = "用户拥有的机型权限列表", notes = "用户拥有的机型权限列表")
    @GetMapping(value = "/listPossess")
    public Result<List<BaseProductResp>> listPossess(
        @RequestParam(value = "isCrossTenant", required = false) @ApiParam(name = "isCrossTenant", value = "是否跨组织查询数据，默认跨组织，废弃，请使用permissionQuery参数") Boolean isCrossTenant,
        @RequestParam(value = "permissionQuery", required = false) @ApiParam(name = "permissionQuery", value = "#查询数据范围，默认：CROSS_TENANT，CROSS_TENANT: 查询有权限的所有组织数据；CURRENT_TENANT: 查询当前组织数据；DELEGATED_TENANT：查询授权给本组织的数据#") PermissionQueryEnum permissionQuery) {
        log.info("用户拥有的机型权限列表获取-开始");

        PermissionQueryEnum query = Optional.ofNullable(permissionQuery)
            .orElseGet(() -> BooleanUtils.isFalse(isCrossTenant) ? PermissionQueryEnum.CURRENT_TENANT : PermissionQueryEnum.CROSS_TENANT);

        List<BaseProductResp> baseProductResps = iBaseUserService.listPossess(query);
        log.info("用户拥有的机型权限列表获取-结束");
        return Result.success(baseProductResps);
    }

    @ApiOperation(value = "用户单租户拥有的机型权限列表", notes = "用户单租户拥有的机型权限列表")
    @GetMapping(value = "/listTenantPossess")
    public Result<List<BaseProductResp>> listTenantPossess() {
        return Result.success(iBaseUserService.listPossess(PermissionQueryEnum.CURRENT_TENANT));
    }

    @ApiOperation(value = "添加权限", notes = "添加权限")
    @PostMapping(value = "/addAuthority")
    public Result<Boolean> addAuthority(@RequestBody @Valid BaseUserEquipmentAddRest param) {
        log.info("addAuthority:{}", param);
        return Result.success(this.iBaseUserService.addAuthority(param));
    }

    @ApiOperation(value = "移除权限", notes = "移除权限")
    @PostMapping(value = "/removeAuthority")
    public Result<Boolean> removeAuthority(@RequestBody @Valid BaseUserEquipmentRemoveRest param) {
        log.info("removeAuthority:{}", param);
        return Result.success(this.iBaseUserService.removeAuthority(param));
    }

    @ApiOperation(value = "数据权限（已有权限）-过滤id", notes = "数据权限（已有权限）-过滤id")
    @GetMapping(value = "/pagePossessIds")
    public Result<Collection<Long>> pagePossessIds(@Valid BaseUserPossessPageRest param) {
        log.info("pagePossessIds:{}", param);
        param.setPossess(true);
        return Result.success(this.iBaseUserService.pagePossessIds(param));
    }

    @ApiOperation(value = "数据权限（添加权限）-过滤id", notes = "数据权限（添加权限）-过滤id")
    @GetMapping(value = "/pageNoPossessIds")
    public Result<Collection<Long>> pageNoPossessIds(@Valid BaseUserPossessPageRest param) {
        log.info("pageNoPossessIds:{}", param);
        param.setPossess(false);
        return Result.success(this.iBaseUserService.pagePossessIds(param));
    }

    @ApiOperation(value = "刷新Redis缓存信息", notes = "刷新Redis缓存信息")
    @PatchMapping(value = "/refreshRedisCache")
    public Result<Boolean> refreshRedisCache() {
        return Result.success(iRedisLoginService.refreshUserCached());
    }

    //@ApiOperation(value = "获得Redis缓存信息", notes = "获得Redis缓存信息")
    //@GetMapping(value = "/getRedisCache")
    //public Result<TokenAndOrganizationUserCachedDTO> getRedisCache() {
    //    return Result.success(SecurityUtils.getTokenAndOrganizationUserCachedDTO());
    //}

}
