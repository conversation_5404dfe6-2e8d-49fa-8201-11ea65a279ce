package com.rootcloud.ids.ucenter.controller.base;


import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.rootcloud.ids.common.core.result.PageResult;
import com.rootcloud.ids.common.core.result.Result;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import com.rootcloud.ids.common.web.exception.BizException;
import com.rootcloud.esmp.iam.dto.permission.IamResourcePropertyValueDto;
import com.rootcloud.esmp.iam.dto.permission.IamResourcePropertyValueQueryDto;
import com.rootcloud.esmp.iam.dto.permission.PagingResult;
import com.rootcloud.esmp.common.enums.ProductTypeEnum;
import com.rootcloud.ids.ucenter.service.base.IBaseProductService;
import com.rootcloud.ids.ucenter.utils.Constants;
import com.rootcloud.ids.ucenter.utils.convertor.base.BaseProductRespConvertor;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseEquipmentModelResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseEquipmentPageResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseProductDetailResp;
import com.rootcloud.esmp.common.dto.cache.BaseProductResp;
import com.rootcloud.ids.ucenter.vo.rest.base.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品分类
 *
 * <AUTHOR>
 * @since 2022-02-24
 */
@Api(value = "BaseProductController", tags = "[BASE]产品分类")
@Slf4j
@RestController
@RequestMapping("/api/base/product")
public class BaseProductController {

    @Autowired
    private IBaseProductService baseProductService;

    @ApiOperation(value = "产品分类结构列表", notes = "产品分类结构列表")
    @GetMapping(value = "/list")
    public Result<List<BaseProductResp>> listProduct() {
        return Result.success(baseProductService.listProduct());
    }

    @ApiOperation(value = "产品分类结构列表-条件检索", notes = "产品分类结构列表-条件检索")
    @PostMapping(value = "/listByParam")
    public Result<List<BaseProductResp>> listByParam(@RequestBody  ProductListParam param) {
        return Result.success(baseProductService.listByParam(param));
    }

    @ApiOperation(value = "产品分类权限结构列表", notes = "产品分类权限结构列表")
    @GetMapping(value = "/listAuth")
    public Result<List<BaseProductResp>> listAuthProduct() {
        return Result.success(baseProductService.listAuthProduct());
    }

    @ApiOperation(value = "产品分类(机械大类、机型、设备号)详情", notes = "产品分类(机械大类、机型、设备号)详情")
    @GetMapping(value = "/getById")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", defaultValue = "", value = "产品ID", required = true),
    })
    public Result<BaseProductDetailResp> getById(@RequestParam("id") @NotNull(message = "产品ID不允许为空") Long id) {
        log.info("getById:{}", id);
        return Result.success(baseProductService.getInfo(id));
    }

    @ApiOperation(value = "产品分类(机械大类、机型、设备号)删除", notes = "产品分类(机械大类、机型、设备号)删除")
    @DeleteMapping(value = "/delById")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", defaultValue = "", value = "产品ID", required = true),
    })
    public Result delById(@RequestParam("id") @NotNull(message = "产品ID不允许为空") Long id) {
        log.info("getById:{}", id);
        return Result.success(baseProductService.del(id));
    }

    @ApiOperation(value = "新增机型", notes = "新增机型")
    @PostMapping(value = "/add")
    public Result<Long> add(@RequestBody @Valid BaseProductParam param) {
        log.info("add:{}", param);
        checkProductName(param.getProductName());
        return Result.success(baseProductService.add(param));
    }

    @ApiOperation(value = "新增设备型号", notes = "新增设备型号")
    @PostMapping(value = "/addEquipment")
    public Result<Long> addEquipment(@RequestBody @Valid BaseProEquipmentParam param) {
        log.info("addEquipment:{}", param);
        checkProductName(param.getProductName());
        return Result.success(baseProductService.addEquipment(param));
    }

    @ApiOperation(value = "产品分类名称(机械大类、机型、设备号)更改", notes = "产品分类名称(机械大类、机型、设备号)更改")
    @PostMapping(value = "/updateName")
    public Result<Boolean> updateName(@RequestBody @Valid BaseProductUpParam param) {
        log.info("updateName:{}", param);
        checkProductName(param.getProductName());
        return Result.success(baseProductService.updateById(param));
    }

    @ApiOperation(value = "设备型号列表分页查询", notes = "设备型号列表分页查询")
    @PostMapping(value = "/pageModelByParam")
    public PageResult<BaseEquipmentModelResp> pageModelByParam(
            @RequestBody @Valid BaseEquipmentModelPageParam param) {
        log.info("pageModelByParam:{}", param);
        return PageResult.success(baseProductService.pageModelByParam(param));
    }

    @ApiOperation(value = "设备型号列表分页查询-单租户", notes = "设备型号列表分页查询-单租户")
    @PostMapping(value = "/pageModelByParam2")
    public PageResult<BaseEquipmentModelResp> pageModelByParam2(
            @RequestBody @Valid BaseEquipmentModelPageParam param) {
        log.info("pageModelByParam:{}", param);
        return PageResult.success(baseProductService.pageModelByParam2(param));
    }

    @ApiOperation(value = "机型列表分页查询", notes = "机型列表分页查询")
    @PostMapping(value = "/pageEquipmentByParam")
    public PageResult<BaseEquipmentPageResp> pageEquipmentByParam(
            @RequestBody @Valid BaseEquipmentPageParam param) {
        log.info("pageEquipmentByParam:{}", param);
        return PageResult.success(baseProductService.pageEquipmentByParam(param));
    }

    @ApiOperation(value = "设备型号列表", notes = "设备型号列表")
    @GetMapping(value = "/listModel")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", defaultValue = "", value = "产品机型ID", required = true),
    })
    public Result<List<BaseProductResp>> listModel(@RequestParam("equipmentId") Long equipmentId) {
        log.info("listModel:{}", equipmentId);
        return Result.success(baseProductService.listModel(equipmentId));
    }

    @ApiOperation(value = "所有机械大类", notes = "所有机械大类")
    @GetMapping(value = "/listMachinery")
    public Result<List<BaseProductResp>> listMachinery() {
        return Result.success(baseProductService.allConstructionMachinery().stream().map(BaseProductRespConvertor::change2ProductResp).collect(Collectors.toList()));
    }

    @ApiOperation(value = "设备型号发布状态变更", notes = "设备型号发布状态变更")
    @PostMapping(value = "/equipmentModelRelease")
    public Result<Boolean> equipmentModelRelease(@RequestBody @Valid BaseEquipmentModelReleaseParam param) {
        log.info("equipmentModelRelease:{}", param);
        return Result.success(baseProductService.equipmentModelRelease(param));
    }

    @ApiOperation(value = "更新设备型号对应topologyVersion", notes = "更新设备型号对应topologyVersion")
    @PostMapping(value = "/updateEquipmentModel")
    public Result<Boolean> updateEquipmentModel(@RequestBody @Valid BaseEquipmentModelUpdateParam param) {
        log.info("updateEquipmentModel:{}", param);
        return Result.success(baseProductService.updateEquipmentModel(param));
    }

    /*    @ApiOperation(value = "产品分类(机械大类、机型、设备号)删除", notes = "产品分类(机械大类、机型、设备号)删除")
    @PostMapping(value = "/remove")
    public Result<Boolean> deleteById(@RequestBody BaseProductDelParam param) {
        log.info("remove param:{}", param);
        return Result.success(baseProductService.del(param.getId()));
    }*/

    @ApiOperation(value = "机型维护对应筛选后的设备类型id列表", notes = "pageEquipmentByParam-listMachineryIds")
    @PostMapping(value = "/listMachineryIds")
    public Result<Collection<Long>> listMachinery(@RequestBody @Valid BaseEquipmentPageParam param) {
        log.info("listMachineryIds:{}", param);
        return Result.success(baseProductService.pageEquipmentByParamFilterIds(param));
    }

    @ApiOperation(value = "设备型号-请选择机型下拉框id列表", notes = "设备型号-请选择机型下拉框id列表")
    @PostMapping(value = "/listEquipmentIds")
    public Result<Collection<Long>> listEquipmentIds(@RequestBody @Valid BaseEquipmentModelPageParam param) {
        log.info("listEquipmentIds:{}", param);
        param.setAcrossTheTenant(true);
        return Result.success(baseProductService.pageModelByParamFilterIds(param));
    }

    @ApiOperation(value = "单租户设备型号-请选择机型下拉框id列表", notes = "单租户设备型号-请选择机型下拉框id列表")
    @PostMapping(value = "/listTenantEquipmentIds")
    public Result<Collection<Long>> listTenantEquipmentIds(@RequestBody @Valid BaseEquipmentModelPageParam param) {
        log.info("listTenantEquipmentIds:{}", param);
        param.setAcrossTheTenant(false);
        return Result.success(baseProductService.pageModelByParamFilterIds(param));
    }

    @ApiOperation(value = "设备型号-请选择设备类型下拉框id列表", notes = "设备型号-请选择机型下拉框id列表")
    @PostMapping(value = "/listEquipmentMachineryIds")
    public Result<Collection<Long>> listEquipmentMachineryIds(@RequestBody @Valid BaseEquipmentModelPageParam param) {
        log.info("listEquipmentMachineryIds:{}", param);
        param.setAcrossTheTenant(true);
        return Result.success(baseProductService.pageModelByParamFilterDeviceTypeIds(param));
    }


    @ApiOperation(value = "单租户设备型号-请选择设备类型下拉框id列表", notes = "单租户设备型号-请选择机型下拉框id列表")
    @PostMapping(value = "/listEquipmentMachineryId")
    public Result<Collection<Long>> listEquipmentMachineryId(@RequestBody @Valid BaseEquipmentModelPageParam param) {
        log.info("listEquipmentMachineryId:{}", param);
        param.setAcrossTheTenant(false);
        return Result.success(baseProductService.pageModelByParamFilterDeviceTypeIds(param));
    }

    /**
     * 校验名称长度
     */
    private void checkProductName(String name) {
        if (StringUtils.isBlank(name) || name.length() > Constants.MAX) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100105));
        }
    }

}
