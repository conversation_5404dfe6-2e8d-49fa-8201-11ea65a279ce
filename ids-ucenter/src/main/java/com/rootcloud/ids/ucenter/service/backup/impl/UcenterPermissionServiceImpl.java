package com.rootcloud.ids.ucenter.service.backup.impl;

import cn.hutool.core.bean.BeanUtil;
import com.rootcloud.ids.ucenter.dto.backup.SysPermissionSaveDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysPermissionUpdateDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterPermission;
import com.rootcloud.ids.ucenter.dao.backup.UcenterPermissionMapper;
import com.rootcloud.ids.ucenter.service.backup.IUcenterPermissionService;
import com.rootcloud.ids.ucenter.vo.backup.SysPermissionVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.HashSet;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.condition.PatternsRequestCondition;
import org.springframework.web.servlet.mvc.condition.RequestMethodsRequestCondition;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description 系统权限服务实现类
 * @ClassName ISysPermissionServiceImpl
 * <AUTHOR>
 * @Date 2021/12/26
 * @Version 1.0
 */
@Service
public class UcenterPermissionServiceImpl extends ServiceImpl<UcenterPermissionMapper, UcenterPermission> implements
    IUcenterPermissionService {

    @Autowired
    private WebApplicationContext applicationContext;

    @Override
    public Long save(SysPermissionSaveDTO permissionSaveDTO) {
        UcenterPermission permission = new UcenterPermission();
        BeanUtil.copyProperties(permissionSaveDTO, permission);
        this.save(permission);
        return permission.getPermissionId();
    }

    @Override
    public Boolean update(SysPermissionUpdateDTO permissionUpdateDTO) {
        UcenterPermission permission = new UcenterPermission();
        BeanUtil.copyProperties(permissionUpdateDTO, permission);
        return this.updateById(permission);
    }

    @Override
    public SysPermissionVO findById(Long permissionId) {
        SysPermissionVO permissionVO = new SysPermissionVO();
        UcenterPermission permission = this.getById(permissionId);
        BeanUtil.copyProperties(permission, permissionVO);
        return permissionVO;
    }

    @Override
    public List<SysPermissionVO> findPermissionByUserId(Long userId) {
        Set<SysPermissionVO> sysPermissionVOSet = new HashSet<SysPermissionVO>();
        //查询角色 对应的权限
        List<SysPermissionVO> rolePermissions = baseMapper.findRolePermissionByUserId(userId);
        //查询组织对应的权限
        List<SysPermissionVO> groupPermissions = baseMapper.findGroupPermissionByUserId(userId);
        //查询用户个人单独配置的权限
        List<SysPermissionVO> userPermissions = baseMapper.findUserPermissionByUserId(userId);
        //去重
        sysPermissionVOSet.addAll(rolePermissions);
        sysPermissionVOSet.addAll(groupPermissions);
        sysPermissionVOSet.addAll(userPermissions);
        return new ArrayList<>(sysPermissionVOSet);
    }

    @Transactional
    @Override
    public Boolean syncDB() throws ClassNotFoundException {
        RequestMappingHandlerMapping mapping = applicationContext.getBean(RequestMappingHandlerMapping.class);
        Map<RequestMappingInfo, HandlerMethod> mappingHandlerMethods = mapping.getHandlerMethods();

        List<UcenterPermission> list = new ArrayList();

        for (Map.Entry<RequestMappingInfo, HandlerMethod> map : mappingHandlerMethods.entrySet()) {
            UcenterPermission permission = new UcenterPermission();
            RequestMappingInfo info = map.getKey();
            HandlerMethod method = map.getValue();

            RequestMethodsRequestCondition methodsRequestCondition = info.getMethodsCondition();
            PatternsRequestCondition patternsCondition = info.getPatternsCondition();
            String className = method.getMethod().getDeclaringClass().getName();
            /**
             * 匹配包路径 根据自己的路径替换
             */
            if (className.contains("com.rootcloud.ids.ucenter.controller")) {
                //获取类对象
                Class clazz = Class.forName(method.getMethod().getDeclaringClass().getName());
                Api api = (Api) clazz.getAnnotation(Api.class);
                if(null != api){
                    permission.setModule(api.tags()[0]);
                }
                String metName = method.getMethod().getName();
                /**
                 * 因为单独获取一个类对象要指定参数，不适合批量使用，所以获取所有的方法然后根据name筛选
                 */
                Method[] clazzDeclaredMethods = clazz.getDeclaredMethods();
                Arrays.stream(clazzDeclaredMethods).forEach(
                        c -> {
                            if(c.getName().equals(metName)){
                                /* swagger注解 可以换成别的 */
                                ApiOperation annotation = c.getAnnotation(ApiOperation.class);
                                if(null != annotation){
                                    permission.setPermissionName(annotation.value());
                                }
                            }
                        }
                );
                if(patternsCondition != null) {
                    for (String url : patternsCondition.getPatterns()) {
                        permission.setPermissionValue(url);
                    }
                }
                for(RequestMethod methodType : methodsRequestCondition.getMethods()) {
                    permission.setMethod(methodType.toString());
                }
                list.add(permission);
            }
        }
        return this.saveBatch(list);
    }
}
