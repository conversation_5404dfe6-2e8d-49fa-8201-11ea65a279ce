package com.rootcloud.ids.ucenter.vo.rest.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-02-28 16:49
 **/
@Data
@ApiModel(value = "BaseOrganizeProAddRest", description = "权限组织授权")
public class BaseOrganizeProAddRest implements Serializable {

    private static final long serialVersionUID = 2967694873994066529L;
    @ApiModelProperty(value = "#机型ID#", required = true)
    @NotNull(message = "机型ID不能为空")
    private Long equipmentId;

    @ApiModelProperty("#组织id数组#")
    @NotNull(message = "cidList不能为空")
    private List<String> cidList;

}
