package com.rootcloud.ids.ucenter.vo.rest.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @author: ling.kang
 * @create: 2022-02-25 14:53
 **/
@Data
@ApiModel(value = "BaseProductParam", description = "机型新增参数")
public class BaseProductParam implements Serializable {

    private static final long serialVersionUID = -7793482116909492431L;

    @ApiModelProperty("#机械大类id#")
    @NotNull(message = "机械大类id不能为空")
    private Long id;

    @ApiModelProperty("#分类名称(机械大类名称或机型名称)#")
    @NotBlank(message = "productName不能为空")
    private String productName;

}
