package com.rootcloud.ids.ucenter.vo.rest.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author: kang.ling
 * @create: 2022-03-28 11:31
 **/
@Data
@ApiModel(value = "BaseDeptEquipmentAddDTO", description = "新增部门机型参数明细")
public class BaseDeptEquipmentAddDTO {

    @ApiModelProperty("#机型id#")
    @NotNull(message = "equipmentId不能为空")
    private Long equipmentId;

    @ApiModelProperty("#默认值 1:是 0:否#")
    @NotNull(message = "defaultFlag不能为空")
    private Integer defaultFlag;

}
