package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @Description 角色删除权限DTO
 * @ClassName SysRoleRemovePermissionDTO
 * <AUTHOR>
 * @Date 2021/12/21
 * @Version 1.0
 */
@ApiModel(value = "角色删除权限DTO")
@Getter
@Setter
public class SysRoleRemovePermissionDTO extends BaseDTO {

    @ApiModelProperty(value = "角色ID")
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @ApiModelProperty(value = "权限ID数组")
    @NotNull(message = "权限ID数组不能为空")
    private Long[] pmsIds;
}
