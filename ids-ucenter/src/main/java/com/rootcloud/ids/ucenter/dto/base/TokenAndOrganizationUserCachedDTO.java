package com.rootcloud.ids.ucenter.dto.base;

import com.rootcloud.esmp.common.dto.iam.organizationUser.OrganizeUserInfoDTO;
import com.rootcloud.esmp.common.dto.cache.UserDTO;
import com.rootcloud.esmp.common.dto.cache.BaseProductResp;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/3/5
 */
@Data
public class TokenAndOrganizationUserCachedDTO {

    /**
     * 用户个人信息
     */
    private UserDTO userInfo;

    /**
     * 用户组织信息
     */
    private OrganizeUserInfoDTO organizeUserInfo;

    /**
     * 用户机型权限信息 集合
     */
    private List<BaseProductResp> baseProductList;
}
