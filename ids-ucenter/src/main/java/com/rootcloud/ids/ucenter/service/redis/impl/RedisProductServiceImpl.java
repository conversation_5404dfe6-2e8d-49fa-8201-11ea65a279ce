package com.rootcloud.ids.ucenter.service.redis.impl;


import com.rootcloud.ids.common.redis.utils.RedisUtils;
import com.rootcloud.esmp.common.utils.SecurityUtils;
import com.rootcloud.ids.ucenter.service.redis.IRedisProductService;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @since 2022/2/28
 */
@Service
public class RedisProductServiceImpl extends RedisUtils implements IRedisProductService {

    private static final String PRODUCTLIST = "{PRODUCTLIST}:";

    @Override
    public boolean del() {
        String tenantId = SecurityUtils.getCurrentUser().getTenantId();
        String key = PRODUCTLIST+tenantId;
        super.del(key);
        return true;
    }
}
