package com.rootcloud.ids.ucenter.enums.base;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * @author: ling.kang
 * @create: 2022-02-28 15:01
 **/
public enum DeptTypeEnum implements IEnum<Integer> {
    /**
     * #组织类型 1:组织:ORGANIZATION,2:部门:DEPARTMENT#
     */
    ORGANIZATION(1, I18nCode.SYS_100027),
    DEPARTMENT(2, I18nCode.SYS_100013),
    ;
    private final int code;
    private final I18nCode label;

    DeptTypeEnum(int code, I18nCode label) {
        this.code = code;
        this.label = label;
    }

    public int getCode() {
        return code;
    }

    public String getLabel() {
        return I18nUtil.message(label);
    }

    public static String getLabel(Integer code) {
        if (code != null) {
            for (DeptTypeEnum value : DeptTypeEnum.values()) {
                if (value.code == code) {
                    return value.getLabel();
                }
            }
        }
        return null;
    }

    public static DeptTypeEnum codeOf(int code) {
        for (DeptTypeEnum value : DeptTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new RuntimeException("cant not change code: " + code + " to DeptTypeEnum.");
    }

    @Override
    public Integer getValue() {
        return code;
    }

}
