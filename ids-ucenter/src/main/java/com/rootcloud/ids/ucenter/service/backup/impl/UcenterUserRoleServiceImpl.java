package com.rootcloud.ids.ucenter.service.backup.impl;

import com.rootcloud.ids.ucenter.dto.backup.SysRoleAddUserDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysRoleRemoveUserDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterUserRole;
import com.rootcloud.ids.ucenter.dao.backup.UcenterUserRoleMapper;
import com.rootcloud.ids.ucenter.service.backup.IUcenterUserRoleService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 系统用户-角色服务实现类
 * @ClassName SysUserRoleServiceImpl
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Service
public class UcenterUserRoleServiceImpl extends ServiceImpl<UcenterUserRoleMapper, UcenterUserRole> implements
    IUcenterUserRoleService {

    @Override
    public Boolean addUser(SysRoleAddUserDTO roleAddUserDTO) {
        List<UcenterUserRole> userRoleList = new ArrayList<>();
        for (Long userId : roleAddUserDTO.getUserIds()) {
            UcenterUserRole userRole = new UcenterUserRole();
            userRole.setRoleId(roleAddUserDTO.getRoleId());
            userRole.setUserId(userId);
            userRoleList.add(userRole);
        }
        return this.saveBatch(userRoleList);
    }

    @Override
    public Boolean removeUser(SysRoleRemoveUserDTO roleRemoveUserDTO) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("role_id", roleRemoveUserDTO.getRoleId());
        queryWrapper.in("user_id", roleRemoveUserDTO.getUserIds());
        return this.remove(queryWrapper);
    }

}
