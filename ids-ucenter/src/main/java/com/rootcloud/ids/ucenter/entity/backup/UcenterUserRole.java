package com.rootcloud.ids.ucenter.entity.backup;

import com.rootcloud.ids.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 用户-角色关联对象
 * @ClassName SysUserRole
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Getter
@Setter
@TableName("ucenter_user_role")
public class UcenterUserRole extends BaseEntity {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 角色ID
     */
    private Long roleId;

}
