package com.rootcloud.ids.ucenter.service.base;

import com.rootcloud.ids.ucenter.entity.base.BaseUserDeptRel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 部门-用户-关系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
public interface IBaseUserDeptRelService extends IService<BaseUserDeptRel> {

    /**
     * 部门下的所有用户
     *
     * @param deptId 部门ID
     * @return List<BaseUserDeptRel>
     */
    List<BaseUserDeptRel> listByDeptId(String deptId);

    /**
     * 查询数据
     *
     * @param userId 部门ID
     * @return List<BaseUserDeptRel>
     */
    List<BaseUserDeptRel> listUserId(String userId);

    /**
     * 根据用户id查询特殊用户
     * @param userId
     * @return
     */
    BaseUserDeptRel getInfoByUserId(String userId);

}
