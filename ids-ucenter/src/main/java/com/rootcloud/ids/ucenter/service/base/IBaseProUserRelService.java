package com.rootcloud.ids.ucenter.service.base;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rootcloud.ids.ucenter.entity.base.BaseProUserRel;

import java.util.List;

/**
 * <p>
 * 产品分类-用户-关系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
public interface IBaseProUserRelService extends IService<BaseProUserRel> {

    List<BaseProUserRel> getProUserRelByUserId(String userId);

    /**
     * 处理特权用户信息
     *
     * @param userIdList   用户ID数组
     * @param equipmentIds 机型ID数组
     */
    void handle(List<String> userIdList, List<Long> equipmentIds);

    /**
     * 查询数据
     *
     * @param userId 用户ID
     * @return List<BaseProUserRel>
     */
    List<BaseProUserRel> listByUserId(String userId);

    /**
     * 查询数据
     * @param userIdList
     * @return
     */
    List<BaseProUserRel> listByUserId(List<String> userIdList);
}
