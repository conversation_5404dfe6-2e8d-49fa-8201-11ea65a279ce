package com.rootcloud.ids.ucenter.service.operationlog.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.util.StringUtil;
import com.rootcloud.ids.common.mybatis.utils.PageUtils;
import com.rootcloud.esmp.common.dto.cache.UserDTO;
import com.rootcloud.esmp.common.utils.SecurityUtils;
import com.rootcloud.ids.ucenter.dao.operationlog.OperationLogMapper;
import com.rootcloud.ids.ucenter.entity.operationlog.OperationLog;
import com.rootcloud.ids.ucenter.enums.operationlog.UcenterOperationModuleEnum;
import com.rootcloud.ids.ucenter.enums.operationlog.UcenterOperationTypeEnum;
import com.rootcloud.ids.ucenter.service.operationlog.IOperationLogService;
import com.rootcloud.ids.ucenter.utils.Constants;
import com.rootcloud.ids.ucenter.utils.DateUtil;
import com.rootcloud.ids.ucenter.vo.resp.operationlog.OperationLogResp;
import com.rootcloud.ids.ucenter.vo.rest.operationlog.OperationLogPageParam;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.net.Inet4Address;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.util.Date;

/**
 * <p> 操作日志表 服务实现类 </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Service
public class OperationLogServiceImpl extends
        ServiceImpl<OperationLogMapper, OperationLog> implements IOperationLogService {

  @Override
  public Page<OperationLogResp> pageByParam(OperationLogPageParam param) {
    //构建条件
    LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(StringUtil.isNotEmpty(param.getServiceId()),
            OperationLog::getServiceId,
            param.getServiceId());
    queryWrapper.eq(StringUtil.isNotEmpty(param.getLogOperationType()),
            OperationLog::getLogOperationType,
            param.getLogOperationType());
    queryWrapper.eq(StringUtil.isNotEmpty(param.getLogOperationModule()),
            OperationLog::getLogOperationModule,
            param.getLogOperationModule());
    queryWrapper.eq(StringUtil.isNotEmpty(param.getOperationResult()),
            OperationLog::getOperationResult,
            param.getOperationResult());
    queryWrapper.like(StringUtil.isNotEmpty(param.getOperationOperator()),
            OperationLog::getCreator,
            param.getOperationOperator());
    handleParam(param);
    //多条件组合查询
    Date startTime = param.getOperationStartTime();
    if (!StringUtils.isEmpty(startTime)) {
      queryWrapper.ge(OperationLog::getCreateTime, startTime);
    }
    Date endTime = param.getOperationEndTime();
    if (!StringUtils.isEmpty(endTime)) {
      queryWrapper.le(OperationLog::getCreateTime, endTime);
    }
    Page<OperationLog> page = this.page(param.batisPage(),
            queryWrapper.orderByDesc(OperationLog::getCreateTime));
    return PageUtils.convert(page, operationLog -> {
      OperationLogResp resp = new OperationLogResp();
      BeanUtil.copyProperties(operationLog,resp);
      return resp;
    });

  }

  @Override
  public void saveOperationLog(String serviceId, UcenterOperationModuleEnum operationModule, UcenterOperationTypeEnum operationType, String logContent, String logDetail, String creator, String result) {
    OperationLog operationLog = new OperationLog();
    if (!StringUtils.isEmpty(logContent) && logContent.endsWith(Constants.STRING_CONST_COMMA)) {
      logContent = logContent.substring(0, logContent.length() - 1);
    }
    operationLog.setLogContent(logContent);
    if (!StringUtils.isEmpty(logDetail) && logDetail.endsWith(Constants.STRING_CONST_POUND)) {
      logDetail = logDetail.substring(0, logDetail.length() - 1);
    }
    operationLog.setSynced(false);
    operationLog.setLogDetail(logDetail);
    operationLog.setLogOperationModule(operationModule.getCode().toString());
    operationLog.setLogOperationType(operationType.getCode().toString());
    try {
      operationLog.setOperationIp(Inet4Address.getLocalHost().getHostAddress());
    } catch (UnknownHostException e) {
    }
    operationLog.setLogTime(new Date());
    operationLog.setOperationOperator(Constants.OPERATIONLOG_UNKNOW);
    operationLog.setServiceId(serviceId);
    operationLog.setOperationResult(result);
    UserDTO currentUser = SecurityUtils.getCurrentUser();
    if(currentUser!=null){
      operationLog.setClientId(currentUser.getClientId());
      operationLog.setTenantId(currentUser.getTenantId());
      operationLog.setCreator(currentUser.getUsername());
      if(currentUser.getUserId()!=null){
        operationLog.setUserId(currentUser.getUserId());
      }
      operationLog.setOrganizationId(currentUser.getOrganizationId());
    }
    //保存日志对象
    boolean flag = this.save(operationLog);
  }


  /**
   * 对param的一些补全操作
   */
  private void handleParam(OperationLogPageParam param) {

    if (!StringUtils.isEmpty(param.getStartTime())) {
      try {
        param.setOperationStartTime(DateUtil.getDateFromString(param.getStartTime().toString(),
                DateUtil.DATE_FORMATE_STRING_A));
      } catch (ParseException e) {
        e.printStackTrace();
      }
    }
    if (!StringUtils.isEmpty(param.getEndTime())) {
      try {
        param.setOperationEndTime(DateUtil.getDateFromString(param.getEndTime().toString(),
                DateUtil.DATE_FORMATE_STRING_A));
      } catch (ParseException e) {
        e.printStackTrace();
      }
    }
  }
}
