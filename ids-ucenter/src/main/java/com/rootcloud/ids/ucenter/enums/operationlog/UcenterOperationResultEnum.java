package com.rootcloud.ids.ucenter.enums.operationlog;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;


import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * <p> dmp  操作日志 操作结果 枚举</p> #
 *
 * <AUTHOR>
 * @since 2021-11-01
 */

public enum UcenterOperationResultEnum implements IEnum<Integer> {
  /**
   * 成功
   */
  SUCCESS(0, I18nCode.SYS_100024),
  /**
   * 失败
   */
  FAILED(1, I18nCode.SYS_100025),
  /**
   * 异常
   */
  EXCEPTION(2, I18nCode.SYS_100026),;
  private int code;
  private I18nCode label;

  private UcenterOperationResultEnum(int code, I18nCode label) {
    this.code = code;
    this.label = label;
  }

  public Integer getCode() {
    return code;
  }

  public String getLabel() {
    return I18nUtil.message(label);
  }

  public static String getLabel(Integer code) {
    if (code != null) {
      for (UcenterOperationResultEnum value : UcenterOperationResultEnum.values()) {
        if (value.code == code) {
          return value.getLabel();
        }
      }
    }
    return null;
  }

  public static UcenterOperationResultEnum codeOf(int code) {
    for (UcenterOperationResultEnum value : UcenterOperationResultEnum.values()) {
      if (value.getCode() == code) {
        return value;
      }
    }
    throw new RuntimeException("cant not change code: " + code + " to DmpOperationResultEnum.");
  }

  @Override
  public Integer getValue() {
    return code;
  }
}
