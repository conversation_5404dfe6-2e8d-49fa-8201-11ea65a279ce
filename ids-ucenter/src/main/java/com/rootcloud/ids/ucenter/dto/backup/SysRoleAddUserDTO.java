package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @Description 角色添加用户DTO
 * @ClassName SysRoleAddUserDTO
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "角色添加用户DTO")
@Getter
@Setter
public class SysRoleAddUserDTO extends BaseDTO {

    @ApiModelProperty(value = "角色ID")
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @ApiModelProperty(value = "用户ID数组")
    @NotNull(message = "用户ID数组不能为空")
    private Long[] userIds;
}
