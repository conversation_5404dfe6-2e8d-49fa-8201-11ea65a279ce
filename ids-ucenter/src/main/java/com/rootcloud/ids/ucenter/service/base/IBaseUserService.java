package com.rootcloud.ids.ucenter.service.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rootcloud.esmp.common.dto.cache.BaseProductResp;
import com.rootcloud.esmp.common.enums.PermissionQueryEnum;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDeptEquipmentResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseUserEquipmentAddRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseUserEquipmentRemoveRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseUserPossessPageRest;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 组织机构表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
public interface IBaseUserService {

    /**
     * 用户权限分页查询
     *
     * @param param 参数
     * @return Page<BaseDeptEquipmentResp>
     */
    Page<BaseDeptEquipmentResp> pagePossess(BaseUserPossessPageRest param);

    /**
     * 根据token获取用户当前租户产品分类
     *
     * @return List<BaseProductResp>
     */
    List<BaseProductResp> listTenantPossess();

    /**
     * 根据token获取用户当前租户产品分类
     *
     * @return List<BaseProductResp>
     */
    List<BaseProductResp> listPossess(PermissionQueryEnum query);

    /**
     * 添加权限
     *
     * @param param 参数
     * @return Boolean
     */
    Boolean addAuthority(BaseUserEquipmentAddRest param);

    /**
     * 移除权限
     * @param param
     * @return
     */
    Boolean removeAuthority(BaseUserEquipmentRemoveRest param);

    Collection<Long> pagePossessIds(BaseUserPossessPageRest param);


}
