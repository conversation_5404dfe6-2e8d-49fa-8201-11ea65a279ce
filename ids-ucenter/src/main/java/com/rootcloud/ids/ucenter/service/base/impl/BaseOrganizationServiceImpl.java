package com.rootcloud.ids.ucenter.service.base.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rootcloud.esmp.common.dto.IamBaseDTO;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import com.rootcloud.ids.common.mybatis.utils.PageUtils;
import com.rootcloud.ids.common.web.exception.BizException;
import com.rootcloud.esmp.iam.client.IamInternalClient;
import com.rootcloud.esmp.iam.client.IamOrganizationUserClient;
import com.rootcloud.esmp.common.dto.iam.department.DepartmentMiniDTO;
import com.rootcloud.esmp.common.dto.iam.organization.OrganizationMiniDTO;
import com.rootcloud.esmp.common.dto.iam.organization.OrganizationsDTO;
import com.rootcloud.esmp.common.dto.iam.organizationUser.OrganizationUsersDTO;
import com.rootcloud.esmp.common.dto.iam.organizationUser.OrganizeUserInfoDTO;
import com.rootcloud.ids.ucenter.entity.base.BaseDeptProRel;
import com.rootcloud.ids.ucenter.entity.base.BaseProUserRel;
import com.rootcloud.ids.ucenter.entity.base.BaseProduct;
import com.rootcloud.ids.ucenter.enums.base.AuthTypeEnum;
import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import com.rootcloud.ids.ucenter.enums.operationlog.UcenterOperationModuleEnum;
import com.rootcloud.ids.ucenter.enums.operationlog.UcenterOperationTypeEnum;
import com.rootcloud.ids.ucenter.service.base.*;
import com.rootcloud.ids.ucenter.service.dmp.IDmpService;
import com.rootcloud.ids.ucenter.service.operationlog.IOperationLogService;
import com.rootcloud.ids.ucenter.service.redis.IRedisOrganizationService;
import com.rootcloud.ids.ucenter.utils.Constants;
import com.rootcloud.ids.ucenter.utils.convertor.ConvertUtils;
import com.rootcloud.ids.ucenter.utils.convertor.base.BaseDeptProRelRespConvertor;
import com.rootcloud.ids.ucenter.utils.convertor.base.BaseOrganizationRespConvertor;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseOrganizationResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseUserListResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseOrganizeProAddRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseOrganizeProRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseOrganizeUserInfoRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseOrganizeUserPageRest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 组织接口实现类
 * @since 2022/3/1 3:23 下午
 */
@Slf4j
@Service
public class BaseOrganizationServiceImpl implements IBaseOrganizationService {

    @Autowired
    private IRedisOrganizationService iRedisOrganizationService;
    @Autowired
    private IBaseDepartmentService iBaseDepartmentService;
    @Autowired
    private IBaseDeptProRelService iBaseDeptProRelService;
    @Autowired
    private IBaseProductService iBaseProductService;
    @Autowired
    private IDmpService iDmpService;
    @Autowired
    private IBaseProUserRelService iBaseProUserRelService;
    @Autowired
    private IOperationLogService operationLogService;

    @Override
    public List<BaseOrganizationResp> list() {
        List<BaseOrganizationResp> respList = this.getAll();
        if (CollUtil.isNotEmpty(respList)) {
            respList.forEach(obj -> obj.setDeptList(iBaseDepartmentService.listDepartment(true, obj.getId())));
        }
        return respList;
    }

    private List<BaseOrganizationResp> getAll() {
        List<BaseOrganizationResp> respList = null;
        respList = this.iRedisOrganizationService.get();
        //此处是服务token。全环境一致。
        String serverToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzZXJ2aWNlSWQiOiJwbXQiLCJuYW1lc3BhY2UiOiJyYyIsImlhdCI6MTY0Njg5NDM5M30.jhpe35xqHGrxQYQr_vwsml57PbPxccrAhEInk6MOq5g";
        if (CollUtil.isEmpty(respList)) {
            OrganizationsDTO result = IamInternalClient.organizations(null, "pmt", null, serverToken, 0, 100, null);
            if (result == null) {
                return respList;
            }
            respList = BaseOrganizationRespConvertor.change2OrganizationRespList(result.getResults().listIterator());
            this.iRedisOrganizationService.set(respList);
        }
        return respList;
    }


    @Override
    public Page<BaseUserListResp> pageUserByParam(BaseOrganizeUserPageRest param) {
        String filter = null;
        if (StrUtil.isNotEmpty(param.getDisplayName())) {
            filter = "{\"where\":{\"displayName\":{\"$regex\":\"" + param.getDisplayName() + "\"}}}";
        }
        String resolve = "{\"fields\":[\"departments\",\"organizations\"]}";
        OrganizationUsersDTO usersDTO = IamOrganizationUserClient.organizationUsers(param.getId(), false, false, null, false, param.skip(), param.limit(), filter, resolve);
        if (null == usersDTO) {
            return param.batisPage();
        }
        List<BaseUserListResp> respList;
        if (CollUtil.isEmpty(usersDTO.getResults())) {
            return param.batisPage();
        }
        respList = BaseOrganizationRespConvertor.change2UserListRespList(usersDTO.getResults().listIterator());
        respList.forEach(obj -> {
            List<OrganizationMiniDTO> organizeList = ConvertUtils.parseObj2Class(obj.getOrganizationList(), OrganizationMiniDTO.class);
            Map<String, String> oraMap = organizeList.stream().collect(Collectors.toMap(IamBaseDTO::getId, OrganizationMiniDTO::getName));
            obj.setOrganizationName(oraMap.get(param.getId()));
            obj.setDeptList(null);
            obj.setOrganizationList(null);
        });
        Page<BaseUserListResp> page = PageUtils.buildPage(param.batisPage(), respList);
        page.setTotal(usersDTO.getTotal());
        return page;
    }

    @Override
    public BaseUserListResp userInfo(BaseOrganizeUserInfoRest param) {
        String resolve = "{\"fields\":[\"departments\",\"organizations\"]}";
        OrganizeUserInfoDTO usersDTO = IamOrganizationUserClient.organizationUserInfo(param.getCid(), param.getUserId(), resolve);
        if (null == usersDTO) {
            return new BaseUserListResp();
        }
        BaseUserListResp resp = BaseOrganizationRespConvertor.change2UserListResp(usersDTO);
        Optional.ofNullable(resp).ifPresent(this::convert);
        return resp;
    }

    @Override
    public Collection<Long> pageByParamFilterIds(BaseOrganizeUserPageRest param) {
        return new ArrayList<>();
    }

    @Override
    public List<BaseOrganizationResp> authorizationList(BaseOrganizeProRest param) {
        List<BaseOrganizationResp> respList = this.getAll();
        List<String> deptIdList = this.iBaseDeptProRelService.listOrganisation(Collections.singleton(param.getEquipmentId()))
                .stream().filter(obj -> obj.getAuthType() == AuthTypeEnum.OTHER_USE).map(BaseDeptProRel::getDeptId).collect(Collectors.toList());
        if (param.getAuthorization()) {
            respList = respList.stream().filter(obj -> deptIdList.contains(obj.getId())).collect(Collectors.toList());
        } else {
            respList = respList.stream().filter(obj -> !deptIdList.contains(obj.getId())).collect(Collectors.toList());
        }

        if (StrUtil.isNotEmpty(param.getName())) {
            respList = respList.stream().filter(obj -> StrUtil.contains(obj.getName(), param.getName())).collect(Collectors.toList());
        }
        return respList.stream().filter(obj -> !obj.getId().equals(param.getCid())).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean authorization(BaseOrganizeProAddRest param) {
        List<BaseDeptProRel> deptProRelList = new ArrayList<>();
        BaseOrganizationResp baseOrgResp;
        //查询组织列表
        List<BaseOrganizationResp> organizationRespList = this.getAll();
        BaseDeptProRel baseDeptProRel = this.iBaseDeptProRelService.getByProductIdAndAuthType(param.getEquipmentId(), AuthTypeEnum.OWNER);
        for (String cid : param.getCidList()) {
            baseOrgResp = organizationRespList.stream().filter(resp -> resp.getId().equals(cid)).findFirst().orElse(null);
            if (baseOrgResp == null) {
                log.info("授权租户权限，租户(" + cid + ")不存在!");
                continue;
            }
            deptProRelList.add(BaseDeptProRelRespConvertor.change2Entity(cid, param.getEquipmentId(), DeptTypeEnum.ORGANIZATION, AuthTypeEnum.OTHER_USE, cid, baseDeptProRel.getDeptId()));
        }

        //保存部门和机型权限
        boolean flag = this.iBaseDeptProRelService.saveBatch(deptProRelList);
        log.info("授权机型ID（{}）的组织权限状态:{}", param.getEquipmentId(), flag);
        if (flag) {
            BaseProduct baseProduct = iBaseProductService.getById(param.getEquipmentId());
            Map<String, String> organizationMap = organizationRespList.stream().collect(Collectors.toMap(BaseOrganizationResp::getId, BaseOrganizationResp::getName));
            //保存日志对象
            param.getCidList().forEach(cid -> {
                String logContent = organizationMap.get(cid) + "(" + cid + ")" + Constants.OPERATIONLOG_ORGANIZATION_ADD() + baseProduct.getProductName() + Constants.OPERATIONLOG_EQUIPMENT_NAME();
                operationLogService.saveOperationLog(baseProduct.getId().toString(), UcenterOperationModuleEnum.MODEL_MAINTENANCE, UcenterOperationTypeEnum.TYPE_DATA_AUTHORITY,
                        logContent, logContent, null, Constants.OPERATIONLOG_SUCCESS);
            });
        }

        //刷新缓存
        iDmpService.refreshCache();
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeAuth(BaseOrganizeProAddRest param) {
        //查询机型
        BaseProduct product = iBaseProductService.getById(param.getEquipmentId());
        if (null == product) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100117));
        }

        boolean flag = this.iBaseDeptProRelService.removeByEquipmentIdAndCid(param.getEquipmentId(), param.getCidList());

        //删除组织下人员的权限
        String resolve = "{\"fields\":[\"departments\",\"organizations\"]}";
        OrganizationUsersDTO usersDTO = IamOrganizationUserClient.organizationUsers(param.getCidList().get(0), false, false, null, false, 0, 10000, null, resolve);
        List<BaseUserListResp> respList;
        if (null != usersDTO && CollUtil.isNotEmpty(usersDTO.getResults())) {
            respList = BaseOrganizationRespConvertor.change2UserListRespList(usersDTO.getResults().listIterator());
            LambdaQueryWrapper<BaseProUserRel> proUserWrapper = new LambdaQueryWrapper<>();
            proUserWrapper.in(BaseProUserRel::getUserId, respList.stream().map(BaseUserListResp::getUserId).collect(Collectors.toSet()));
            proUserWrapper.eq(BaseProUserRel::getProductId, param.getEquipmentId());
            iBaseProUserRelService.remove(proUserWrapper);

        }
        if (flag) {
            Map<String, String> organizationMap = getAll().stream().collect(Collectors.toMap(BaseOrganizationResp::getId, BaseOrganizationResp::getName));
            //保存日志对象
            param.getCidList().forEach(cid -> {
                String logContent = UcenterOperationTypeEnum.TYPE_REMOVE.getLabel() + organizationMap.get(cid) + "(" + cid + ")"
                        + "[" + product.getProductName() + "]" + UcenterOperationTypeEnum.TYPE_DATA_AUTHORITY.getLabel();
                operationLogService.saveOperationLog(product.getId().toString(),
                        UcenterOperationModuleEnum.MODEL_MAINTENANCE, UcenterOperationTypeEnum.TYPE_DATA_AUTHORITY,
                        logContent, logContent, null, Constants.OPERATIONLOG_SUCCESS);
            });
        }
        iDmpService.refreshCache();
        return true;
    }

    private void convert(BaseUserListResp obj) {
        List<DepartmentMiniDTO> deptList = ConvertUtils.parseObj2Class(obj.getDeptList(), DepartmentMiniDTO.class);
        obj.setDeptInfoList(deptList);
        List<OrganizationMiniDTO> organizeList = ConvertUtils.parseObj2Class(obj.getOrganizationList(), OrganizationMiniDTO.class);
        Map<String, String> oraMap = organizeList.stream().collect(Collectors.toMap(IamBaseDTO::getId, OrganizationMiniDTO::getName));
        obj.setOrganizationName(oraMap.get(obj.getOrganizationId()));
        obj.setOrganizeInfoList(organizeList);
        obj.setDeptList(null);
        obj.setOrganizationList(null);
    }

}
