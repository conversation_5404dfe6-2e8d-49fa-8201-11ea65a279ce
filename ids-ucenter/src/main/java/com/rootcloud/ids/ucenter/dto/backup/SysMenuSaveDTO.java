package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @Description 菜单保存DTO对象
 * @ClassName SysMenuSaveDTO
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "菜单保存DTO对象")
@Getter
@Setter
public class SysMenuSaveDTO extends BaseDTO {

    @ApiModelProperty(value = "应用ID", required = true)
    @NotNull(message = "应用ID不能为空")
    private Long appId;

    @ApiModelProperty(value = "上级菜单id", required = true)
    @NotNull(message = "上级菜单id不能为空，根节点id为0")
    private Long parentId;

    @ApiModelProperty(value = "菜单名称", required = true)
    @NotNull(message = "菜单名称不能为空")
    private String menuName;

    @ApiModelProperty(value = "菜单英文名称", required = true)
    @NotNull(message = "菜单英文名称不能为空")
    private String englishName;

    @ApiModelProperty(value = "菜单显示名称", required = true)
    @NotNull(message = "菜单显示名称")
    private String showName;

    @ApiModelProperty(value = "菜单路由地址")
    private String menuUrl;

    @ApiModelProperty(value = "菜单图标")
    private String menuIcon;

    @ApiModelProperty(value = "排序号")
    private Integer sortNo;

}
