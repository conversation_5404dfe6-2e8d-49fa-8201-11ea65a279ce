package com.rootcloud.ids.ucenter.service.backup;

import com.rootcloud.ids.ucenter.dto.backup.SysRoleAddPermissionDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysRoleRemovePermissionDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterRolePermission;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description 系统角色-权限服务接口类
 * @ClassName ISysRolePermissionService
 * <AUTHOR>
 * @Date 2021/12/21
 * @Version 1.0
 */
public interface IUcenterRolePermissionService extends IService<UcenterRolePermission> {

    /**
     * 添加权限
     * @param roleAddPermissionDTO
     * @return
     */
    Boolean addPermission(SysRoleAddPermissionDTO roleAddPermissionDTO);

    /**
     * 删除权限
     * @param roleRemovePermissionDTO
     */
    Boolean removePermission(SysRoleRemovePermissionDTO roleRemovePermissionDTO);
}
