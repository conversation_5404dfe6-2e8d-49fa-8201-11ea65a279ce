package com.rootcloud.ids.ucenter.service.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseOrganizationResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseUserListResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseOrganizeProAddRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseOrganizeProRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseOrganizeUserInfoRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseOrganizeUserPageRest;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 组织接口
 * @since 2022/3/1 3:21 下午
 */
public interface IBaseOrganizationService {
    /**
     * 组织列表
     *
     * @return List<BaseOrganizationResp>
     */
    List<BaseOrganizationResp> list();

    /**
     * 组织用户列表
     *
     * @param param 参数
     * @return Page<BaseUserListResp>
     */
    Page<BaseUserListResp> pageUserByParam(BaseOrganizeUserPageRest param);

    /**
     * 组织用户详情
     *
     * @param param 参数
     * @return BaseUserListResp
     */
    BaseUserListResp userInfo(BaseOrganizeUserInfoRest param);

    Collection<Long> pageByParamFilterIds(BaseOrganizeUserPageRest param);

    /**
     * 没有授权的组织列表
     *
     * @param param 参数
     * @return List<BaseOrganizationResp>
     */
    List<BaseOrganizationResp> authorizationList(BaseOrganizeProRest param);

    /**
     * 授权组织权限
     *
     * @param param 参数
     * @return Boolean
     */
    Boolean authorization(BaseOrganizeProAddRest param);

    /**
     * 移除授权
     *
     * @param param 参数
     * @return Boolean
     */
    Boolean removeAuth(BaseOrganizeProAddRest param);
}
