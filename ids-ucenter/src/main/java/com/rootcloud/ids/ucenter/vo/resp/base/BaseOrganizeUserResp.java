package com.rootcloud.ids.ucenter.vo.resp.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-02-28 17:03
 **/
@Data
@ApiModel(value = "BaseOrganizeUserResp", description = "组织用户")
public class BaseOrganizeUserResp {

    @ApiModelProperty("#总数#")
    private Integer total;

    @ApiModelProperty("#用户列表#")
    private List<BaseUserListResp> respList;

}
