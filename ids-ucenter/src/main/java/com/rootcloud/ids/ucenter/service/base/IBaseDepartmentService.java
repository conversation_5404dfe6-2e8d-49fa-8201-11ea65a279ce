package com.rootcloud.ids.ucenter.service.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDepartmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDeptEquipmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseEquipmentResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseDeptEquipmentAddRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseDeptEquipmentDelRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseDeptEquipmentPageRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseDeptEquipmentRest;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 组织机构表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
public interface IBaseDepartmentService {

    /**
     * 所选组织下所有部门
     *
     * @param parseToTree 是否返回树结构
     * @param id          组织ID
     * @return List<BaseDepartmentResp>
     */
    List<BaseDepartmentResp> listDepartment(Boolean parseToTree, String id);

    /**
     * 部门的机型
     *
     * @param param 参数
     * @return Page<BaseDeptEquipmentResp>
     */
    Page<BaseDeptEquipmentResp> pageDept(BaseDeptEquipmentPageRest param);

    /**
     * 新增部门权限
     *
     * @param param 参数
     * @return Boolean
     */
    Boolean addEquipment(BaseDeptEquipmentAddRest param);

    /**
     * 部门所拥有机型
     *
     * @param param 参数
     * @return List<BaseEquipmentResp>
     */
    List<BaseEquipmentResp> getEquipment(BaseDeptEquipmentRest param);

    /**
     * 部门机型删除
     *
     * @param param
     * @return
     */
    Boolean removeEquipment(BaseDeptEquipmentDelRest param);

    Collection<Long> pagePossessIds(BaseDeptEquipmentPageRest param);

    /**
     * 查所有权列表
     *
     * @param param 参数
     * @return Page<BaseDeptEquipmentResp>
     */
    Page<BaseDeptEquipmentResp> pageOwner(BaseDeptEquipmentPageRest param);

    /**
     * 查跨组织权限列表
     *
     * @param param 参数
     * @return Page<BaseDeptEquipmentResp>
     */
    Page<BaseDeptEquipmentResp> pageUse(BaseDeptEquipmentPageRest param);
}
