package com.rootcloud.ids.ucenter.vo.rest.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "BaseEquipmentModelReleaseParam", description = "设备型号发布参数")
public class BaseEquipmentModelReleaseParam {

    @ApiModelProperty("#设备型号id#")
    private Long id;

    @ApiModelProperty("#设备型号id#")
    private Integer releaseStatus;

}
