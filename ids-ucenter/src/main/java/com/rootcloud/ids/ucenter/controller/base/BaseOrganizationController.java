package com.rootcloud.ids.ucenter.controller.base;


import com.rootcloud.ids.common.core.result.PageResult;
import com.rootcloud.ids.common.core.result.Result;
import com.rootcloud.ids.ucenter.service.base.IBaseOrganizationService;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseOrganizationResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseUserListResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseOrganizeProAddRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseOrganizeProRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseOrganizeUserInfoRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseOrganizeUserPageRest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 组织
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Api(value = "BaseOrganizationController", tags = "[BASE]组织管理")
@Slf4j
@RestController
@RequestMapping("/api/base/organize")
public class BaseOrganizationController {

    @Autowired
    private IBaseOrganizationService iBaseOrganizationService;

    @ApiOperation(value = "组织列表", notes = "组织列表")
    @GetMapping(value = "/list")
    public Result<List<BaseOrganizationResp>> list() {
        return Result.success(iBaseOrganizationService.list());
    }

    @ApiOperation(value = "组织用户列表", notes = "组织用户列表")
    @GetMapping(value = "/user/pageByParam")
    public PageResult<BaseUserListResp> pageUserByParam(@Valid BaseOrganizeUserPageRest param) {
        return PageResult.success(iBaseOrganizationService.pageUserByParam(param));
    }

    @ApiOperation(value = "组织用户详情", notes = "组织用户详情")
    @GetMapping(value = "/user/info")
    public Result<BaseUserListResp> userInfo(@Valid BaseOrganizeUserInfoRest param) {
        return Result.success(iBaseOrganizationService.userInfo(param));
    }

    @ApiOperation(value = "组织用户列表-id过滤", notes = "组织用户列表-id过滤")
    @GetMapping(value = "/user/pageByParamFilterIds")
    public Result<Collection<Long>> pageUserByParamIds(@Valid BaseOrganizeUserPageRest param) {
        return Result.success(iBaseOrganizationService.pageByParamFilterIds(param));
    }

    @ApiOperation(value = "授权的组织列表", notes = "授权的组织列表")
    @GetMapping(value = "/authorization/list")
    public Result<List<BaseOrganizationResp>> noAuthorizationList(@Valid BaseOrganizeProRest param) {
        return Result.success(iBaseOrganizationService.authorizationList(param));
    }

    @ApiOperation(value = "授权", notes = "授权")
    @PostMapping(value = "/authorization")
    public Result<Boolean> authorization(@RequestBody @Valid BaseOrganizeProAddRest param) {
        return Result.success(iBaseOrganizationService.authorization(param));
    }

    @ApiOperation(value = "移除授权", notes = "移除授权")
    @PostMapping(value = "/remove/auth")
    public Result<Boolean> removeAuth(@RequestBody @Valid BaseOrganizeProAddRest param) {
        return Result.success(iBaseOrganizationService.removeAuth(param));
    }
}
