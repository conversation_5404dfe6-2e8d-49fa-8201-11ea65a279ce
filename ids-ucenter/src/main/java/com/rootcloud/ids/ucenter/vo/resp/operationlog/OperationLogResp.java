package com.rootcloud.ids.ucenter.vo.resp.operationlog;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
@Data
@ApiModel(value="OperationLog返回对象", description="操作日志表")
public class OperationLogResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "#日志时间#")
    private Date logTime;

    @ApiModelProperty(value = "#操作模块#")
    private String logOperationModule;

    @ApiModelProperty(value = "#操作类型#")
    private String logOperationType;

    @ApiModelProperty(value = "#操作内容#")
    private String logContent;

    @ApiModelProperty(value = "#操作结果#")
    private String operationResult;

    @ApiModelProperty(value = "#操作人#")
    private String operationOperator;

    @ApiModelProperty(value = "#IP地址#")
    private String operationIp;

    @ApiModelProperty(value = "#主键id#")
	private Long id;

    @ApiModelProperty(value = "#记录创建人#")
    private String creator;

    @ApiModelProperty(value = "#记录创建时间#")
    private Date createTime;

    @ApiModelProperty(value = "#记录修改人#")
    private String modifier;

    @ApiModelProperty(value = "#记录修改时间#")
    private Date updateTime;

    @ApiModelProperty(value = "#业务id#")
    private Long serviceId;

    @ApiModelProperty(value = "#日志详情#")
    private String logDetail;



}
