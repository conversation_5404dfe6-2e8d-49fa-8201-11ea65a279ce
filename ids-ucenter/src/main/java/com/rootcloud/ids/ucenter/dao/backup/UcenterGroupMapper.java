package com.rootcloud.ids.ucenter.dao.backup;

import com.rootcloud.ids.ucenter.dto.backup.SysGroupCondition;
import com.rootcloud.ids.ucenter.entity.backup.UcenterGroup;
import com.rootcloud.ids.ucenter.vo.backup.SysGroupQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 组织Mapper接口
 * @InterfaceName SysGroupMapper
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public interface UcenterGroupMapper extends BaseMapper<UcenterGroup> {

    /**
     * 条件查询
     * @param cnd
     * @return
     */
    public List<SysGroupQueryVO> query(@Param(value = "cnd") SysGroupCondition cnd);

}
