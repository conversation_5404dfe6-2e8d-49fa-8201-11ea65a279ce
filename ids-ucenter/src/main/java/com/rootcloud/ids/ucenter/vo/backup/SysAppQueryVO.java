package com.rootcloud.ids.ucenter.vo.backup;

import com.rootcloud.ids.common.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 应用Query VO对象
 * @ClassName SysAppQueryVO
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "应用Query VO对象")
@Getter
@Setter
public class SysAppQueryVO extends BaseVO {

    @ApiModelProperty(value = "应用ID")
    private Long appId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "应用logo地址")
    private String appLogo;

}
