package com.rootcloud.ids.ucenter.service.backup;

import com.rootcloud.ids.ucenter.dto.backup.SysRoleCondition;
import com.rootcloud.ids.ucenter.dto.backup.SysRoleSaveDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysRoleUpdateDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterRole;
import com.rootcloud.ids.ucenter.vo.backup.SysRoleQueryVO;
import com.rootcloud.ids.ucenter.vo.backup.SysRoleVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;

/**
 * @Description 系统角色服务接口类
 * @InterfaceName ISysRoleService
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public interface IUcenterRoleService extends IService<UcenterRole> {

    /**
     * 保存角色
     * @param roleSaveDTO
     * @return
     */
    Long save(SysRoleSaveDTO roleSaveDTO);

    /**
     * 更新角色
     * @param roleUpdateDTO
     * @return
     */
    Boolean update(SysRoleUpdateDTO roleUpdateDTO);

    /**
     * 根据ID查询角色
     * @param roleId
     * @return
     */
    SysRoleVO findById(Long roleId);


    /**
     * 批量删除
     * @param roleIds
     */
    Boolean batchDelete(Long[] roleIds);

    /**
     * 分页查询
     * @param cnd
     * @return
     */
    PageInfo<SysRoleQueryVO> query(SysRoleCondition cnd);

}
