package com.rootcloud.ids.ucenter.service.redis.impl;


import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.rootcloud.esmp.common.dto.cache.BaseProductResp;
import com.rootcloud.esmp.common.dto.cache.TokenAndOrganizationUserCachedDTO;
import com.rootcloud.esmp.common.enums.PermissionQueryEnum;
import com.rootcloud.esmp.common.utils.SecurityUtils;
import com.rootcloud.ids.common.core.constant.SecurityConstants;
import com.rootcloud.ids.common.redis.utils.RedisUtils;
import com.rootcloud.ids.ucenter.service.base.IBaseUserService;
import com.rootcloud.ids.ucenter.service.redis.IRedisLoginService;
import java.util.List;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/2/28
 */
@Slf4j
@Service
public class RedisLoginServiceImpl extends RedisUtils implements IRedisLoginService {
    private static final String KEY = "TOKEN:GATEWAY:";

    @Override
    public TokenAndOrganizationUserCachedDTO getUserInfo(String tokenValue) {
        String object = (String) get(getRedisKeyByToken(tokenValue));
        if (object == null) {
            return null;
        }
        return JSONUtil.toBean(object, TokenAndOrganizationUserCachedDTO.class);
    }

    public boolean refreshUserCached() {
        try {
            IBaseUserService iBaseUserService = SpringUtil.getBean(IBaseUserService.class);
            List<BaseProductResp> baseProductResps = iBaseUserService.listPossess(PermissionQueryEnum.CROSS_TENANT);
            TokenAndOrganizationUserCachedDTO userCached = SecurityUtils.getTokenAndOrganizationUserCachedDTO();
            final String token = SecurityUtils.getCurrentToken();
            userCached.setBaseProductList(baseProductResps);
            boolean result = set(getRedisKeyByToken(token), JSONUtil.toJsonStr(userCached));
            log.info("缓存刷新结果");
            return result;
        } catch (Exception ex) {
            log.error("缓存刷新失败: " + ex.getMessage(), ex);
        }
        return false;
    }

    public String getRedisKeyByToken(String tokenValue) {
        String md5 = MD5.create().digestHex(tokenValue);
        final String key = KEY + md5;
        log.info("Redis Key: " + key);
        return key;
    }

    /**
     * 从header或者cookie中获取token
     *
     * @param request request
     * @return String
     */
    public String getToken(HttpServletRequest request) {
        String tokenValue = "";
        Cookie[] cookie = request.getCookies();
        //未传cookies,就从header中获取。方便swagger调用
        if (cookie == null || cookie.length == 0) {
            tokenValue = request.getHeader(SecurityConstants.ACCESS_TOKEN);
        } else {
            //获取cookies中的过期时间和access_token
            for (Cookie cke : cookie) {
                if (SecurityConstants.ACCESS_TOKEN.equals(cke.getName())) {
                    tokenValue = cke.getValue();
                }
            }
            if (StrUtil.isEmpty(tokenValue)) {
                tokenValue = request.getHeader(SecurityConstants.ACCESS_TOKEN);
            }
        }

        // 如果不是使用access_token传递的token，那么就从Authorization中取
        if (StringUtils.isBlank(tokenValue)) {
            String authorization = request.getHeader(SecurityConstants.AUTHORIZATION_KEY);
            tokenValue = StrUtil.subAfter(authorization, SecurityConstants.JWT_PREFIX, false);
        }
        return tokenValue;
    }
}
