package com.rootcloud.ids.ucenter.controller.operationlog;


import com.rootcloud.ids.common.core.result.PageResult;
import com.rootcloud.ids.ucenter.service.operationlog.IOperationLogService;
import com.rootcloud.ids.ucenter.vo.resp.operationlog.OperationLogResp;
import com.rootcloud.ids.ucenter.vo.rest.operationlog.OperationLogPageParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p> 操作日志表 前端控制器 </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
@Api(value = "OperationLogController", tags = "操作日志")
@RestController
@Slf4j
@RequestMapping("/api/ids/operationlog")
public class OperationLogController {

  @Autowired
  private IOperationLogService iOperationLogService;

  @ApiOperation(value = "操作日志分页查询", notes = "操作日志分页查询")
  @PostMapping(value = "/list")
  public PageResult<OperationLogResp> list(
      @RequestBody OperationLogPageParam param) {
    return PageResult.success(iOperationLogService.pageByParam(param));
  }

}

