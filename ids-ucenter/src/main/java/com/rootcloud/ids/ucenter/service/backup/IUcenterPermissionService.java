package com.rootcloud.ids.ucenter.service.backup;

import com.rootcloud.ids.ucenter.dto.backup.SysPermissionSaveDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysPermissionUpdateDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterPermission;
import com.rootcloud.ids.ucenter.vo.backup.SysPermissionVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description 系统权限服务接口类
 * @InterfaceName ISysPermissionService
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public interface IUcenterPermissionService extends IService<UcenterPermission> {

    /**
     * 保存权限
     * @param permissionSaveDTO
     * @return
     */
    Long save(SysPermissionSaveDTO permissionSaveDTO);

    /**
     * 更新权限
     * @param permissionUpdateDTO
     * @return
     */
    Boolean update(SysPermissionUpdateDTO permissionUpdateDTO);

    /**
     * 根据ID查询权限
     * @param permissionId
     * @return
     */
    SysPermissionVO findById(Long permissionId);

    /**
     * 根据用户ID查询用户所有权限
     * @param userId
     * @return
     */
    List<SysPermissionVO> findPermissionByUserId(Long userId);

    /**
     * 同步权限到数据库
     * @return
     */
    Boolean syncDB() throws ClassNotFoundException;

}
