package com.rootcloud.ids.ucenter.vo.resp.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rootcloud.esmp.common.enums.RegisterTypeEnum;
import com.rootcloud.esmp.common.enums.ReleaseStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: ling.kang
 * @create: 2022-02-25 16:59
 **/
@Data
@ApiModel(value = "BaseEquipmentModelResp", description = "设备型号列表")
public class BaseEquipmentModelResp implements Serializable {

    private static final long serialVersionUID = -7772221275091232342L;

    @ApiModelProperty("#设备型号id#")
    private Long id;

    @ApiModelProperty("#机械大类/机型#")
    private String productName;

    @ApiModelProperty("#机型id#")
    private Long equipmentId;

    @ApiModelProperty("#设备型号#")
    private String equipmentModelName;

    @ApiModelProperty("#产品业务编码#")
    private String productBusinessCode;

    @ApiModelProperty("#发布状态 0:未发布 1:已发布#")
    private ReleaseStatusEnum releaseStatus;

    @ApiModelProperty("#租户ID#")
    private String tenantId;

    @ApiModelProperty("#创建时间#")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("#修改时间#")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "监管类型 0:工程机械:ENGINEERING_MACHINES 1:非工程机械:NON_ENGINEERING_MACHINES 2:其它:OTHERS")
    private RegisterTypeEnum registerType;

    @ApiModelProperty("#所属站点#")
    private String ownerShip;

}
