package com.rootcloud.ids.ucenter.dao.backup;

import com.rootcloud.ids.ucenter.dto.backup.SysAppCondition;
import com.rootcloud.ids.ucenter.entity.backup.UcenterApp;
import com.rootcloud.ids.ucenter.vo.backup.SysAppQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 应用Mapper接口
 * @InterfaceName SysAppMapper
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Mapper
public interface UcenterAppMapper extends BaseMapper<UcenterApp> {

    /**
     * 条件查询
     * @param cnd
     * @return
     */
    public List<SysAppQueryVO> query(@Param(value = "cnd") SysAppCondition cnd);

}
