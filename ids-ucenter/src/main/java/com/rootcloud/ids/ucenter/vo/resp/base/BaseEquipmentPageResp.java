package com.rootcloud.ids.ucenter.vo.resp.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rootcloud.esmp.common.enums.ProductStatusEnum;
import com.rootcloud.esmp.common.enums.ProductTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/19 15:43
 */
@Data
@ApiModel(value = "BaseEquipmentPageResp", description = "机型列表")
public class BaseEquipmentPageResp{

    @ApiModelProperty("#主键id#")
    private Long id;

    @ApiModelProperty("#机械大类id#")
    private Long conMacId;

    @ApiModelProperty("#机械大类#")
    private String constructionMachineryName;

    @ApiModelProperty("#机型id#")
    private Long equipmentId;

    @ApiModelProperty("#机型#")
    private String equipmentName;

    @ApiModelProperty("#产品状态#")
    private ProductStatusEnum productStatus;

    @ApiModelProperty("#分类层级#")
    private ProductTypeEnum productType;

    @ApiModelProperty("#租户ID#")
    private String tenantId;

    @ApiModelProperty("#创建时间#")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("#创建人#")
    private String creator;
}
