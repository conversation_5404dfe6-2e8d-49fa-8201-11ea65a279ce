package com.rootcloud.ids.ucenter.service.redis.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.rootcloud.ids.common.redis.utils.RedisUtils;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseOrganizationResp;
import com.rootcloud.ids.ucenter.service.redis.IRedisOrganizationService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 组织列表缓存实现类
 * @since 2022/3/1 3:00 下午
 */
@Service
public class RedisOrganizationServiceImpl extends RedisUtils implements IRedisOrganizationService {

    private static final String KEY = "IAM:ORGANIZE";

    /**
     * 组织列表缓存过期时间，暂定1小时
     */
    private static final Integer EXP = 60 * 3;

    @Override
    public List<BaseOrganizationResp> get() {
        Object obj = get(KEY);
        if (obj == null) {
            return null;
        }
        return JSONUtil.toList(obj.toString(), BaseOrganizationResp.class);
    }

    @Override
    public Boolean set(List<BaseOrganizationResp> value) {
        if (CollUtil.isEmpty(value)) {
            return false;
        }
        return set(KEY, JSONUtil.toJsonStr(value), EXP);
    }

}
