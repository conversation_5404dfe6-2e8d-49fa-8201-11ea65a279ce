package com.rootcloud.ids.ucenter.vo.rest.base;

import com.rootcloud.ids.common.core.base.BasePageQuery;
import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: ling.kang
 * @create: 2022-02-28 15:51
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "BaseDeptEquipmentPageRest", description = "机型列表参数")
public class BaseDeptEquipmentPageRest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = -3776925602037834371L;
    @ApiModelProperty(value = "#部门或者组织ID#", required = true)
    @NotBlank(message = "id不能为空")
    private String id;

    @ApiModelProperty(value = "#部门或者组织ID#", required = true)
    @NotNull(message = "deptType不能为空")
    private DeptTypeEnum deptType;

    @ApiModelProperty("#机械大类id#")
    private Long conMacId;

    @ApiModelProperty(value = "#搜索字段#")
    private String searchKey;

    /**
     * 是否已拥有
     */
    @ApiModelProperty(hidden = true)
    private Boolean possess;

    @ApiModelProperty(value = "#上级部门ID(查二级或以下部门未拥有权限时必传)#")
    private String parentId;

    @ApiModelProperty(value = "#组织ID(查部门未拥有权限时必传)#")
    private String cid;

}
