package com.rootcloud.ids.ucenter.controller.base;


import com.rootcloud.esmp.common.dto.permission.ExpressionDto;
import com.rootcloud.esmp.common.enums.IamPermissionActionEnum;
import com.rootcloud.esmp.common.enums.ProductTypeEnum;
import com.rootcloud.esmp.iam.dto.permission.IamResourcePropertyValueDto;
import com.rootcloud.esmp.iam.dto.permission.IamResourcePropertyValueQueryDto;
import com.rootcloud.esmp.iam.dto.permission.PagingResult;
import com.rootcloud.esmp.iam.service.auth.IDataPermissionService;
import com.rootcloud.ids.common.core.result.Result;
import com.rootcloud.ids.ucenter.service.auth.IIamAuthorizationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Api(value = "BaseUserController", tags = "[BASE]用户数据权限")
@Slf4j
@RestController
@RequestMapping("/api/base/auth")
@AllArgsConstructor
public class IamAuthorizationController {
    private final IIamAuthorizationService iamAuthorizationService;
    private final IDataPermissionService dataPermissionService;

    @ApiOperation(value = "IAM数据权限机型树结构", notes = "IAM数据权限机型树结构")
    @PostMapping("permissions/{product-type}")
    public PagingResult<List<IamResourcePropertyValueDto>> permissions(
        @PathVariable("product-type") ProductTypeEnum productType,
        @RequestBody IamResourcePropertyValueQueryDto param) {
        return iamAuthorizationService.permissionTree(productType, param);
    }

    @ApiOperation(value = "IAM数据权限机型值", notes = "IAM数据权限机型值")
    @PostMapping("permissions/values")
    public PagingResult<List<IamResourcePropertyValueDto>> values(
        @RequestBody IamResourcePropertyValueQueryDto param) {
        return iamAuthorizationService.permissionValues(param);
    }

    @ApiOperation(value = "获得用户数据权限表达式", notes = "获得用户数据权限表达式")
    @GetMapping(value = "/expressions")
    public Result<ExpressionDto> pagePossess(@RequestParam(value = "actions", required = false) IamPermissionActionEnum[] actions) {
        return Result.success(dataPermissionService.getExpressions(actions));
    }
}
