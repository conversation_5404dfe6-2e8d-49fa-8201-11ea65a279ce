package com.rootcloud.ids.ucenter.dao.backup;

import com.rootcloud.ids.ucenter.dto.backup.SysRoleCondition;
import com.rootcloud.ids.ucenter.entity.backup.UcenterRole;
import com.rootcloud.ids.ucenter.vo.backup.SysRoleQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 角色Mapper接口
 * @InterfaceName SysRoleMapper
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public interface UcenterRoleMapper extends BaseMapper<UcenterRole> {

    /**
     * 条件查询
     * @param cnd
     * @return
     */
    public List<SysRoleQueryVO> query(@Param(value = "cnd") SysRoleCondition cnd);
}
