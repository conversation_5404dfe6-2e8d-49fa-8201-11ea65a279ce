package com.rootcloud.ids.ucenter.vo.resp.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: ling.kang
 * @create: 2022-02-28 16:57
 **/
@Data
@ApiModel(value = "BaseUserDetailResp", description = "用户详情")
public class BaseUserDetailResp {

    @ApiModelProperty("#用户id#")
    private Long userId;

    @ApiModelProperty("#租户id#")
    private Long tenantId;

    @ApiModelProperty("#用户姓名#")
    private String userName;

    @ApiModelProperty("#部门#")
    private List<BaseDepartmentResp> depts;

    @ApiModelProperty("#手机号#")
    private String phone;

    @ApiModelProperty("#性别#")
    private String sex;

    @ApiModelProperty("#邮箱#")
    private String email;

}
