package com.rootcloud.ids.ucenter.service.base;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rootcloud.ids.ucenter.entity.base.BaseDeptProRel;
import com.rootcloud.ids.ucenter.enums.base.AuthTypeEnum;
import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 产品分类-部门-关系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
public interface IBaseDeptProRelService extends IService<BaseDeptProRel> {

    /**
     * 查询数据
     *
     * @param ids      组织或者部门ID
     * @param deptType 部门类型
     * @return List<BaseDeptProRel>
     */
    List<BaseDeptProRel> listByDeptIdsAndDeptType(List<String> ids, DeptTypeEnum deptType);

    /**
     * 查询数据
     *
     * @param productIds 机型ID
     * @return List<BaseDeptProRel>
     */
    List<BaseDeptProRel> listOrganisation(Set<Long> productIds);

    /**
     * 查询不在目标内的机型数据
     *
     * @param ids 组织ID
     * @return List<BaseDeptProRel>
     */
    List<BaseDeptProRel> listNoInByCidList(List<String> ids);

    /**
     * 查询数据
     *
     * @param deptType 部门类型
     * @return List<BaseDeptProRel>
     */
    List<BaseDeptProRel> listByDeptType(DeptTypeEnum deptType);

    /**
     * 查询数据
     *
     * @param equipmentIds 机型id数组
     * @param deptType     部门类型
     * @param authType     权限类型
     * @return List<BaseDeptProRel>
     */
    List<BaseDeptProRel> listByProductIdsAndDeptTypeAndAuthType(Collection<Long> equipmentIds, DeptTypeEnum deptType, AuthTypeEnum authType);

    /**
     * 查询数据
     *
     * @param equipmentId 机型id
     * @param authType    权限类型
     * @return BaseDeptProRel
     */
    BaseDeptProRel getByProductIdAndAuthType(Long equipmentId, AuthTypeEnum authType);

    /**
     * 查询数据
     *
     * @param equipmentIds 机型id数组
     * @param authType     权限类型
     * @return List<BaseDeptProRel>
     */
    List<BaseDeptProRel> listByProductIdAndAuthType(List<Long> equipmentIds, AuthTypeEnum authType);

    /**
     * 移除授权
     *
     * @param equipmentId 机型id
     * @param cidList     被移除的租户id数组
     * @return boolean
     */
    boolean removeByEquipmentIdAndCid(Long equipmentId, List<String> cidList);

    /**
     * 查询数据
     *
     * @param cid       租户id
     * @param productId 机型id
     * @return List<BaseDeptProRel>
     */
    List<BaseDeptProRel> listByCidAndProductId(String cid, Long productId);
}
