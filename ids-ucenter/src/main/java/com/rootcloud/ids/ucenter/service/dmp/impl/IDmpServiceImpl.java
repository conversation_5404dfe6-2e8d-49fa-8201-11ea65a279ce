package com.rootcloud.ids.ucenter.service.dmp.impl;

import static com.rootcloud.esmp.common.Constants.LOCALE_HEADER;
import static com.rootcloud.ids.common.core.constant.GlobalConstants.API_REQUEST_ID_HEADER;
import static com.rootcloud.ids.common.core.constant.GlobalConstants.LOG_REQUEST_ID;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import com.rootcloud.ids.common.web.config.LanguageLocalConfig;
import com.rootcloud.ids.ucenter.service.dmp.IDmpService;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.client.methods.HttpPost;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

/**
 * @author: kang.ling
 * @create: 2022-03-28 13:59
 **/
@Service
@Slf4j
public class IDmpServiceImpl implements IDmpService {

    @Value("${os.gateway.domain}")
    private String gatewayDomain;

    @Override
    public void refreshCache() {
        String url = gatewayDomain + "/dmp/dmp-server/api/dmp/gateway/refreshCache";
        HttpRequest request = HttpUtil.createRequest(Method.GET, url);
        addHeader(request);
        HttpResponse response = request.execute();
        log.info("【刷新存储机型缓存】-[{}]：返回：{}", url, response.body());
    }

    private void addHeader(HttpRequest request) {
        String requestId = Optional.ofNullable(request.header(API_REQUEST_ID_HEADER))
            .orElseGet(() -> MDC.get(LOG_REQUEST_ID));
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(requestId)) {
            requestId = UUID.randomUUID().toString().replaceAll("-", "");
        }
        request.header(API_REQUEST_ID_HEADER, requestId);
        final String language = I18nUtil.getCurrentLanguage();
        request.header(LOCALE_HEADER, language);

        log.info("Request-id: {}, Language: {}", requestId, language);
    }

}
