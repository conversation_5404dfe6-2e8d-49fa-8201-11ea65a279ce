package com.rootcloud.ids.ucenter.vo.resp.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: ling.kang
 * @create: 2022-02-28 17:03
 **/
@Data
@ApiModel(value = "BaseUserResp", description = "用户列表")
public class BaseUserResp {

    @ApiModelProperty("#用户id#")
    private Long userId;

    @ApiModelProperty("#用户名称#")
    private String userName;

    @ApiModelProperty("#部门id#")
    private Long deptId;

    @ApiModelProperty("#部门名称#")
    private String deptName;

}
