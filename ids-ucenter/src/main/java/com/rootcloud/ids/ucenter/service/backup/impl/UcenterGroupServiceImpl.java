package com.rootcloud.ids.ucenter.service.backup.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.rootcloud.ids.ucenter.dto.backup.SysGroupCondition;
import com.rootcloud.ids.ucenter.dto.backup.SysGroupSaveDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysGroupUpdateDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterGroup;
import com.rootcloud.ids.ucenter.dao.backup.UcenterGroupMapper;
import com.rootcloud.ids.ucenter.service.backup.IUcenterGroupService;
import com.rootcloud.ids.ucenter.vo.backup.SysGroupQueryVO;
import com.rootcloud.ids.ucenter.vo.backup.SysGroupVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 系统组织服务实现类
 * @ClassName SysGroupServiceImpl
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Service
public class UcenterGroupServiceImpl extends ServiceImpl<UcenterGroupMapper, UcenterGroup> implements
    IUcenterGroupService {

    @Override
    public Long save(SysGroupSaveDTO groupSaveDTO) {
        UcenterGroup group = new UcenterGroup();
        BeanUtil.copyProperties(groupSaveDTO, group);
        this.save(group);
        return group.getGroupId();
    }

    @Override
    public Boolean update(SysGroupUpdateDTO groupUpdateDTO) {
        UcenterGroup group = new UcenterGroup();
        BeanUtil.copyProperties(groupUpdateDTO, group);
        return this.updateById(group);
    }

    @Override
    public SysGroupVO findById(Long groupId) {
        SysGroupVO groupVO = new SysGroupVO();
        UcenterGroup group = this.getById(groupId);
        BeanUtil.copyProperties(group, groupVO);
        return groupVO;
    }

    @Override
    public Boolean batchDelete(Long[] groupIds) {
        return this.removeByIds(Convert.toList(Long.class, groupIds));
    }

    @Override
    public PageInfo<SysGroupQueryVO> query(SysGroupCondition cnd) {
        PageHelper.startPage(cnd.getPage(), cnd.getPageSize());
        List<SysGroupQueryVO> groupVOList = this.baseMapper.query(cnd);
        PageInfo<SysGroupQueryVO> pageInfo = new PageInfo(groupVOList);
        return pageInfo;
    }

}
