package com.rootcloud.ids.ucenter.entity.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rootcloud.esmp.common.enums.*;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import com.rootcloud.ids.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * <p>
 * 产品分类表
 * </p>
 * <AUTHOR>
 * @since 2022-02-24
 */
@Data
@TableName("ucenter_base_product")
@ApiModel(value = "BaseProduct对象", description = "产品分类表")
public class BaseProduct extends BaseEntity {

    @ApiModelProperty("#分类id#")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("#父级id#")
    private Long parentId;

    @ApiModelProperty("#分类名称#")
    private String productName;

    @ApiModelProperty("#分类名称英文名#")
    private String productNameEn;

    @ApiModelProperty("#产品状态#")
    private ProductStatusEnum productStatus;

    @ApiModelProperty("#分类层级#")
    private ProductTypeEnum productType;

    @ApiModelProperty("#产品编码#")
    private EquipmentTypeEnum productCode;

    @ApiModelProperty("#产品业务编码#")
    private String productBusinessCode;

    @ApiModelProperty("#发布状态#")
    private ReleaseStatusEnum releaseStatus;

    @ApiModelProperty(value = "监管类型 0:工程机械:ENGINEERING_MACHINES 1:非工程机械:NON_ENGINEERING_MACHINES 2:其它:OTHERS")
    private RegisterTypeEnum registerType;

    @ApiModelProperty(value = "#总线拓扑结构ID#")
    private Long topologyId;

    @ApiModelProperty(value = "#描述信息#")
    @TableField(value = "description")
    private String desc;

    @ApiModelProperty(value = "#总线拓扑结构版本#")
    private Integer topologyVersion;

    @ApiModelProperty(value = "#是否发布过 0 已发布 1 未发布#")
    private Integer hasPublished;

    @ApiModelProperty(value = "#租户ID#")
    private String tenantId;

    @ApiModelProperty(value = "#所属站点#")
    private String ownerShip;

    public String getProductName() {
        if (I18nUtil.isEnglish()) {
            return StringUtils.hasLength(productNameEn) ? productNameEn : productName;
        }
        return productName;
    }
}
