package com.rootcloud.ids.ucenter.enums.operationlog;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;


import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * <p> dmp  操作日志 操作类型 枚举</p> #
 *
 * <AUTHOR>
 * @since 2021-11-01
 */

public enum UcenterOperationTypeEnum implements IEnum<Integer> {
  /**
   * 创建
   */
  TYPE_CREATE(0, I18nCode.SYS_100009),
  /**
   * 移除
   */
  TYPE_REMOVE(1, I18nCode.SYS_100010),
  /**
   * 更新
   */
  TYPE_UPDATE(2, I18nCode.SYS_100011),
  /**
   * 查看
   */
  TYPE_QUERY(3, I18nCode.SYS_100012),
  /**
   * 部门
   */
  TYPE_DEPT(8, I18nCode.SYS_100013),
  /**
   * 用户
   */
  TYPE_USER(9, I18nCode.SYS_100014),
  /**
   * 数据权限
   */
  TYPE_DATA_AUTHORITY(10, I18nCode.SYS_100015),
  ;
  private int code;
  private I18nCode label;

  private UcenterOperationTypeEnum(int code, I18nCode label) {
    this.code = code;
    this.label = label;
  }

  public Integer getCode() {
    return code;
  }

  public String getLabel() {
    return I18nUtil.message(label);
  }

  public static String getLabel(Integer code) {
    if (code != null) {
      for (UcenterOperationTypeEnum value : UcenterOperationTypeEnum.values()) {
        if (value.code == code) {
          return value.getLabel();
        }
      }
    }
    return null;
  }

  public static UcenterOperationTypeEnum codeOf(int code) {
    for (UcenterOperationTypeEnum value : UcenterOperationTypeEnum.values()) {
      if (value.getCode() == code) {
        return value;
      }
    }
    throw new RuntimeException("cant not change code: " + code + " to DmpOperationTypeEnum.");
  }

  @Override
  public Integer getValue() {
    return code;
  }

  public static String getNameByCode(Integer code){
    for(UcenterOperationTypeEnum value : UcenterOperationTypeEnum.values()){
      if(value.code == code){
        return value.name().substring(value.name().indexOf("_")+1);
      }
    }
    return null;
  }
}
