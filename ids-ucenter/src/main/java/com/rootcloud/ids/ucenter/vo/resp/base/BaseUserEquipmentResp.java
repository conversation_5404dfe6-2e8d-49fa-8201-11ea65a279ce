package com.rootcloud.ids.ucenter.vo.resp.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: ling.kang
 * @create: 2022-03-01 10:58
 **/
@Data
@ApiModel(value = "BaseUserEquipmentResp", description = "机型列表")
public class BaseUserEquipmentResp implements Serializable {

    private static final long serialVersionUID = 8610179570752028046L;

    @ApiModelProperty("#主键id#")
    private Long id;

    @ApiModelProperty("#机械大类id#")
    private Long conMacId;

    @ApiModelProperty("#机械大类#")
    private String constructionMachineryName;

    @ApiModelProperty("#机型id#")
    private Long equipmentId;

    @ApiModelProperty("#机型#")
    private String equipmentName;

}
