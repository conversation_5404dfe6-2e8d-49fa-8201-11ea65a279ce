package com.rootcloud.ids.ucenter.service.backup;

import com.rootcloud.ids.ucenter.dto.backup.SysGroupAddUserDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysGroupRemoveUserDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterUserGroup;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description 系统用户-组织服务接口类
 * @InterfaceName ISysUserGroupService
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public interface IUcenterUserGroupService extends IService<UcenterUserGroup> {

    /**
     * 添加用户
     * @param groupAddUserDTO
     * @return
     */
    Boolean addUser(SysGroupAddUserDTO groupAddUserDTO);

    /**
     * 删除用户
     * @param groupRemoveUserDTO
     */
    Boolean removeUser(SysGroupRemoveUserDTO groupRemoveUserDTO);

}
