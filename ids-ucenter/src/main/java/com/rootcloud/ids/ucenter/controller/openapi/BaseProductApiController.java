package com.rootcloud.ids.ucenter.controller.openapi;

import com.rootcloud.ids.common.core.base.CommonIdsParam;
import com.rootcloud.ids.common.core.result.Result;
import com.rootcloud.ids.ucenter.entity.base.BaseDeptProRel;
import com.rootcloud.ids.ucenter.service.base.IBaseDeptProRelService;
import com.rootcloud.ids.ucenter.service.base.IBaseProductService;
import com.rootcloud.ids.ucenter.service.openapi.BaseProductApiService;
import com.rootcloud.ids.ucenter.utils.convertor.base.BaseProductRespConvertor;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDepartmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseProductDetailResp;
import com.rootcloud.esmp.common.dto.cache.BaseProductResp;
import com.rootcloud.ids.ucenter.vo.resp.openapi.BaseProductApiResp;
import com.rootcloud.ids.ucenter.vo.resp.openapi.BaseProductDetailApiResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: ling.kang
 * @create: 2022-03-01 14:55
 **/
@Api(value = "BaseProductApiController", tags = "openApi-产品分类管理")
@RestController
@Slf4j
@RequestMapping("/api/open/product")
public class BaseProductApiController {

    @Autowired
    private BaseProductApiService baseProductApiService;
    @Autowired
    private IBaseProductService iBaseProductService;
    @Autowired
    private IBaseDeptProRelService iBaseDeptProRelService;

    @ApiOperation(value = "获得所有机型部门关系列表", notes = "获得所有机型部门关系列表")
    @GetMapping(value = "/listDeptProduct")
    public Result<List<BaseDeptProRel>> listDeptPro() {
        return Result.success(iBaseDeptProRelService.list());
    }

    @ApiOperation(value = "产品分类结构列表", notes = "产品分类结构列表")
    @GetMapping(value = "/list")
    public Result<List<BaseProductApiResp>> listProduct() {
        return Result.success(baseProductApiService.listProduct());
    }

    @ApiOperation(value = "机型结构列表", notes = "机型结构列表")
    @GetMapping(value = "/equipmentList")
    public Result<List<BaseProductApiResp>> equipmentList() {
        return Result.success(baseProductApiService.equipmentList(true));
    }

    @ApiOperation(value = "机型结构列表-单租户", notes = "机型结构列表-单租户")
    @GetMapping(value = "/listEquipment")
    public Result<List<BaseProductApiResp>> listEquipment() {
        return Result.success(baseProductApiService.equipmentList(false));
    }

    @ApiOperation(value = "设备型号列表", notes = "设备型号列表")
    @GetMapping(value = "/modelList")
    public Result<List<BaseProductApiResp>> modelList() {
        return Result.success(baseProductApiService.modelList());
    }

    @ApiOperation(value = "产品分类(机械大类、机型、设备号)详情", notes = "产品分类(机械大类、机型、设备号)详情")
    @GetMapping(value = "/getById")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", defaultValue = "", value = "主键ID", required = true),
    })
    public Result<BaseProductDetailApiResp> getById(@RequestParam("id") Long id) {
        log.info("getById:{}", id);
        return Result.success(baseProductApiService.getInfo(id));
    }

    @ApiOperation(value = "查询机型的名称列表", notes = "查询机型的名称列表")
    @PostMapping(value = "/listProductName")
    public Result<List<BaseProductApiResp>> listProductName(@RequestBody @Valid CommonIdsParam param) {
        log.info("listProductName:{}", param.toString());
        return Result.success(iBaseProductService.listProductName(param.getIds()));
    }

    @ApiOperation(value = "查询机型信息", notes = "查询机型信息")
    @GetMapping(value = "/productInfo/{equipmentModelId}")
    public Result<List<BaseProductApiResp>> productInfo(
            @PathVariable(name = "equipmentModelId") String equipmentModelId) {
        log.info("productInfo:{}", equipmentModelId);
        return Result.success(iBaseProductService.productInfo(equipmentModelId));
    }

    @ApiOperation(value = "查询组织信息", notes = "查询组织信息")
    @GetMapping(value = "/groupInfo/{productId}")
    public Result<List<BaseDepartmentResp>> groupInfo(
            @PathVariable(name = "productId") String productId) {
        log.info("groupInfo:{}", productId);
        return Result.success(iBaseProductService.groupInfo(productId));
    }

    @ApiOperation(value = "所有机械大类(包含公用)", notes = "所有机械大类(包含公用)")
    @GetMapping(value = "/listMachineryPub")
    public Result<List<BaseProductResp>> listMachineryPub() {
        return Result.success(iBaseProductService.listMachineryPub().stream().map(BaseProductRespConvertor::change2ProductResp).collect(Collectors.toList()));
    }

    @ApiOperation(value = "设备号详情", notes = "设备号详情详情")
    @GetMapping(value = "/getEquipmentModelById")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", defaultValue = "", value = "设备详情主键ID", required = true),
    })
    public Result<BaseProductDetailResp> getEquipmentModelById(@RequestParam("id") Long id) {
        log.info("getEquipmentModelById:{}", id);
        return Result.success(iBaseProductService.getEquipmentModelById(id));
    }

    @ApiOperation(value = "设备号详情", notes = "设备号详情详情")
    @GetMapping(value = "/getEquipmentModelByIds")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", defaultValue = "", value = "设备详情主键ID集合", required = true),
    })
    public Result<List<BaseProductDetailResp>> getEquipmentModelByIds(@RequestParam("ids") List<Long> ids) {
    	log.info("getEquipmentModelByIds:{}", ids);
    	
    	return Result.success(iBaseProductService.getEquipmentModelByIds(ids));
    }

    @ApiOperation(value = "设备号详情", notes = "设备号详情详情")
    @GetMapping(value = "/getEquipmentModelByCode")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", defaultValue = "", value = "设备详情主键ID", required = true),
    })
    public Result<BaseProductDetailResp> getEquipmentModelByCode(@RequestParam("businessCode") String businessCode) {
        log.info("getEquipmentModelByCode:{}", businessCode);
        return Result.success(iBaseProductService.getEquipmentModelByCode(businessCode));
    }
}
