package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BaseDTO;
import com.rootcloud.ids.common.core.enums.UserCardTypeEnum;
import com.rootcloud.ids.common.core.enums.UserRegFromEnum;
import com.rootcloud.ids.common.core.enums.UserSexEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description 用户保存DTO对象
 * @ClassName SysUserSaveDTO
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "用户保存DTO对象")
@Getter
@Setter
public class SysUserSaveDTO extends BaseDTO {

    @ApiModelProperty(value = "账号", required = true)
    @NotBlank(message = "账号不能为空")
    private String account;

    @ApiModelProperty(value = "真实姓名")
    private String userName;

    @ApiModelProperty(value = "用户密码", required = true)
    @NotBlank(message = "用户密码不能为空")
    private String password;

    @ApiModelProperty(value = "用户昵称", required = true)
    @NotBlank(message = "用户昵称不能为空")
    private String nickName;

    @ApiModelProperty(value = "手机号码", required = true)
    @NotBlank(message = "手机号码不能为空")
    private String phone;

    @ApiModelProperty(value = "用户性别 1男，0女，2其它", required = true)
    @NotNull(message = "用户性别不能为空")
    private UserSexEnum sex;

    @ApiModelProperty(value = "电子邮箱")
    private String email;

    @ApiModelProperty(value = "头像链接")
    private String portrait;

    @ApiModelProperty(value = "证件类型（1：身份证，2：军官证，3：驾驶证）", required = true)
    @NotNull(message = "证件类型不能为空")
    private UserCardTypeEnum cardType;

    @ApiModelProperty(value = "证件号码", required = true)
    @NotBlank(message = "证件号码不能为空")
    private String cardNo;

    @ApiModelProperty(value = "注册来源（1：App，2：Web，3：Wechat，4：Unknown）", required = true)
    @NotNull(message = "注册来源不能为空")
    private UserRegFromEnum registFrom;

}
