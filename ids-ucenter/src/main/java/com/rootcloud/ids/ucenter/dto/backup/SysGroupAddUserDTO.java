package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @Description 组织添加用户DTO
 * @ClassName SysGroupAddUserDTO
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "组织添加用户DTO")
@Getter
@Setter
public class SysGroupAddUserDTO extends BaseDTO {

    @ApiModelProperty(value = "组织ID")
    @NotNull(message = "组织ID不能为空")
    private Long groupId;

    @ApiModelProperty(value = "用户ID数组")
    @NotNull(message = "用户ID数组不能为空")
    private Long[] userIds;

}
