package com.rootcloud.ids.ucenter.service.backup.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.rootcloud.ids.common.core.utils.IdUtils;
import com.rootcloud.ids.common.core.utils.ReStrUtils;
import com.rootcloud.ids.ucenter.dto.backup.SysUserCondition;
import com.rootcloud.ids.ucenter.dto.backup.SysUserResetPasswordDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysUserSaveDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysUserUpdateDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterUser;
import com.rootcloud.ids.common.core.enums.UserStateEnum;
import com.rootcloud.ids.ucenter.dao.backup.UcenterUserMapper;
import com.rootcloud.ids.ucenter.service.backup.IUcenterUserService;
import com.rootcloud.ids.ucenter.vo.backup.SysUserQueryVO;
import com.rootcloud.ids.ucenter.vo.backup.SysUserVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 系统用户服务实现类
 * @ClassName SysUserServiceImpl
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Service(value = "userService")
public class UcenterUserServiceImpl extends ServiceImpl<UcenterUserMapper, UcenterUser> implements
    IUcenterUserService {

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public Long save(SysUserSaveDTO userSaveDTO) {
        UcenterUser user = new UcenterUser();
        BeanUtil.copyProperties(userSaveDTO, user);
        user.setSex(userSaveDTO.getSex().getCode());
        user.setCardType(userSaveDTO.getCardType().getCode());
        long userId = IdUtils.nextId();
        user.setUserId(userId);
        user.setPassword(passwordEncoder.encode(userSaveDTO.getPassword()));
        user.setState(UserStateEnum.OPEN.getCode());
        this.save(user);
        return user.getUserId();
    }

    @Override
    public Boolean update(SysUserUpdateDTO userUpdateDTO) {
        UcenterUser user = new UcenterUser();
        BeanUtil.copyProperties(userUpdateDTO, user);
        user.setCardType(userUpdateDTO.getCardType().getCode());
        return this.updateById(user);
    }

    @Override
    public SysUserVO findById(Long userId) {
        SysUserVO userVO = new SysUserVO();
        UcenterUser user = this.getById(userId);
        BeanUtil.copyProperties(user, userVO);
        //信息脱敏
        userVO.setCardNo(ReStrUtils.reCard(userVO.getCardNo()));
        userVO.setUserName(ReStrUtils.reName(userVO.getUserName()));
        return userVO;
    }

    @Override
    public SysUserVO findByAccount(String account) {
        SysUserVO userVO = new SysUserVO();
        QueryWrapper<UcenterUser> wrapper = new QueryWrapper<>();
        wrapper.eq("account", account);
        UcenterUser user = this.getOne(wrapper);
        BeanUtil.copyProperties(user, userVO);
        return userVO;
    }

    @Override
    public Boolean batchDelete(Long[] userIds) {
        return this.removeByIds(Convert.toList(Integer.class, userIds));
    }

    @Override
    public Boolean resetPassword(SysUserResetPasswordDTO userResetPasswordDTO) {
        UcenterUser user = this.getById(userResetPasswordDTO.getUserId());
        if (user != null && passwordEncoder.matches(userResetPasswordDTO.getPasswordOld(),user.getPassword())) {
            user.setPassword(passwordEncoder.encode(userResetPasswordDTO.getPasswordNew()));
            this.updateById(user);
            return true;
        }
        return false;
    }

    @Override
    public PageInfo<SysUserQueryVO> query(SysUserCondition cnd) {
        PageHelper.startPage(cnd.getPageSize(), cnd.getPage());
        List<SysUserQueryVO> userVOList = this.baseMapper.query(cnd);
        PageInfo<SysUserQueryVO> pageInfo = new PageInfo(userVOList);
        return pageInfo;
    }

}
