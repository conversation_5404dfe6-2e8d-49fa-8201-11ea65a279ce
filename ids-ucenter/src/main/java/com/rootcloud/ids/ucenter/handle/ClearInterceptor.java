package com.rootcloud.ids.ucenter.handle;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.rootcloud.ids.common.core.constant.SecurityConstants;
import com.rootcloud.ids.common.web.dto.IamJwtParseDTO;
import com.rootcloud.ids.common.web.dto.IamUserInfoDTO;
import com.rootcloud.esmp.common.dto.cache.UserDTO;
import com.rootcloud.ids.common.web.utils.JwtUtils;
import com.rootcloud.esmp.common.utils.SecurityUtils;
import com.rootcloud.esmp.iam.client.IamOrganizationClient;
import com.rootcloud.esmp.iam.client.IamOrganizationUserClient;
import com.rootcloud.esmp.common.dto.iam.organization.OrganizationInfoDTO;
import com.rootcloud.esmp.common.dto.iam.organizationUser.OrganizeUserInfoDTO;
import com.rootcloud.esmp.common.dto.cache.TokenAndOrganizationUserCachedDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.HandlerInterceptor;
import com.rootcloud.ids.ucenter.service.redis.IRedisLoginService;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2022-03-02
 */
@Slf4j
public class ClearInterceptor implements HandlerInterceptor {

    @Autowired
    private IRedisLoginService iRedisLoginService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws ParseException {
        String tokenValue = iRedisLoginService.getToken(request);

        if (StrUtil.isEmpty(tokenValue)) {
            List<String> headerList = new ArrayList<>();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String name = headerNames.nextElement();
                headerList.add(name);
            }
            log.warn("无法获得Token");
            return true;
        }
        //自给自足，丰衣足食
        IamJwtParseDTO parseDTO = JwtUtils.parseJwt(tokenValue);
        if (null == parseDTO) {
            log.warn("错误的Token: {}", tokenValue);
            return true;
        }
        TokenAndOrganizationUserCachedDTO cachedDTO = iRedisLoginService.getUserInfo(tokenValue);
        UserDTO userDTO = new UserDTO();
        if (null == cachedDTO) {
            cachedDTO = new TokenAndOrganizationUserCachedDTO();
            IamUserInfoDTO userInfoDTO = parseDTO.getUser();
            userDTO.setToken(tokenValue);
            userDTO.setTokenType(SecurityConstants.JWT_PREFIX);
            userDTO.setUserId(userInfoDTO.getId());
            userDTO.setTenantId(parseDTO.getTenantId());
            OrganizeUserInfoDTO organizeUserInfoDTO = IamOrganizationUserClient.organizationUserInfo(userDTO.getTenantId(), userDTO.getUserId(), null);
            userDTO.setDeptIdList(organizeUserInfoDTO.getDepartments().stream().map(String::valueOf).collect(Collectors.toList()));
            Optional.ofNullable(organizeUserInfoDTO.getOrganizations())
                    .map(objects -> objects.stream().map(String::valueOf).collect(Collectors.toList()))
                    .ifPresent(userDTO::setOrganizationList);
            log.info("从Token中获得租户信息: {}", userDTO);
        } else {
            //补上自己要用的
            userDTO = cachedDTO.getUserInfo();
            OrganizeUserInfoDTO organizeUser = cachedDTO.getOrganizeUserInfo();
            userDTO.setToken(tokenValue);
            userDTO.setTokenType(SecurityConstants.JWT_PREFIX);
            Optional.ofNullable(organizeUser.getOrganizations())
                    .map(objects -> objects.stream().map(String::valueOf).collect(Collectors.toList()))
                    .ifPresent(userDTO::setOrganizationList);
            Optional.ofNullable(organizeUser.getDepartments())
                    .map(objects -> objects.stream().map(String::valueOf).collect(Collectors.toList()))
                    .ifPresent(userDTO::setDeptIdList);
            log.info("从Redis缓存中获得租户信息: {}", userDTO);
        }
        //        获取组织信息
        OrganizationInfoDTO organizeInfo = IamOrganizationClient.organizationInfo(parseDTO.getTenantId(), null);
        //        转化组织部门信息
        boolean admin = organizeInfo.getAdmins() != null && organizeInfo.getAdmins().contains(userDTO.getUserId());
        //        设置管理员
        userDTO.setAdmin(admin);
        log.info("是否是组织部件管理员: {}", admin);

        cachedDTO.setUserInfo(userDTO);

        SecurityUtils.add(cachedDTO);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        SecurityUtils.remove();
    }
}
