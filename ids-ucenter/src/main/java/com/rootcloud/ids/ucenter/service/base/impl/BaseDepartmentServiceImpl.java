package com.rootcloud.ids.ucenter.service.base.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import com.rootcloud.ids.common.mybatis.utils.PageUtils;
import com.rootcloud.ids.common.mybatis.utils.SqlUtils;
import com.rootcloud.ids.common.web.exception.BizException;
import com.rootcloud.esmp.iam.client.IamDepartmentClient;
import com.rootcloud.esmp.iam.client.IamDepartmentUserClient;
import com.rootcloud.esmp.iam.client.IamOrganizationUserClient;
import com.rootcloud.esmp.common.dto.IamBaseDTO;
import com.rootcloud.esmp.common.dto.iam.department.DepartmentsDTO;
import com.rootcloud.esmp.common.dto.iam.departmentUser.DeptUserInfoDTO;
import com.rootcloud.esmp.common.dto.iam.departmentUser.DeptUsersDTO;
import com.rootcloud.esmp.common.dto.iam.organizationUser.OrganizationUsersDTO;
import com.rootcloud.ids.ucenter.entity.base.BaseDeptProRel;
import com.rootcloud.ids.ucenter.entity.base.BaseProUserRel;
import com.rootcloud.ids.ucenter.entity.base.BaseProduct;
import com.rootcloud.ids.ucenter.enums.base.AuthTypeEnum;
import com.rootcloud.ids.ucenter.enums.base.DefaultFlagEnum;
import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import com.rootcloud.esmp.common.enums.ProductTypeEnum;
import com.rootcloud.ids.ucenter.enums.operationlog.UcenterOperationModuleEnum;
import com.rootcloud.ids.ucenter.enums.operationlog.UcenterOperationTypeEnum;
import com.rootcloud.ids.ucenter.service.base.*;
import com.rootcloud.ids.ucenter.service.dmp.IDmpService;
import com.rootcloud.ids.ucenter.service.operationlog.IOperationLogService;
import com.rootcloud.ids.ucenter.service.redis.IRedisDepartmentService;
import com.rootcloud.ids.ucenter.service.redis.IRedisProductService;
import com.rootcloud.ids.ucenter.utils.Constants;
import com.rootcloud.ids.ucenter.utils.IamUtils;
import com.rootcloud.ids.ucenter.utils.convertor.ConvertUtils;
import com.rootcloud.ids.ucenter.utils.convertor.base.BaseDepartmentRespConvertor;
import com.rootcloud.ids.ucenter.utils.convertor.base.BaseDeptProRelRespConvertor;
import com.rootcloud.ids.ucenter.utils.convertor.base.BaseProductRespConvertor;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDepartmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDeptEquipmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseEquipmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseOrganizationResp;
import com.rootcloud.ids.ucenter.vo.rest.base.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.rootcloud.ids.ucenter.utils.convertor.base.BaseDepartmentRespConvertor.change2ODeptRespList;

/**
 * <p>
 * 组织机构表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Slf4j
@Service
public class BaseDepartmentServiceImpl implements IBaseDepartmentService {

    @Autowired
    private IRedisDepartmentService iRedisDepartmentService;
    @Autowired
    private IBaseDeptProRelService iBaseDeptProRelService;
    @Autowired
    private IBaseProUserRelService iBaseProUserRelService;
    @Autowired
    private IBaseProductService iBaseProductService;
    @Autowired
    private IOperationLogService operationLogService;
    @Autowired
    private IDmpService iDmpService;
    @Lazy
    @Autowired
    private IBaseOrganizationService iBaseOrganizationService;
    @Autowired
    private IRedisProductService redisProductService;

    @Override
    public List<BaseDepartmentResp> listDepartment(Boolean parseToTree, String id) {
        List<BaseDepartmentResp> respList;
        respList = this.iRedisDepartmentService.get(id);
        if (CollUtil.isEmpty(respList)) {
            List<DepartmentsDTO> result = IamDepartmentClient.departments(parseToTree, id, null);
            if (null == result) {
                return respList;
            }
            respList = BaseDepartmentRespConvertor.change2OrganizationRespList(result.listIterator());
//            respList.forEach(obj -> obj.setCid(id));
            this.iRedisDepartmentService.set(id, respList);
        }
        return respList;
    }

    @Override
    public Page<BaseDeptEquipmentResp> pageDept(BaseDeptEquipmentPageRest param) {
        List<BaseDeptProRel> relList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(param.getId()), param.getDeptType());
        if (CollUtil.isEmpty(relList) && param.getPossess()) {
            return param.batisPage();
        }
        Map<Long, String> machineryMap = this.iBaseProductService.allConstructionMachinery().stream().collect(Collectors.toMap(BaseProduct::getId, BaseProduct::getProductName, (v1, v2) -> v1));

        LambdaQueryWrapper<BaseProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseProduct::getProductType, ProductTypeEnum.EQUIPMENT);
        if (param.getPossess()) {
            wrapper.in(BaseProduct::getId, relList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet()));
        } else {
            List<BaseDeptProRel> organizeRelList;
            if (param.getDeptType() == DeptTypeEnum.DEPARTMENT) {
                //未拥有的权限，并且是部门，查上级部门操作权/组织所有权的机型
                if (StrUtil.isNotEmpty(param.getParentId())) {
                    organizeRelList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(param.getParentId()), DeptTypeEnum.DEPARTMENT);
                } else {
                    organizeRelList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(param.getCid()), DeptTypeEnum.ORGANIZATION);
                }
                if (CollUtil.isEmpty(organizeRelList)) {
                    return param.batisPage();
                }
                wrapper.in(CollUtil.isNotEmpty(organizeRelList), BaseProduct::getId, organizeRelList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet()));
            } else {
                //未拥有的权限，并且是组织，只能查未分配所有权的机型，这个sprint不做所属权=
                //所有权已分配并且不在这个组织下的机型
                organizeRelList = this.iBaseDeptProRelService.listNoInByCidList(Collections.singletonList(param.getId()));
                wrapper.notIn(CollUtil.isNotEmpty(organizeRelList), BaseProduct::getId, organizeRelList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet()));
            }
            wrapper.notIn(CollUtil.isNotEmpty(relList), BaseProduct::getId, relList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet()));

        }
        wrapper.eq(param.getConMacId() != null, BaseProduct::getParentId, param.getConMacId());
        wrapper.like(StrUtil.isNotEmpty(param.getSearchKey()), BaseProduct::getProductName, SqlUtils.likeEscape(param.getSearchKey()));
        Page<BaseProduct> page = this.iBaseProductService.page(param.batisPage(), wrapper);
        //组织信息
        Map<String, String> organizeMap = this.iBaseOrganizationService.list()
                .stream().collect(Collectors.toMap(BaseOrganizationResp::getId, BaseOrganizationResp::getName, (v1, v2) -> v1));

        Map<Long, String> relMap = this.iBaseDeptProRelService.listOrganisation(page.getRecords().stream().map(BaseProduct::getId).collect(Collectors.toSet()))
                .stream().filter(obj -> obj.getAuthType() == AuthTypeEnum.OWNER).collect(Collectors.toMap(BaseDeptProRel::getProductId, BaseDeptProRel::getDeptId, (v1, v2) -> v1));
        return PageUtils.convert(page, obj -> {
            BaseDeptEquipmentResp resp = BaseProductRespConvertor.change2BaseDeptEquipmentResp(obj);
            resp.setDeptType(param.getDeptType());
            resp.setConstructionMachineryName(machineryMap.get(resp.getConMacId()));
            resp.setDeptId(relMap.get(resp.getEquipmentId()));
            resp.setDeptName(organizeMap.get(resp.getDeptId()));
            return resp;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addEquipment(BaseDeptEquipmentAddRest param) {
        if (CollUtil.isEmpty(param.getEquipmentList())) {
            return true;
        }
        List<BaseDeptProRel> deptProRelList = new ArrayList<>();
        List<BaseProUserRel> proUserRelList = new ArrayList<>();
        List<BaseDepartmentResp> subDeptList = null;
        List<BaseProUserRel> proUserList;
        BaseOrganizationResp baseOrgResp;
        if (DeptTypeEnum.ORGANIZATION == param.getDeptType()) {
            //查询组织列表
            List<BaseOrganizationResp> organizationRespList = iBaseOrganizationService.list();
            baseOrgResp = organizationRespList.stream().filter(resp -> resp.getId().equals(param.getDeptId())).findFirst()
                    .orElseThrow(() -> new BizException(I18nUtil.message(I18nCode.SYS_100118)));
            //获取组织下所有部门信息
            subDeptList = IamUtils.getOrgDept(baseOrgResp.getDeptList());
        }

        switch (param.getDeptType()) {
            case DEPARTMENT:
                //查询当前部门的用户列表
                DeptUsersDTO deptUsersDTO = IamDepartmentUserClient.users(param.getDeptId(), false, false, null, null, null);
                proUserList = iBaseProUserRelService.listByUserId(deptUsersDTO.getResults().stream().map(DeptUserInfoDTO::getId).collect(Collectors.toList()));
                List<Long> equipmentIds = param.getEquipmentList().stream().map(BaseDeptEquipmentAddDTO::getEquipmentId).collect(Collectors.toList());
                Map<Long, String> ownerMap = this.iBaseDeptProRelService.listByProductIdAndAuthType(equipmentIds, AuthTypeEnum.OWNER)
                        .stream().collect(Collectors.toMap(BaseDeptProRel::getProductId, BaseDeptProRel::getDeptId));
                for (BaseDeptEquipmentAddDTO rel : param.getEquipmentList()) {
                    //部门和产品关系
                    deptProRelList.add(BaseDeptProRelRespConvertor.change2Entity(param.getDeptId(), rel.getEquipmentId(), param.getDeptType(), AuthTypeEnum.OWNER_USE, param.getCid(), ownerMap.get(rel.getEquipmentId())));
                    if (CollUtil.isNotEmpty(deptUsersDTO.getResults())) {
                        Map<String, List<BaseProUserRel>> userList = proUserList.stream().collect(Collectors.groupingBy(BaseProUserRel::getUserId));
                        List<String> ids = deptUsersDTO.getResults().stream().map(IamBaseDTO::getId).collect(Collectors.toList());
                        proUserRelList.addAll(ConvertUtils.getProUseRelList(ids, rel.getEquipmentId(), userList));
                    }
                }

                break;
            case ORGANIZATION:
                //查询租户下所有用户
                OrganizationUsersDTO allDeptUsers =
                        IamOrganizationUserClient.organizationUsers(param.getDeptId(), false, false, null, false, null, null, null, null);
                proUserList = iBaseProUserRelService.listByUserId(allDeptUsers.getResults().stream().map(DeptUserInfoDTO::getId).collect(Collectors.toList()));
                for (BaseDeptEquipmentAddDTO rel : param.getEquipmentList()) {
                    //组织和产品关系
                    deptProRelList.add(BaseDeptProRelRespConvertor.change2Entity(param.getDeptId(), rel.getEquipmentId(), param.getDeptType(), AuthTypeEnum.OWNER, param.getDeptId(), param.getDeptId()));
                    //组织层级新增机型默认操作
                    if (DefaultFlagEnum.DEFAULT.getCode() != rel.getDefaultFlag()) {
                        continue;
                    }

                    Optional.ofNullable(subDeptList).ifPresent(list -> list.forEach(resp ->
                            deptProRelList.add(BaseDeptProRelRespConvertor.change2Entity(resp.getId(), rel.getEquipmentId(), resp.getDeptType(), AuthTypeEnum.OWNER_USE, param.getDeptId(), param.getDeptId()))));
                    if (CollUtil.isNotEmpty(allDeptUsers.getResults())) {
                        Map<String, List<BaseProUserRel>> userList = proUserList.stream().collect(Collectors.groupingBy(BaseProUserRel::getUserId));
                        List<String> ids = allDeptUsers.getResults().stream().map(IamBaseDTO::getId).collect(Collectors.toList());
                        proUserRelList.addAll(ConvertUtils.getProUseRelList(ids, rel.getEquipmentId(), userList));
                    }
                }
            default:
                break;
        }

        //保存用户机型权限
        if (CollUtil.isNotEmpty(proUserRelList)) {
            iBaseProUserRelService.saveBatch(proUserRelList);
        }
        //保存部门和机型权限
        boolean flag = this.iBaseDeptProRelService.saveBatch(deptProRelList);
        if (flag) {
            List<BaseProduct> baseProducts = iBaseProductService.list(Wrappers.lambdaQuery(BaseProduct.class)
                    .in(BaseProduct::getId, param.getEquipmentList().stream().map(BaseDeptEquipmentAddDTO::getEquipmentId).collect(Collectors.toList()))
            );
            //保存日志对象
            String logContent = Constants.OPERATIONLOG_DEPT_ADD() + baseProducts.stream().map(BaseProduct::getProductName).collect(Collectors.toList()) + Constants.OPERATIONLOG_EQUIPMENT_NAME();
            String logDetail = baseProducts.stream().map(BaseProduct::getProductName).collect(Collectors.toList()).toString();
            operationLogService.saveOperationLog(param.getDeptId(),
                    UcenterOperationModuleEnum.ORGANIZATION_USER_PERMISSIONS, UcenterOperationTypeEnum.TYPE_CREATE,
                    logContent, logDetail, null, Constants.OPERATIONLOG_SUCCESS);
        }
        //刷新缓存
        iDmpService.refreshCache();
        //删除缓存
        redisProductService.del();
        return flag;
    }

    @Override
    public List<BaseEquipmentResp> getEquipment(BaseDeptEquipmentRest param) {
        List<BaseDeptProRel> baseDeptProRels = iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(param.getId()), param.getDeptType());
        if (CollUtil.isEmpty(baseDeptProRels)) {
            return new ArrayList<>();
        }
        List<BaseProduct> baseProducts = iBaseProductService.list(Wrappers.lambdaQuery(BaseProduct.class)
                .eq(BaseProduct::getProductType, ProductTypeEnum.EQUIPMENT)
                .in(BaseProduct::getId, baseDeptProRels.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet())));
        return change2ODeptRespList(baseProducts.listIterator());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean removeEquipment(BaseDeptEquipmentDelRest param) {
        //查询机型
        BaseProduct product = iBaseProductService.getOne(Wrappers.lambdaQuery(BaseProduct.class)
                .eq(BaseProduct::getId, param.getEquipmentId())
                .eq(BaseProduct::getProductType, ProductTypeEnum.EQUIPMENT));
        if (null == product) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100117));
        }
        //查询部门信息
        DepartmentsDTO departmentsDTO = IamDepartmentClient.department(param.getDeptId(), false, null);
        //获取子部门id集合
        List<String> subDeptList = IamUtils.getSubDept(param);
        if (CollUtil.isNotEmpty(subDeptList)) {
            subDeptList.add(param.getDeptId());
        }
        LambdaQueryWrapper<BaseDeptProRel> deptProWrapper = new LambdaQueryWrapper<>();
        deptProWrapper.in(BaseDeptProRel::getDeptId, CollUtil.isNotEmpty(subDeptList) ? subDeptList : Collections.singletonList(param.getDeptId()));
        deptProWrapper.eq(BaseDeptProRel::getProductId, param.getEquipmentId());
        //查询部门下所有的用户信息(包含子部门)
        DeptUsersDTO deptUsersDTO = IamDepartmentUserClient.users(param.getDeptId(), true, false, null, null, null);
        if (CollUtil.isNotEmpty(deptUsersDTO.getResults())) {
            LambdaQueryWrapper<BaseProUserRel> proUserWrapper = new LambdaQueryWrapper<>();
            proUserWrapper.in(BaseProUserRel::getUserId, deptUsersDTO.getResults().stream().map(DeptUserInfoDTO::getId).collect(Collectors.toSet()));
            proUserWrapper.eq(BaseProUserRel::getProductId, param.getEquipmentId());
            iBaseProUserRelService.remove(proUserWrapper);
            //保存日志对象
            String logContent = UcenterOperationTypeEnum.TYPE_REMOVE.getLabel() + departmentsDTO.getName()
                    + "[" + product.getProductName() + "]" + UcenterOperationTypeEnum.TYPE_DATA_AUTHORITY.getLabel();
            String logDetail = product.getProductName();
            operationLogService.saveOperationLog(param.getDeptId(),
                    UcenterOperationModuleEnum.ORGANIZATION_USER_PERMISSIONS, UcenterOperationTypeEnum.TYPE_REMOVE,
                    logContent, logDetail, null, Constants.OPERATIONLOG_SUCCESS);
            iDmpService.refreshCache();
            //删除缓存
            redisProductService.del();
        }
        iBaseDeptProRelService.remove(deptProWrapper);
        //删除被IAM删除了的部门，但是在数据库还存留的数据
        dealWithPreserveData(param.getCid(), param.getEquipmentId());
        return true;
    }

    private void dealWithPreserveData(String cid, Long equipmentId) {
        //获取子部门id集合
        List<String> subDeptList = IamUtils.getSubDept(cid);
        List<BaseDeptProRel> delList = new ArrayList<>();
        List<BaseDeptProRel> relList = this.iBaseDeptProRelService.listByCidAndProductId(cid, equipmentId);
        if (CollUtil.isNotEmpty(subDeptList)) {
            for (BaseDeptProRel rel : relList) {
                if (!subDeptList.contains(rel.getDeptId()) && !Objects.equals(rel.getDeptId(), cid)) {
                    delList.add(rel);
                }
            }
        } else {
            delList = relList.stream().filter(obj -> !Objects.equals(obj.getDeptId(), cid)).collect(Collectors.toList());
        }

        if (CollUtil.isNotEmpty(delList)) {
            this.iBaseDeptProRelService.removeByIds(delList.stream().map(BaseDeptProRel::getId).collect(Collectors.toList()));
        }
    }

    @Override
    public Collection<Long> pagePossessIds(BaseDeptEquipmentPageRest param) {
        List<BaseDeptProRel> relList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(param.getId()), param.getDeptType());
        if (CollUtil.isEmpty(relList) && param.getPossess()) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<BaseProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseProduct::getProductType, ProductTypeEnum.EQUIPMENT);
        if (param.getPossess()) {
            wrapper.in(BaseProduct::getId, relList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet()));
        } else {
            List<BaseDeptProRel> organizeRelList;
            if (param.getDeptType() == DeptTypeEnum.DEPARTMENT) {
                //未拥有的权限，并且是部门，查上级部门操作权/组织所有权的机型
                if (StrUtil.isNotEmpty(param.getParentId())) {
                    organizeRelList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(param.getParentId()), DeptTypeEnum.DEPARTMENT);
                } else {
                    organizeRelList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(param.getCid()), DeptTypeEnum.ORGANIZATION);
                }
                if (CollUtil.isEmpty(organizeRelList)) {
                    return new ArrayList<>();
                }
                wrapper.in(CollUtil.isNotEmpty(organizeRelList), BaseProduct::getId, organizeRelList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet()));
            } else {
                //未拥有的权限，并且是组织，只能查未分配所有权的机型，这个sprint不做所属权=
                //所有权已分配并且不在这个组织下的机型
                organizeRelList = this.iBaseDeptProRelService.listNoInByCidList(Collections.singletonList(param.getId()));
                wrapper.notIn(CollUtil.isNotEmpty(organizeRelList), BaseProduct::getId, organizeRelList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet()));
            }
            wrapper.notIn(CollUtil.isNotEmpty(relList), BaseProduct::getId, relList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet()));
        }
        wrapper.like(StrUtil.isNotEmpty(param.getSearchKey()), BaseProduct::getProductName, SqlUtils.likeEscape(param.getSearchKey()));
        wrapper.select(BaseProduct::getParentId);
        List<BaseProduct> baseProducts = this.iBaseProductService.list(wrapper);
        return baseProducts.stream().map(BaseProduct::getParentId).collect(Collectors.toSet());
    }

    @Override
    public Page<BaseDeptEquipmentResp> pageOwner(BaseDeptEquipmentPageRest param) {
        List<BaseDeptProRel> relList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(param.getId()), param.getDeptType())
                .stream().filter(obj -> obj.getAuthType() == AuthTypeEnum.OWNER).collect(Collectors.toList());
        if (CollUtil.isEmpty(relList)) {
            return param.batisPage();
        }

        LambdaQueryWrapper<BaseProduct> wrapper = this.buildWrapper(param, relList);
        Page<BaseProduct> page = this.iBaseProductService.page(param.batisPage(), wrapper);

        Map<Long, String> machineryMap = this.iBaseProductService.allConstructionMachinery().stream().collect(Collectors.toMap(BaseProduct::getId, BaseProduct::getProductName, (v1, v2) -> v1));
        return PageUtils.convert(page, obj -> {
            BaseDeptEquipmentResp resp = BaseProductRespConvertor.change2BaseDeptEquipmentResp(obj);
            resp.setDeptType(param.getDeptType());
            resp.setConstructionMachineryName(machineryMap.get(resp.getConMacId()));
            return resp;
        });
    }

    @Override
    public Page<BaseDeptEquipmentResp> pageUse(BaseDeptEquipmentPageRest param) {
        List<BaseDeptProRel> relList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(param.getId()), param.getDeptType())
                .stream().filter(obj -> obj.getAuthType() == AuthTypeEnum.OTHER_USE).collect(Collectors.toList());
        if (CollUtil.isEmpty(relList)) {
            return param.batisPage();
        }

        LambdaQueryWrapper<BaseProduct> wrapper = this.buildWrapper(param, relList);
        Page<BaseProduct> page = this.iBaseProductService.page(param.batisPage(), wrapper);
        if (CollUtil.isEmpty(page.getRecords())) {
            return param.batisPage();
        }
        Set<Long> productIds = page.getRecords().stream().map(BaseProduct::getId).collect(Collectors.toSet());
        Map<Long, String> machineryMap = this.iBaseProductService.allConstructionMachinery().stream().collect(Collectors.toMap(BaseProduct::getId, BaseProduct::getProductName, (v1, v2) -> v1));

        Map<Long, String> relMap = this.iBaseDeptProRelService.listByProductIdsAndDeptTypeAndAuthType(productIds, DeptTypeEnum.ORGANIZATION, AuthTypeEnum.OWNER)
                .stream().collect(Collectors.toMap(BaseDeptProRel::getProductId, BaseDeptProRel::getDeptId, (v1, v2) -> v1));
        //组织信息
        Map<String, String> organizeMap = this.iBaseOrganizationService.list()
                .stream().collect(Collectors.toMap(BaseOrganizationResp::getId, BaseOrganizationResp::getName, (v1, v2) -> v1));
        return PageUtils.convert(page, obj -> {
            BaseDeptEquipmentResp resp = BaseProductRespConvertor.change2BaseDeptEquipmentResp(obj);
            resp.setDeptId(relMap.get(resp.getEquipmentId()));
            resp.setConstructionMachineryName(machineryMap.get(resp.getConMacId()));
            resp.setDeptName(organizeMap.get(resp.getDeptId()));
            return resp;
        });
    }

    private LambdaQueryWrapper<BaseProduct> buildWrapper(BaseDeptEquipmentPageRest param, List<BaseDeptProRel> relList) {
        LambdaQueryWrapper<BaseProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseProduct::getProductType, ProductTypeEnum.EQUIPMENT);
        wrapper.in(BaseProduct::getId, relList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toList()));
        wrapper.eq(param.getConMacId() != null, BaseProduct::getParentId, param.getConMacId());
        wrapper.like(StrUtil.isNotEmpty(param.getSearchKey()), BaseProduct::getProductName, SqlUtils.likeEscape(param.getSearchKey()));
        return wrapper;
    }


}
