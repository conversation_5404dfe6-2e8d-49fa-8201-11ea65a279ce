package com.rootcloud.ids.ucenter.utils;

import java.time.LocalDateTime;
import java.util.Comparator;

/**
 * <AUTHOR>
 */
public class Util {

  /**
   * LocalDateTime 降序排序 null的排在后面
   */
  public static Comparator<LocalDateTime> LOCAL_DATETIME_DESC_COMPARATOR = (d1, d2) -> {
    if (d1 == null) {
      return 1;
    }
    if (d2 == null) {
      return -1;
    }
    return d1.isEqual(d2) ? 0 : d1.isAfter(d2) ? -1 : 1;
  };

  /**
   * LocalDateTime 升序排序 null的排在后面
   */
  public static Comparator<LocalDateTime> LOCAL_DATETIME_ASC_COMPARATOR = (d1, d2) -> {
    if (d1 == null) {
      return 1;
    }
    if (d2 == null) {
      return -1;
    }
    return d1.isEqual(d2) ? 0 : d1.isAfter(d2) ? 1 : -1;
  };
}
