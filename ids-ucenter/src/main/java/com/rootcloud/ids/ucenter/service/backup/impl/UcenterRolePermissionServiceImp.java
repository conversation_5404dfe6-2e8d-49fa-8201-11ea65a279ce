package com.rootcloud.ids.ucenter.service.backup.impl;

import com.rootcloud.ids.ucenter.dto.backup.SysRoleAddPermissionDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysRoleRemovePermissionDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterRolePermission;
import com.rootcloud.ids.ucenter.dao.backup.UcenterRolePermissionMapper;
import com.rootcloud.ids.ucenter.service.backup.IUcenterRolePermissionService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 系统角色-权限服务实现类
 * @ClassName SysRolePermissionServiceImp
 * <AUTHOR>
 * @Date 2021/12/21
 * @Version 1.0
 */
@Service
public class UcenterRolePermissionServiceImp extends ServiceImpl<UcenterRolePermissionMapper, UcenterRolePermission> implements
    IUcenterRolePermissionService {

    @Override
    public Boolean addPermission(SysRoleAddPermissionDTO roleAddPermissionDTO) {
        List<UcenterRolePermission> rolePermissionList = new ArrayList<>();
        for (Long pmsId : roleAddPermissionDTO.getPmsIds()) {
            UcenterRolePermission rolePermission = new UcenterRolePermission();
            rolePermission.setRoleId(roleAddPermissionDTO.getRoleId());
            rolePermission.setPmsId(pmsId);
            rolePermissionList.add(rolePermission);
        }
        return this.saveBatch(rolePermissionList);
    }

    @Override
    public Boolean removePermission(SysRoleRemovePermissionDTO roleRemovePermissionDTO) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("role_id", roleRemovePermissionDTO.getRoleId());
        queryWrapper.in("pms_id", roleRemovePermissionDTO.getPmsIds());
        return this.remove(queryWrapper);
    }
}
