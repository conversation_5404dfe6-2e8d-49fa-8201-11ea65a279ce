package com.rootcloud.ids.ucenter.vo.resp.base;

import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since  2022-02-28 14:26
 **/
@Data
@ApiModel(value = "BaseDepartmentResp", description = "部门信息")
public class BaseDepartmentResp implements Serializable {

    private static final long serialVersionUID = -6418737894934284498L;

    @ApiModelProperty("#部门id#")
    private String id;

    @ApiModelProperty("#父级id#")
    private String parentId;

    @ApiModelProperty("#组织ID#")
    private String cid;

    @ApiModelProperty("#部门名称#")
    private String name;

    @ApiModelProperty("#部门类型#")
    private DeptTypeEnum deptType;

    @ApiModelProperty("#子部门#")
    private List<BaseDepartmentResp> children;

}
