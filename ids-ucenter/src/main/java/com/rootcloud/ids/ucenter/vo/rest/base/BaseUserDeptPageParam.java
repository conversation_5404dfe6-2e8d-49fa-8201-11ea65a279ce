package com.rootcloud.ids.ucenter.vo.rest.base;

import com.rootcloud.ids.common.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: ling.kang
 * @create: 2022-02-28 16:49
 **/
@Data
@ApiModel(value = "BaseUserDeptPageParam", description = "用户列表参数")
public class BaseUserDeptPageParam extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 2967694873994066529L;
    @ApiModelProperty("#部门id#")
    private Long deptId;

    @ApiModelProperty("#用户id#")
    private Long userId;

}
