package com.rootcloud.ids.ucenter.vo.backup;

import com.rootcloud.ids.common.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 组织VO对象
 * @ClassName SysAppVO
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "组织VO对象")
@Getter
@Setter
public class SysGroupVO extends BaseVO {

    @ApiModelProperty(value = "组织ID")
    private Long groupId;

    @ApiModelProperty(value = "组织名称")
    private String groupName;

    @ApiModelProperty(value = "英文名称")
    private String englishName;

    @ApiModelProperty(value = "备注")
    private String remark;

}
