package com.rootcloud.ids.ucenter.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 时间工具类
 * @Param:
 * @return:
 * @Author: huiming.yin
 * @Date: 2021-11-09 16:28
 */
public class DateUtil {

  private static Map<String, SimpleDateFormat> formats;
  private static Map<String, DateTimeFormatter> dateTimeformats;
  public static final String DATE_FORMATE_STRING_DEFAULT = "yyyyMMddHHmmss";
  public static final String DATE_FORMATE_STRING_A = "yyyy-MM-dd HH:mm:ss";
  public static final String DATE_FORMATE_STRING_B = "yyyy-MM-dd";
  public static final String DATE_FORMATE_STRING_C = "MM/dd/yyyy HH:mm:ss a";
  public static final String DATE_FORMATE_STRING_D = "yyyy-MM-dd HH:mm:ss a";
  public static final String DATE_FORMATE_STRING_E = "yyyy-MM-dd'T'HH:mm:ss'Z'";
  public static final String DATE_FORMATE_STRING_F = "yyyy-MM-dd'T'HH:mm:ssZ";
  public static final String DATE_FORMATE_STRING_G = "yyyy-MM-dd'T'HH:mm:ssz";
  public static final String DATE_FORMATE_STRING_H = "yyyyMMdd";
  public static final String DATE_FORMATE_STRING_I = "yyyy-MM-dd HH:mm:ss.SSS";
  public static final String DATE_FORMATE_STRING_J = "yyyyMMddHHmmss.SSS";
  public static final String DATE_FORMATE_STRING_K = "yyyyMMddHHmmssSSS";
  public static final String DATE_FORMATE_STRING_L = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
  public static final String DATE_FORMATE_STRING_M = "yyMMdd";

  static {
    formats = new HashMap<String, SimpleDateFormat>();
    dateTimeformats = new HashMap<>();
    formats.put(DATE_FORMATE_STRING_DEFAULT, new SimpleDateFormat(DATE_FORMATE_STRING_DEFAULT));
    formats.put(DATE_FORMATE_STRING_A, new SimpleDateFormat(DATE_FORMATE_STRING_A));
    formats.put(DATE_FORMATE_STRING_B, new SimpleDateFormat(DATE_FORMATE_STRING_B));
    formats.put(DATE_FORMATE_STRING_C, new SimpleDateFormat(DATE_FORMATE_STRING_C));
    formats.put(DATE_FORMATE_STRING_D, new SimpleDateFormat(DATE_FORMATE_STRING_D));
    formats.put(DATE_FORMATE_STRING_E, new SimpleDateFormat(DATE_FORMATE_STRING_E));
    formats.put(DATE_FORMATE_STRING_F, new SimpleDateFormat(DATE_FORMATE_STRING_F));
    formats.put(DATE_FORMATE_STRING_G, new SimpleDateFormat(DATE_FORMATE_STRING_G));
    formats.put(DATE_FORMATE_STRING_H, new SimpleDateFormat(DATE_FORMATE_STRING_H));
    formats.put(DATE_FORMATE_STRING_I, new SimpleDateFormat(DATE_FORMATE_STRING_I));
    formats.put(DATE_FORMATE_STRING_J, new SimpleDateFormat(DATE_FORMATE_STRING_J));
    formats.put(DATE_FORMATE_STRING_K, new SimpleDateFormat(DATE_FORMATE_STRING_K));
    formats.put(DATE_FORMATE_STRING_L, new SimpleDateFormat(DATE_FORMATE_STRING_L));
    formats.put(DATE_FORMATE_STRING_M, new SimpleDateFormat(DATE_FORMATE_STRING_M));
  }

  /**
   * 将Date转换为 pattern 格式的字符串，格式为： yyyyMMddHHmmss yyyy-MM-dd HH:mm:ss yyyy-MM-dd MM/dd/yyyy HH:mm:ss
   * a yyyy-MM-dd HH:mm:ss a yyyy-MM-dd'T'HH:mm:ss'Z' yyyy-MM-dd'T'HH:mm:ssZ yyyy-MM-dd'T'HH:mm:ssz
   *
   * @param date 日期
   * @param pattern 日期格式
   * @return String --格式化的日期字符串
   * @see Date
   */
  public static String getFormatTimeString(Date date, String pattern) {
    SimpleDateFormat sDateFormat = getDateFormat(pattern);
    //单实例,SimpleDateFormat非线程安全
    synchronized (sDateFormat) {
      return sDateFormat.format(date);
    }
  }

  public static String getFormatTimeString(LocalDateTime date, String pattern) {
    DateTimeFormatter formatter = getDateTimeFormat(pattern);
    //单实例,SimpleDateFormat非线程安全
    synchronized (formatter) {
      return formatter.format(date);
    }
  }

  /**
   * 将Date转换为默认的YYYYMMDDHHMMSS 格式的字符串
   */
  public static String getDefaultFormateTimeString(LocalDateTime date) {
    return getFormatTimeString(date, DATE_FORMATE_STRING_DEFAULT);
  }

  /**
   * 根据pattern取得的date formate
   */
  public static SimpleDateFormat getDateFormat(String pattern) {
    SimpleDateFormat sDateFormat = formats.get(pattern);
    if (sDateFormat == null) {
      sDateFormat = new SimpleDateFormat(pattern);
      formats.put(pattern, sDateFormat);
    }
    return sDateFormat;
  }

  public static DateTimeFormatter getDateTimeFormat(String pattern) {
    DateTimeFormatter formatter = dateTimeformats.get(pattern);
    if (formatter == null) {
      formatter = DateTimeFormatter.ofPattern(pattern);
      dateTimeformats.put(pattern, formatter);
    }
    return formatter;
  }

  /**
   * 将格式将日期字符串转换为Date对象
   *
   * @param date 字符串
   * @param pattern 格式如下： yyyyMMddHHmmss yyyy-MM-dd HH:mm:ss yyyy-MM-dd MM/dd/yyyy HH:mm:ss a
   * yyyy-MM-dd HH:mm:ss a yyyy-MM-dd'T'HH:mm:ss'Z' yyyy-MM-dd'T'HH:mm:ssZ yyyy-MM-dd'T'HH:mm:ssz
   * @return 日期Date对象
   * @see Date
   */
  public static Date getDateFromString(String date, String pattern) throws ParseException {
    SimpleDateFormat sDateFormat = getDateFormat(pattern);
    //单实例,SimpleDateFormat非线程安全
    synchronized (sDateFormat) {
      return sDateFormat.parse(date);
    }
  }

  /**
   * 取当前时间,格式为YYYYMMDDHHMMSS的日期字符串
   *
   * @return 当前日期字符串
   * @see Date
   */
  public static String getNowDefault() {
    return getNow(DATE_FORMATE_STRING_DEFAULT);
  }

  /**
   * 取当前时间,格式为YYYYMMDDHHMMSS的日期字符串
   *
   * @return 当前日期字符串
   * @see Date
   */
  public static String getNowUtc() {
    return getNow(DATE_FORMATE_STRING_L);
  }

  /**
   * 按照pattern格式取当前日期字符串
   *
   * @param pattern 日期字符串格式
   * @return 格式化后的当前日期字符串
   */
  public static String getNow(String pattern) {
    return getFormatTimeString(new Date(), pattern);
  }

  /**
   * 将指定字符串格式的时间串转化为需要的格式时间串
   *
   * @param numdate 　入参时间
   * @param inFormat 入参格式
   * @param outFormat 　出参格式
   */
  public static String changeNumDateToDate(String numdate, String inFormat, String outFormat)
      throws ParseException {
    Date date = getDateFromString(numdate, inFormat);
    return getFormatTimeString(date, outFormat);
  }

  public static Date getDate() {
    return new Date();
  }

  public static LocalDateTime getLocalDateTime() {
    return LocalDateTime.now();
  }

  /**
   * 获取UTC时间
   */
  public static String getUtcDateString() {
    SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMATE_STRING_L);
    return sdf.format(new Date());
  }

  /**
   * Date转LocalDateTime
   */
  public static LocalDateTime toLocalDateTime(Date date) {
    Instant instant = date.toInstant();
    ZoneId zoneId = ZoneId.systemDefault();
    return instant.atZone(zoneId).toLocalDateTime();
  }


}

