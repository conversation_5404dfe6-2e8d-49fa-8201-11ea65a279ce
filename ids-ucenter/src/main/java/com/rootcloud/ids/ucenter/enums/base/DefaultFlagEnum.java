package com.rootcloud.ids.ucenter.enums.base;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * @author: kang.ling
 * @create: 2022-03-30 11:48
 **/
public enum DefaultFlagEnum implements IEnum<Integer> {
    /**
     * #组织类型 1:组织:ORGANIZATION,2:部门:DEPARTMENT#
     */
    DEFAULT(1, I18nCode.SYS_100037),
    NOT_DEFAULT(0, I18nCode.SYS_100038),
    ;
    private final int code;
    private final I18nCode label;

    DefaultFlagEnum(int code, I18nCode label) {
        this.code = code;
        this.label = label;
    }

    public int getCode() {
        return code;
    }

    public String getLabel() {
        return I18nUtil.message(label);
    }

    public static String getLabel(Integer code) {
        if (code != null) {
            for (DefaultFlagEnum value : DefaultFlagEnum.values()) {
                if (value.code == code) {
                    return value.getLabel();
                }
            }
        }
        return null;
    }

    public static DefaultFlagEnum codeOf(int code) {
        for (DefaultFlagEnum value : DefaultFlagEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new RuntimeException("cant not change code: " + code + " to DefaultFlagEnum.");
    }

    @Override
    public Integer getValue() {
        return code;
    }
    
}
