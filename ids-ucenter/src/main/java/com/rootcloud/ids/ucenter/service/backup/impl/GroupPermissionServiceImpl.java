package com.rootcloud.ids.ucenter.service.backup.impl;

import com.rootcloud.ids.ucenter.entity.backup.GroupPermission;
import com.rootcloud.ids.ucenter.dao.backup.GroupPermissionMapper;
import com.rootcloud.ids.ucenter.service.backup.IGroupPermissionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统组织权限关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
@Service
public class GroupPermissionServiceImpl extends ServiceImpl<GroupPermissionMapper, GroupPermission> implements IGroupPermissionService {

}
