package com.rootcloud.ids.ucenter.vo.rest.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: kang.ling
 * @create: 2022-03-22 16:30
 **/
@Data
@ApiModel(value = "BaseDeptEquipmentDelRest", description = "部门机型删除参数")
public class BaseDeptEquipmentDelRest {

    @ApiModelProperty("#组织ID#")
    @NotBlank(message = "组织id不能为空")
    private String cid;

    @ApiModelProperty("#部门id#")
    @NotBlank(message = "部门id不能为空")
    private String deptId;

    @ApiModelProperty("#机型id#")
    @NotNull(message = "机型id不能为空")
    private Long equipmentId;
}
