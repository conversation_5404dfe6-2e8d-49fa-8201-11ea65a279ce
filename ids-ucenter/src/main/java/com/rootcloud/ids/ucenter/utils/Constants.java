package com.rootcloud.ids.ucenter.utils;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;

/**
 * @author: kang.ling
 * @create: 2022-03-11 09:54
 **/
public class Constants {
    public static final String I18N_HEADER = "language";

    /**
     * 参数合法校验
     */
    public final static int MIN = 4;
    public final static int MAX = 32;
    public final static int AUTHENTICATE = 16;

    public static final String ID = "id";
    public static final String VERSION = "version";
    public static final String DELETE_FLAG = "delete_flag";
    public static final String CREATOR = "creator";
    public static final String MODIFIER = "modifier";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";

    public static final String EQUIPMENT_TYPE_ID = "equipment_type_id";

    public static final String LOG_TIME = "log_time";

    public static final String LOG_OPERATION_MODULE = "log_operation_module";

    public static final String LOG_OPERATION_TYPE = "log_operation_type";

    public static final String LOG_CONTENT = "log_content";

    public static final String OPERATION_RESULT = "operation_result";

    public static final String OPERATION_OPERATOR = "operation_operator";

    public static final String OPERATION_IP = "operation_ip";

    public static final String SERVICE_ID = "service_id";

    public static final String LOG_DETAIL = "log_detail";

    public static final String SYNCED = "synced";

    public static final String USER_ID = "user_id";

    public static final String CLIENT_ID = "client_id";

    public static final String TENANT_ID = "tenant_id";

    public static final String ORGANIZATION_ID = "organization_id";


    public static final String STRING_CONST_YIWEN = "?";
    public static final String STRING_CONST_YU = "&";
    public static final String STRING_CONST_POUND = "#";
    public static final String STRING_CONST_COMMA = "，";
    public static final String STRING_CONST_UTF_8 = "UTF-8";
    public static final String STRING_CONST_HYPHEN = "-";
    public static final String STRING_CONST_LOW_HYPHEN = "_";

    public static final String OPERATIONLOG_CONSTRUCTION_MACHINERY(){return I18nUtil.message(I18nCode.SYS_100063);};
    public static final String OPERATIONLOG_EQUIPMENT(){return I18nUtil.message(I18nCode.SYS_100064);};
    public static final String OPERATIONLOG_EQUIPMENT_MODEL(){return I18nUtil.message(I18nCode.SYS_100065);};

    public static final String OPERATIONLOG_UNKNOW = "UNKNOW";
    public static final String OPERATIONLOG_SUCCESS = "SUCCESS";
    public static final String OPERATIONLOG_FAIL = "FAILED";

    /**
     * 操作日志
     */
    public static final String OPERATIONLOG_UPDATE_PRE (){return I18nUtil.message(I18nCode.SYS_100066);};
    public static final String OPERATIONLOG_UPDATE_SUF (){return I18nUtil.message(I18nCode.SYS_100067);};
    public static final String OPERATIONLOG_UPGRADE_PRE (){return I18nUtil.message(I18nCode.SYS_100068);};
    public static final String OPERATIONLOG_UPGRADE_SUF (){return I18nUtil.message(I18nCode.SYS_100069);};
    public static final String OPERATIONLOG_TASK_NAME (){return I18nUtil.message(I18nCode.SYS_100070);};
    public static final String OPERATIONLOG_UPGRADE_FAILURECAUSE (){return I18nUtil.message(I18nCode.SYS_100071);};
    public static final String OPERATIONLOG_TASK_SEND_SUCESS (){return I18nUtil.message(I18nCode.SYS_100072);};
    public static final String OPERATIONLOG_TASK_SEND_FAIL (){return I18nUtil.message(I18nCode.SYS_100073);};
    public static final String OPERATIONLOG_UPDATE_THINGSMODELNAME (){return I18nUtil.message(I18nCode.SYS_100074);};
    public static final String OPERATIONLOG_UPDATE_RELATEDTHINGSMODELNAME (){return I18nUtil.message(I18nCode.SYS_100075);};
    public static final String OPERATIONLOG_UPDATE_THINGSMODEL (){return I18nUtil.message(I18nCode.SYS_100076);};
    public static final String OPERATIONLOG_AUTHENTICATEIDENTIFICATION (){return I18nUtil.message(I18nCode.SYS_100077);};
    public static final String OPERATIONLOG_AUTHENTICATESECRETKEY (){return I18nUtil.message(I18nCode.SYS_100078);};
    public static final String OPERATIONLOG_RELATEDDEVICENAME (){return I18nUtil.message(I18nCode.SYS_100079);};
    public static final String OPERATIONLOG_DEVICENAME (){return I18nUtil.message(I18nCode.SYS_100080);};
    public static final String OPERATIONLOG_OTA = "OTA";
    public static final String OPERATIONLOG_COMPONENTNAME (){return I18nUtil.message(I18nCode.SYS_100081);};
    public static final String OPERATIONLOG_COMPONENT (){return I18nUtil.message(I18nCode.SYS_100082);};
    public static final String OPERATIONLOG_COMPONENT_NODE (){return I18nUtil.message(I18nCode.SYS_100083);};

    public static final String OPERATIONLOG_MODEL (){return I18nUtil.message(I18nCode.SYS_100084);};
    public static final String OPERATIONLOG_DEVICE (){return I18nUtil.message(I18nCode.SYS_100085);};
    public static final String OPERATIONLOG_UPLOADFILEBAG (){return I18nUtil.message(I18nCode.SYS_100086);};
    public static final String OPERATIONLOG_SYNCFILEBAG (){return I18nUtil.message(I18nCode.SYS_100087);};
    public static final String OPERATIONLOG_DWNCFILEBAG (){return I18nUtil.message(I18nCode.SYS_100088);};
    public static final String OPERATIONLOG_COMPONENTMODELNAME (){return I18nUtil.message(I18nCode.SYS_100089);};
    public static final String OPERATIONLOG_COMPONENTDEVICENAME (){return I18nUtil.message(I18nCode.SYS_100090);};
    public static final String OPERATIONLOG_FILEBAGNAME (){return I18nUtil.message(I18nCode.SYS_100091);};
    public static final String OPERATIONLOG_FILEBAG (){return I18nUtil.message(I18nCode.SYS_100092);};
    public static final String OPERATIONLOG_COMPONENTDEVICELICENSENUMBER (){return I18nUtil.message(I18nCode.SYS_100093);};
    public static final String OPERATIONLOG_COMPONENTDEVICEVINNUMBER (){return I18nUtil.message(I18nCode.SYS_100094);};
    public static final String OPERATIONLOG_COMPONENTDEVICEMATERIALCODE (){return I18nUtil.message(I18nCode.SYS_100095);};
    public static final String OPERATIONLOG_OTA_SUCCESS (){return I18nUtil.message(I18nCode.SYS_100024);};
    public static final String OPERATIONLOG_OTA_FAIL (){return I18nUtil.message(I18nCode.SYS_100025);};
    public static final String OPERATIONLOG_FIELD (){return I18nUtil.message(I18nCode.SYS_100096);};
    public static final String OPERATIONLOG_NEW_EQUIPMENT (){return I18nUtil.message(I18nCode.SYS_100097);};
    public static final String OPERATIONLOG_NEW_EQUIPMENT_MODEL (){return I18nUtil.message(I18nCode.SYS_100098);};
    public static final String OPERATIONLOG_UPDATE (){return I18nUtil.message(I18nCode.SYS_100011);};
    public static final String OPERATIONLOG_NAME (){return I18nUtil.message(I18nCode.SYS_100099);};
    public static final String OPERATIONLOG_DESC (){return I18nUtil.message(I18nCode.SYS_100160);};
    public static final String OPERATIONLOG_DEPT_ADD (){return I18nUtil.message(I18nCode.SYS_100100);};
    public static final String OPERATIONLOG_ADD (){return I18nUtil.message(I18nCode.SYS_100101);};
    public static final String OPERATIONLOG_EQUIPMENT_NAME (){return I18nUtil.message(I18nCode.SYS_100031);};
    public static final String OPERATIONLOG_USER (){return I18nUtil.message(I18nCode.SYS_100014);};
    public static final String OPERATIONLOG_ORGANIZATION_ADD (){return I18nUtil.message(I18nCode.SYS_100102);};

}
