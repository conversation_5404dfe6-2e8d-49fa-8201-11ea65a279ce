package com.rootcloud.ids.ucenter.entity.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rootcloud.ids.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 产品分类-用户-关系
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Getter
@Setter
@TableName("ucenter_base_pro_user_rel")
@ApiModel(value = "BaseProUserRel对象", description = "产品分类-用户-关系")
@EqualsAndHashCode(callSuper = true)
public class BaseProUserRel extends BaseEntity {

    @ApiModelProperty("#主键id#")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("#用户id#")
    private String userId;

    @ApiModelProperty("#机型id#")
    private Long productId;



}
