package com.rootcloud.ids.ucenter.vo.rest.operationlog;

import com.rootcloud.ids.common.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
@Data
@ApiModel(value="OperationLog对象", description="操作日志表")
public class OperationLogPageParam extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "#日志时间#")
    private Date logTime;

    @ApiModelProperty(value = "#操作模块#")
    private String logOperationModule;

    @ApiModelProperty(value = "#操作类型#")
    private String logOperationType;

    @ApiModelProperty(value = "#操作内容#")
    private String logContent;

    @ApiModelProperty(value = "#操作结果#")
    private String operationResult;

    @ApiModelProperty(value = "#操作人#")
    private String operationOperator;

    @ApiModelProperty(value = "#IP地址#")
    private String operationIp;

    @ApiModelProperty(value = "#主键id#")
    private Long id;

    @ApiModelProperty(value = "#查找开始时间#")
    private String startTime;

    @ApiModelProperty(value = "#操作日志查询开始时间#")
    private Date operationStartTime;

    @ApiModelProperty(value = "#查找结束时间#")
    private String endTime;

    @ApiModelProperty(value = "#操作日志查找结束时间#")
    private Date  operationEndTime;

    @ApiModelProperty(value = "#业务id#")
    private String serviceId;

}
