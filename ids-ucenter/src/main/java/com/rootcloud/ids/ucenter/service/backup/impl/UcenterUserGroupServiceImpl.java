package com.rootcloud.ids.ucenter.service.backup.impl;

import com.rootcloud.ids.ucenter.dto.backup.SysGroupAddUserDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysGroupRemoveUserDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterUserGroup;
import com.rootcloud.ids.ucenter.dao.backup.UcenterUserGroupMapper;
import com.rootcloud.ids.ucenter.service.backup.IUcenterUserGroupService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 系统用户-组织服务实现类
 * @ClassName SysUserGroupServiceImpl
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Service
public class UcenterUserGroupServiceImpl extends ServiceImpl<UcenterUserGroupMapper, UcenterUserGroup> implements
    IUcenterUserGroupService {

    @Override
    public Boolean addUser(SysGroupAddUserDTO groupAddUserDTO) {
        List<UcenterUserGroup> userGroupList = new ArrayList<>();
        for (Long userId : groupAddUserDTO.getUserIds()) {
            UcenterUserGroup userGroup = new UcenterUserGroup();
            userGroup.setGroupId(groupAddUserDTO.getGroupId());
            userGroup.setUserId(userId);
            userGroupList.add(userGroup);
        }
        return this.saveBatch(userGroupList);
    }

    @Override
    public Boolean removeUser(SysGroupRemoveUserDTO groupRemoveUserDTO) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("group_id", groupRemoveUserDTO.getGroupId());
        queryWrapper.in("user_id", groupRemoveUserDTO.getUserIds());
        return this.remove(queryWrapper);
    }

}
