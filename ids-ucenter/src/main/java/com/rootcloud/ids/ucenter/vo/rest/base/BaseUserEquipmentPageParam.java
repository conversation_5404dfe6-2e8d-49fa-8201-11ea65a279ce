package com.rootcloud.ids.ucenter.vo.rest.base;

import com.rootcloud.ids.common.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author: ling.kang
 * @create: 2022-03-01 10:44
 **/
@Data
@ApiModel(value = "BaseUserEquipmentPageParam", description = "用户机型列表参数")
public class BaseUserEquipmentPageParam  extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = -7928723979168722004L;

    @ApiModelProperty(value = "#用户id#",required = true)
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @ApiModelProperty("#机械大类id#")
    private Long conMacId;

    @ApiModelProperty("#机型id#")
    private Long equipmentId;


}
