package com.rootcloud.ids.ucenter.vo.rest.base;

import com.rootcloud.esmp.common.enums.RegisterTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @author: ling.kang
 * @create: 2022-02-25 16:21
 **/
@Data
@ApiModel(value = "BaseProEquipmentParam", description = "设备号新增参数")
public class BaseProEquipmentParam implements Serializable {

    private static final long serialVersionUID = 2467507151706689425L;

    @ApiModelProperty("#机型id#")
    private Long id;

    @ApiModelProperty("#设备机型名称#")
    @NotBlank(message = "productName不能为空")
    private String productName;

    @ApiModelProperty(value = "#总线拓扑结构ID#")
    private Long topologyId;

    @ApiModelProperty(value = "#描述信息#")
    private String desc;

    @ApiModelProperty(value = "#总线拓扑结构版本#")
    private Integer topologyVersion;

    @ApiModelProperty(value = "监管类型 0:工程机械:ENGINEERING_MACHINES 1:非工程机械:NON_ENGINEERING_MACHINES 2:其它:OTHERS")
    private RegisterTypeEnum registerType;

}
