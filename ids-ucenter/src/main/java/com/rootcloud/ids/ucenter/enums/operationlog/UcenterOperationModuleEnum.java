package com.rootcloud.ids.ucenter.enums.operationlog;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;


import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * <p> dmp  操作日志模块枚举 </p> #
 *
 * <AUTHOR>
 * @since 2021-11-01
 */

public enum UcenterOperationModuleEnum implements  IEnum<Integer> {
  /**
   * 部件型号
   */
  COMPONENT_MODEL(0, I18nCode.SYS_100016),
  /**
   * 部件清单
   */
  COMPONENT_INST(1, I18nCode.SYS_100017),
  /**
   * ota升级
   */
  COMPONENT_OTA(2, I18nCode.SYS_100159),
  /**
   * 设备清单
   */
  COMPONENT_DEVICE(3, I18nCode.SYS_100018),
  /**
   * 文件管理
   */
  COMPONENT_FILE(4, I18nCode.SYS_100019),
  /**
   * 设备型号
   */
  COMPONENT_EQUIPMENTMODEL(5, I18nCode.SYS_100020),
  /**
   * 机型维护
   */
  MODEL_MAINTENANCE(6, I18nCode.SYS_100021),
  /**
   * 组织用户权限
   */
  ORGANIZATION_USER_PERMISSIONS(7, I18nCode.SYS_100022),
  /**
   * 用户数据权限
   */
  USER_PERMISSION(8, I18nCode.SYS_100023),
  ;


  private int code;
  private I18nCode label;

  private UcenterOperationModuleEnum(int code, I18nCode label) {
    this.code = code;
    this.label = label;
  }

  public Integer getCode() {
    return code;
  }

  public String getLabel() {
    return I18nUtil.message(label);
  }

  public static String getLabel(Integer code) {
    if (code != null) {
      for (UcenterOperationModuleEnum value : UcenterOperationModuleEnum.values()) {
        if (value.code == code) {
          return value.getLabel();
        }
      }
    }
    return null;
  }

  public static UcenterOperationModuleEnum codeOf(int code) {
    for (UcenterOperationModuleEnum value : UcenterOperationModuleEnum.values()) {
      if (value.getCode() == code) {
        return value;
      }
    }
    throw new RuntimeException("cant not change code: " + code + " to DmpOperationModuleEnum.");
  }

  @Override
  public Integer getValue() {
    return code;
  }

  public static String getNameByCode(Integer code){
    for(UcenterOperationModuleEnum value : UcenterOperationModuleEnum.values()){
      if(value.code == code){
        return value.name().substring(value.name().indexOf("_")+1);
      }
    }
    return null;
  }
}
