package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * @Description 应用保存DTO
 * @ClassName SysAppSaveDTO
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "应用保存DTO")
@Getter
@Setter
public class SysAppSaveDTO extends BaseDTO {

    @ApiModelProperty(value = "应用名称", required = true)
    @NotBlank(message = "应用名称不能为空")
    private String appName;

    @ApiModelProperty(value = "应用简介", required = true)
    @NotBlank(message = "应用简介不能为空")
    private String remark;

    @ApiModelProperty(value = "应用logo地址")
    private String appLogo;

    @ApiModelProperty(value = "应用首页地址")
    private String indexUrl;

    @ApiModelProperty(value = "授权登录回调接口地址")
    private String callBackUrl;

}
