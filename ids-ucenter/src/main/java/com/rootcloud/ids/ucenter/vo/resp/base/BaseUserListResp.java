package com.rootcloud.ids.ucenter.vo.resp.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rootcloud.esmp.common.dto.iam.department.DepartmentMiniDTO;
import com.rootcloud.esmp.common.dto.iam.organization.OrganizationMiniDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-02-28 17:03
 **/
@Data
@ApiModel(value = "BaseUserListResp", description = "组织用户")
public class BaseUserListResp {

    @ApiModelProperty("#用户id#")
    private String userId;

    @ApiModelProperty("#用户名称#")
    private String username;

    @ApiModelProperty("#用户的真实姓名#")
    private String displayName;

    @ApiModelProperty("#用户所属主组织ID#")
    private String organizationId;

    @ApiModelProperty("#用户所属的组织#")
    private String organizationName;

    @ApiModelProperty("#用户所属的部门#")
    private List<DepartmentMiniDTO> deptInfoList;

    @ApiModelProperty("#用户所属的组织#")
    private List<OrganizationMiniDTO> organizeInfoList;

    @ApiModelProperty("#用户邮箱#")
    private String email;

    @ApiModelProperty("#用户手机号#")
    private String cellphone;

    @JsonIgnore
    private List<Object> deptList;

    @JsonIgnore
    private List<Object> organizationList;

}
