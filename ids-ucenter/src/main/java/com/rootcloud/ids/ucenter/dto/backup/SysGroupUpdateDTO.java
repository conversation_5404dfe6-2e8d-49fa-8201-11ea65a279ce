package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @Description 组织更新DTO
 * @ClassName SysGroupUpdateDTO
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "组织更新DTO")
@Getter
@Setter
public class SysGroupUpdateDTO extends BaseDTO {

    @ApiModelProperty(value = "组织ID")
    @NotNull(message = "组织ID不能为空")
    private Long groupId;

    @ApiModelProperty(value = "组织名称")
    private String groupName;

    @ApiModelProperty(value = "英文名称")
    private String englishName;

    @ApiModelProperty(value = "备注")
    private String remark;

}
