package com.rootcloud.ids.ucenter.service.openapi;

import com.rootcloud.ids.ucenter.vo.resp.openapi.BaseProductApiResp;
import com.rootcloud.ids.ucenter.vo.resp.openapi.BaseProductDetailApiResp;

import java.util.List;

public interface BaseProductApiService {

    /**
     * 产品分类(机械大类、机型、设备号)详情
     * @param id
     * @return
     */
    BaseProductDetailApiResp getInfo(long id);

    /**
     * 产品分类结构列表
     * @return
     */
    List<BaseProductApiResp> listProduct();

    /**
     * 机型结构列表
     */
    List<BaseProductApiResp> equipmentList(boolean acrossTheTenant);

    /**
     * 设备型号列表
     * @return
     */
    List<BaseProductApiResp> modelList();
}
