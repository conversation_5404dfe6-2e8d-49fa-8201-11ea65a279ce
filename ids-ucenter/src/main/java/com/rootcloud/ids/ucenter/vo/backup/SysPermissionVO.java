package com.rootcloud.ids.ucenter.vo.backup;

import com.rootcloud.ids.common.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 系统权限VO对象
 * @ClassName SysPermissionVO
 * <AUTHOR>
 * @Date 2021/12/22
 * @Version 1.0
 */
@ApiModel(value = "系统权限VO对象")
@Getter
@Setter
public class SysPermissionVO extends BaseVO {

    @ApiModelProperty(value = "权限ID")
    private Long permissionId;

    @ApiModelProperty(value = "所属模块")
    private String module;

    @ApiModelProperty(value = "request请求方式")
    private String method;

    @ApiModelProperty(value = "权限名称")
    private String permissionName;

    @ApiModelProperty(value = "权限值")
    private String permissionValue;

    @ApiModelProperty(value = "描述信息")
    private String remark;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        SysPermissionVO that = (SysPermissionVO) o;
        return Objects.equals(permissionId, that.permissionId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), permissionId, module, method, permissionName,
            permissionValue, remark);
    }
}
