package com.rootcloud.ids.ucenter.vo.backup;

import com.rootcloud.ids.common.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 角色VO对象
 * @ClassName SysRoleVO
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "角色VO对象")
@Getter
@Setter
public class SysRoleVO extends BaseVO {

    @ApiModelProperty(value = "角色ID")
    private Long roleId;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "英文名称")
    private String englishName;

    @ApiModelProperty(value = "备注")
    private String remark;

}
