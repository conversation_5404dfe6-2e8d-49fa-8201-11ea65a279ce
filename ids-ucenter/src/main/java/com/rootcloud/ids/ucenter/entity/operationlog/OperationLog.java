package com.rootcloud.ids.ucenter.entity.operationlog;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rootcloud.ids.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p> 操作日志表 </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
@Data
@TableName("ucenter_operation_log")
@ApiModel(value = "OperationLog对象", description = "操作日志表")
public class OperationLog extends BaseEntity {

  private static final long serialVersionUID = 1L;

  @ApiModelProperty("#主键id#")
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  @ApiModelProperty(value = "#日志时间#")
  private Date logTime;

  @ApiModelProperty(value = "#操作模块#")
  private String logOperationModule;

  @ApiModelProperty(value = "#操作类型#")
  private String logOperationType;

  @ApiModelProperty(value = "#操作内容#")
  private String logContent;

  @ApiModelProperty(value = "#操作结果#")
  private String operationResult;

  @ApiModelProperty(value = "#操作人#")
  private String operationOperator;

  @ApiModelProperty(value = "#IP地址#")
  private String operationIp;

  @ApiModelProperty(value = "#业务id#")
  private String serviceId;

  @ApiModelProperty(value = "#日志详情#")
  private String logDetail;

  @ApiModelProperty(value = "#已同步:synced,false:未同步:unsynced#")
  private Boolean synced;

  @ApiModelProperty(value = "#用户id#")
  private String userId;

  @ApiModelProperty(value = "#应用客户端id#")
  private String clientId;

  @ApiModelProperty(value = "#租户id#")
  private String tenantId;

  @ApiModelProperty(value = "#组织id#")
  private String organizationId;

}
