package com.rootcloud.ids.ucenter.entity.backup;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rootcloud.ids.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户权限关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
@Getter
@Setter
@TableName("ucenter_user_permission")
@ApiModel(value = "UserPermission对象", description = "用户权限关系表")
public class UserPermission extends BaseEntity {

    @ApiModelProperty("用户ID")
    @TableId("user_id")
    private Long userId;

    @ApiModelProperty("权限ID")
    @TableField("permission_id")
    private Long permissionId;

    @ApiModelProperty("创建用户ID")
    @TableField("create_user")
    private Long createUser;

    @ApiModelProperty("更新用户ID")
    @TableField("update_user")
    private Long updateUser;


}
