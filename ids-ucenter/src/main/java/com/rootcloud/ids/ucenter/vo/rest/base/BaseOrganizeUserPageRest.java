package com.rootcloud.ids.ucenter.vo.rest.base;

import com.rootcloud.ids.common.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-02-28 16:49
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "BaseOrganizeUserPageRest", description = "组织用户查询")
public class BaseOrganizeUserPageRest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 2967694873994066529L;
    @ApiModelProperty(value = "#组织ID#",required = true)
    @NotBlank(message = "组织ID不能为空")
    private String id;

    @ApiModelProperty("#组织名称#")
    private String organizationName;

    @ApiModelProperty("#用户名称-全匹配#")
    private String displayName;

}
