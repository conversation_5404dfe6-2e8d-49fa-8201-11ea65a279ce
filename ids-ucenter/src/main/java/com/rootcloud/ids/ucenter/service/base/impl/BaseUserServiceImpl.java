package com.rootcloud.ids.ucenter.service.base.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.rootcloud.esmp.common.dto.cache.BaseProductResp;
import com.rootcloud.esmp.common.dto.cache.UserDTO;
import com.rootcloud.esmp.common.dto.iam.organization.OrganizationInfoDTO;
import com.rootcloud.esmp.common.dto.iam.organizationUser.OrganizeUserInfoDTO;
import com.rootcloud.esmp.common.dto.permission.ExpressionDto;
import com.rootcloud.esmp.common.enums.PermissionQueryEnum;
import com.rootcloud.esmp.common.enums.ProductTypeEnum;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import com.rootcloud.esmp.common.utils.OptionalX;
import com.rootcloud.esmp.common.utils.SecurityUtils;
import com.rootcloud.esmp.iam.client.IamOrganizationClient;
import com.rootcloud.esmp.iam.client.IamOrganizationUserClient;
import com.rootcloud.esmp.common.enums.IamPermissionActionEnum;
import com.rootcloud.esmp.iam.service.auth.IDataPermissionService;
import com.rootcloud.ids.common.core.result.ResultCode;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.ids.common.mybatis.utils.PageUtils;
import com.rootcloud.ids.common.mybatis.utils.SqlUtils;
import com.rootcloud.ids.common.web.exception.BizException;
import com.rootcloud.ids.ucenter.dto.base.OrganizationDTO;
import com.rootcloud.ids.ucenter.entity.base.BaseDeptProRel;
import com.rootcloud.ids.ucenter.entity.base.BaseProUserRel;
import com.rootcloud.ids.ucenter.entity.base.BaseProduct;
import com.rootcloud.ids.ucenter.entity.base.BaseUserDeptRel;
import com.rootcloud.ids.ucenter.enums.base.AuthTypeEnum;
import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import com.rootcloud.ids.ucenter.enums.operationlog.UcenterOperationModuleEnum;
import com.rootcloud.ids.ucenter.enums.operationlog.UcenterOperationTypeEnum;
import com.rootcloud.ids.ucenter.service.base.IBaseDeptProRelService;
import com.rootcloud.ids.ucenter.service.base.IBaseOrganizationService;
import com.rootcloud.ids.ucenter.service.base.IBaseProUserRelService;
import com.rootcloud.ids.ucenter.service.base.IBaseProductService;
import com.rootcloud.ids.ucenter.service.base.IBaseUserDeptRelService;
import com.rootcloud.ids.ucenter.service.base.IBaseUserService;
import com.rootcloud.ids.ucenter.service.dmp.IDmpService;
import com.rootcloud.ids.ucenter.service.operationlog.IOperationLogService;
import com.rootcloud.ids.ucenter.utils.Constants;
import com.rootcloud.ids.ucenter.utils.Util;
import com.rootcloud.ids.ucenter.utils.convertor.ConvertUtils;
import com.rootcloud.ids.ucenter.utils.convertor.base.BaseProductRespConvertor;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDeptEquipmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseOrganizationResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseUserEquipmentAddRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseUserEquipmentRemoveRest;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseUserPossessPageRest;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 组织机构表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Slf4j
@Service
public class BaseUserServiceImpl implements IBaseUserService {

    @Autowired
    private IBaseDeptProRelService iBaseDeptProRelService;
    @Autowired
    private IBaseProUserRelService iBaseProUserRelService;
    @Autowired
    private IBaseProductService iBaseProductService;
    @Autowired
    private IBaseUserDeptRelService iBaseUserDeptRelService;
    @Autowired
    private IBaseOrganizationService iBaseOrganizationService;
    @Autowired
    private IDmpService iDmpService;
    @Autowired
    private IOperationLogService operationLogService;
    @Autowired
    private IDataPermissionService dataPermissionService;

    @Value("${equipmentModel.unPublishedExclude}")
    private Boolean unPublishedExclude;


    @Override
    public Page<BaseDeptEquipmentResp> pagePossess(BaseUserPossessPageRest param) {
        String resolve = "{\"fields\":[\"organizations\"]}";
        //获取个人信息
        OrganizeUserInfoDTO userInfo = IamOrganizationUserClient.organizationUserInfo(param.getCid(), param.getUserId(), resolve);
        //获取用户所有租户信息
        JSONArray jsonArray = JSONUtil.parseArray(userInfo.getOrganizations());
        List<OrganizationDTO> orgInfoList = JSONUtil.toList(jsonArray, OrganizationDTO.class);
        List<String> orgList = orgInfoList.stream().map(OrganizationDTO::getId).collect(Collectors.toList());
        //获取组织信息
        OrganizationInfoDTO organizeInfo = IamOrganizationClient.organizationInfo(param.getCid(), null);
        //转化组织部门信息
        boolean admin = organizeInfo.getAdmins() != null && organizeInfo.getAdmins().contains(param.getUserId());
        List<String> deptList = ConvertUtils.parseObj2Class(userInfo.getDepartments(), String.class);
        //查询用户机型权限
        List<Long> productIdList = this.getUserProduct(param.getUserId(), orgList, deptList, admin, null);
        if (CollUtil.isEmpty(productIdList) && param.getPossess()) {
            return param.batisPage();
        }
        LambdaQueryWrapper<BaseProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseProduct::getProductType, ProductTypeEnum.EQUIPMENT);
        if (param.getPossess()) {
            wrapper.in(BaseProduct::getId, productIdList);
        } else {
            //未拥有权限还得是有分配该组织下所有权的机型
            List<BaseDeptProRel> deptProRelList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(orgList, DeptTypeEnum.ORGANIZATION);
            wrapper.in(CollUtil.isNotEmpty(deptProRelList), BaseProduct::getId, deptProRelList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet()));
            wrapper.notIn(CollUtil.isNotEmpty(productIdList), BaseProduct::getId, productIdList);
            if (CollUtil.isEmpty(deptProRelList) && CollUtil.isEmpty(productIdList)) {
                return param.batisPage();
            }
        }
        wrapper.eq(param.getConMacId() != null, BaseProduct::getParentId, param.getConMacId());
        wrapper.like(StrUtil.isNotEmpty(param.getSearchKey()), BaseProduct::getProductName, SqlUtils.likeEscape(param.getSearchKey()));
        Page<BaseProduct> page = this.iBaseProductService.page(param.batisPage(), wrapper);
        if (CollUtil.isEmpty(page.getRecords())) {
            return param.batisPage();
        }
        //所有机械大类数据
        Map<Long, String> machineryMap = this.iBaseProductService.allConstructionMachinery().stream().collect(Collectors.toMap(BaseProduct::getId, BaseProduct::getProductName, (v1, v2) -> v1));

        Map<Long, String> relMap = this.iBaseDeptProRelService.listOrganisation(page.getRecords().stream().map(BaseProduct::getId).collect(Collectors.toSet()))
                .stream().filter(obj -> obj.getAuthType() == AuthTypeEnum.OWNER).collect(Collectors.toMap(BaseDeptProRel::getProductId, BaseDeptProRel::getDeptId, (v1, v2) -> v1));
        //组织信息
        Map<String, String> organizeMap = this.iBaseOrganizationService.list()
                .stream().collect(Collectors.toMap(BaseOrganizationResp::getId, BaseOrganizationResp::getName, (v1, v2) -> v1));
        return PageUtils.convert(page, obj -> {
            BaseDeptEquipmentResp resp = BaseProductRespConvertor.change2BaseDeptEquipmentResp(obj);
            resp.setDeptId(relMap.get(resp.getEquipmentId()));
            resp.setDeptName(organizeMap.get(resp.getDeptId()));
            resp.setConstructionMachineryName(machineryMap.get(resp.getConMacId()));
            return resp;
        });
    }

    private List<Long> getUserProduct(String userId, List<String> tenantIdList, List<String> deptIdList, Boolean isSysAdmin, String tenantId) {
        log.info("===== BaseUserServiceImpl =====getUserProduct userId:{} ,tenantIdList:{},deptIdList:{},isSysAdmin:{}", userId, tenantIdList, deptIdList, isSysAdmin);
        List<Long> productIdList;
        if (null != isSysAdmin && isSysAdmin) {
            List<BaseDeptProRel> relList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(tenantIdList, DeptTypeEnum.ORGANIZATION);
            if (CollUtil.isEmpty(relList)) {
                return new ArrayList<>();
            }
            productIdList = relList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toList());
        } else {
            productIdList = this.iBaseProUserRelService.listByUserId(userId).stream().map(BaseProUserRel::getProductId).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(tenantId) && CollUtil.isNotEmpty(productIdList)) {
                List<BaseDeptProRel> baseDeptProRels = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(tenantId), DeptTypeEnum.ORGANIZATION);
                if (CollUtil.isEmpty(baseDeptProRels)) {
                    return new ArrayList<>();
                }
                List<Long> baseDeptProRel = baseDeptProRels.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toList());
                productIdList = productIdList.stream().filter(baseDeptProRel::contains).collect(Collectors.toList());
            }
        }
        log.info("===== BaseUserServiceImpl =====getUserProduct productIdList:{}", productIdList.size());
        return productIdList;
    }

    @Override
    public List<BaseProductResp> listTenantPossess() {
        UserDTO userDTO = SecurityUtils.getCurrentUser();
        if (null == userDTO) {
            throw new BizException(ResultCode.AUTHORIZED_ERROR);
        }
        List<Long> productIdList = this.getUserProduct(userDTO.getUserId(), Collections.singletonList(userDTO.getTenantId()), userDTO.getDeptIdList(), userDTO.getAdmin(), userDTO.getTenantId());
        if (CollUtil.isEmpty(productIdList)) {
            return new ArrayList<>();
        }

        return this.getUserPossess(productIdList,true);
    }

    @Override
    public List<BaseProductResp> listPossess(@NotNull PermissionQueryEnum query) {
        List<BaseProduct> allBaseProducts = iBaseProductService.getByPermission(query);

        List<BaseProductResp> list = BaseProductRespConvertor.change2ProductRespList(allBaseProducts.listIterator());
        //按创建时间倒叙
        list.sort(createTimeDescComparator);
        return list;
    }

    private static final Comparator<BaseProductResp> createTimeDescComparator = (r1, r2) -> {
        LocalDateTime t1 = r1 == null ? null : r1.getCreateTime();
        LocalDateTime t2 = r2 == null ? null : r2.getCreateTime();
        return Util.LOCAL_DATETIME_DESC_COMPARATOR.compare(t1, t2);
    };

    private List<BaseProductResp> getUserPossess(Collection<Long> productIdList, Boolean isCrossTenant){
        UserDTO userDTO = SecurityUtils.getCurrentUser();
        if (null == userDTO) {
            throw new BizException(ResultCode.AUTHORIZED_ERROR);
        }

        List<BaseProduct> list = this.iBaseProductService.listByIds(productIdList);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        //不跨租户，机型筛选
        if (Boolean.FALSE.equals(isCrossTenant)) {
            List<BaseProductResp> productRespList = this.listTenantPossess();
            if (CollUtil.isEmpty(productRespList)) {
                return new ArrayList<>();
            }
            Set<Long> productIds = productRespList.stream()
                .filter(x ->StrUtil.equals(userDTO.getTenantId(), x.getTenantId()))
                .map(BaseProductResp::getId).map(Long::parseLong).collect(Collectors.toSet());
            list = list.stream().filter(x -> productIds.contains(x.getId())).collect(Collectors.toList());
        }
        Set<Long> machineryIds = list.stream().map(BaseProduct::getParentId).collect(Collectors.toSet());

        //机型所有权的租户ID信息
        Map<Long, String> proDeptMap = this.iBaseDeptProRelService.listByProductIdsAndDeptTypeAndAuthType(productIdList, DeptTypeEnum.ORGANIZATION, AuthTypeEnum.OWNER)
                .stream().collect(Collectors.toMap(BaseDeptProRel::getProductId, BaseDeptProRel::getDeptId, (o1, o2) -> o1));
        //所有机械大类数据
        Map<Long, BaseProduct> machineryMap = this.iBaseProductService.allConstructionMachinery().stream().collect(Collectors.toMap(BaseProduct::getId, obj -> obj, (o1, o2) -> o1));

        List<BaseProductResp> respList = BaseProductRespConvertor.change2ProductRespList(list.listIterator());
        //放入租户ID到机型数据
        respList.forEach(obj -> obj.setTenantId(proDeptMap.get(obj.getId())));
        //有权限的机械大类才返回，没有的不返回
        machineryIds.forEach(id -> {
            BaseProductResp resp = BaseProductRespConvertor.change2ProductResp(machineryMap.get(id));
            Optional.ofNullable(resp).ifPresent(respList::add);
        });
        //设备型号
        List<BaseProduct> modelList = this.iBaseProductService.listByParentIds(list.stream().map(BaseProduct::getId).collect(Collectors.toSet()));
        if (CollUtil.isNotEmpty(modelList)) {
            List<BaseProductResp> modelRespList = BaseProductRespConvertor.change2ProductRespList(modelList.listIterator());
            modelRespList.forEach(obj -> obj.setTenantId(proDeptMap.get(obj.getParentId())));
            respList.addAll(modelRespList);
        }
        // 过滤掉未发布的设备型号
        if (Objects.nonNull(unPublishedExclude) && unPublishedExclude) {
            respList.removeIf(baseProductResp -> baseProductResp.getProductType() == ProductTypeEnum.EQUIPMENT_MODEL && (baseProductResp.getHasPublished() != null && BooleanUtil.isTrue(baseProductResp.getHasPublished())));
        }
        log.info("===== BaseUserServiceImpl =====listPossess respList:{}", respList.size());
        return respList;
    }

    private Set<Long> getUserAllProduct(String userId, String tenantId, List<String> deptIdList, Boolean isSysAdmin) {
        log.info("===== BaseUserServiceImpl =====getUserAllProduct userId:{},deptIdList:{},isSysAdmin:{}", userId, deptIdList, isSysAdmin);
        Set<Long> productIdList = new HashSet<>();
        List<BaseDeptProRel> relList;
        if (BooleanUtil.isTrue(isSysAdmin)) {
            relList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(Collections.singletonList(tenantId), DeptTypeEnum.ORGANIZATION);
        } else {
            relList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(deptIdList, DeptTypeEnum.DEPARTMENT);
        }
        if (CollUtil.isNotEmpty(relList)) {
            productIdList = relList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet());
        }
        //加上特殊个人授权的权限
        productIdList.addAll(this.iBaseProUserRelService.listByUserId(userId).stream().map(BaseProUserRel::getProductId).collect(Collectors.toSet()));
        return productIdList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addAuthority(BaseUserEquipmentAddRest param) {
        if (CollUtil.isEmpty(param.getIds())) {
            return true;
        }
        //查询用户是否跨租户
        List<BaseUserDeptRel> userDeptList = this.iBaseUserDeptRelService.listUserId(param.getUserId());
        List<Long> proIdList = new ArrayList<>();
        if (CollUtil.isNotEmpty(userDeptList)) {
            this.iBaseUserDeptRelService.removeByIds(userDeptList.stream().map(BaseUserDeptRel::getId).collect(Collectors.toList()));
            //查询用户拥有的机型权限
            proIdList = this.iBaseProUserRelService.listByUserId(param.getUserId()).stream().map(BaseProUserRel::getProductId).collect(Collectors.toList());
        }
        //更新部门数据，可能有新增或者减少的部门
        List<BaseUserDeptRel> newUserDeptList = new ArrayList<>();
        List<String> deptOrOrganizeIds = CollUtil.isEmpty(param.getDeptIdList()) ? Collections.singletonList(param.getOrganizationId()) : param.getDeptIdList();
        deptOrOrganizeIds.forEach(obj -> {
            BaseUserDeptRel entity = new BaseUserDeptRel();
            entity.setUserId(param.getUserId());
            entity.setDeptId(obj);
            newUserDeptList.add(entity);
        });
        this.iBaseUserDeptRelService.saveBatch(newUserDeptList);

        List<BaseProUserRel> proUserList = new ArrayList<>();
        List<Long> finalProIdList = proIdList;
        param.getIds().forEach(id -> {
            //增量不重复
            if (CollUtil.isEmpty(finalProIdList) || !finalProIdList.contains(id)) {
                BaseProUserRel entity = new BaseProUserRel();
                entity.setUserId(param.getUserId());
                entity.setProductId(id);
                proUserList.add(entity);
            }
        });
        boolean flag = this.iBaseProUserRelService.saveBatch(proUserList);
        if (flag) {
            //查询机型
            List<BaseProduct> baseProducts = iBaseProductService.list(Wrappers.lambdaQuery(BaseProduct.class)
                    .in(BaseProduct::getId, param.getIds())
            );
            //保存日志对象
            String logContent = Constants.OPERATIONLOG_ADD() + Constants.OPERATIONLOG_EQUIPMENT_NAME()
                    + String.join("、", baseProducts.stream().map(BaseProduct::getProductName).collect(Collectors.toList()));
            String logDetail = String.join("、", baseProducts.stream().map(BaseProduct::getProductName).collect(Collectors.toList()));
            operationLogService.saveOperationLog(param.getUserId(),
                    UcenterOperationModuleEnum.USER_PERMISSION, UcenterOperationTypeEnum.TYPE_CREATE,
                    logContent, logDetail, null, Constants.OPERATIONLOG_SUCCESS);
        }
        iDmpService.refreshCache();
        return flag;
    }

    @Override
    public Boolean removeAuthority(BaseUserEquipmentRemoveRest param) {
        //查询用户 租户管理员不移除权限
        OrganizeUserInfoDTO usersDTO = IamOrganizationUserClient.organizationUserInfo(param.getCid(), param.getUserId(), null);
        OrganizationInfoDTO organizeInfo = IamOrganizationClient.organizationInfo(param.getCid(), null);
        boolean admin = organizeInfo.getAdmins() != null && organizeInfo.getAdmins().contains(usersDTO.getId());
        if (admin) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100119));
        }
        //查询机型
        BaseProduct product = iBaseProductService.getOne(Wrappers.lambdaQuery(BaseProduct.class)
                .eq(BaseProduct::getId, param.getEquipmentId())
                .eq(BaseProduct::getProductType, ProductTypeEnum.EQUIPMENT));
        if (null == product) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100117));
        }
        LambdaQueryWrapper<BaseProUserRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseProUserRel::getUserId, param.getUserId());
        wrapper.eq(BaseProUserRel::getProductId, param.getEquipmentId());
        //保存日志对象
        String logContent = UcenterOperationTypeEnum.TYPE_REMOVE.getLabel() + Constants.OPERATIONLOG_EQUIPMENT_NAME() + product.getProductName();
        String logDetail = product.getProductName();
        operationLogService.saveOperationLog(param.getUserId(),
                UcenterOperationModuleEnum.USER_PERMISSION, UcenterOperationTypeEnum.TYPE_REMOVE,
                logContent, logDetail, null, Constants.OPERATIONLOG_SUCCESS);
        iDmpService.refreshCache();
        return iBaseProUserRelService.remove(wrapper);
    }

    @Override
    public Collection<Long> pagePossessIds(BaseUserPossessPageRest param) {
        String resolve = "{\"fields\":[\"organizations\"]}";
        //获取个人信息
        OrganizeUserInfoDTO userInfo = IamOrganizationUserClient.organizationUserInfo(param.getCid(), param.getUserId(), resolve);
        //获取用户所有租户信息
        JSONArray jsonArray = JSONUtil.parseArray(userInfo.getOrganizations());
        List<OrganizationDTO> orgInfoList = JSONUtil.toList(jsonArray, OrganizationDTO.class);
        List<String> orgList = orgInfoList.stream().map(OrganizationDTO::getId).collect(Collectors.toList());
        //获取组织信息
        OrganizationInfoDTO organizeInfo = IamOrganizationClient.organizationInfo(param.getCid(), null);
        //转化组织部门信息
        boolean admin = organizeInfo.getAdmins() != null && organizeInfo.getAdmins().contains(param.getUserId());
        List<String> deptList = ConvertUtils.parseObj2Class(userInfo.getDepartments(), String.class);
        //查询用户机型权限
        List<Long> productIdList = this.getUserProduct(param.getUserId(), orgList, deptList, admin, null);
        if (CollUtil.isEmpty(productIdList) && param.getPossess()) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<BaseProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseProduct::getProductType, ProductTypeEnum.EQUIPMENT);
        if (param.getPossess()) {
            wrapper.in(BaseProduct::getId, productIdList);
        } else {
            //未拥有权限还得是有分配该组织下所有权的机型
            List<BaseDeptProRel> deptProRelList = this.iBaseDeptProRelService.listByDeptIdsAndDeptType(orgList, DeptTypeEnum.ORGANIZATION);
            wrapper.in(CollUtil.isNotEmpty(deptProRelList), BaseProduct::getId, deptProRelList.stream().map(BaseDeptProRel::getProductId).collect(Collectors.toSet()));
            wrapper.notIn(CollUtil.isNotEmpty(productIdList), BaseProduct::getId, productIdList);
            if (CollUtil.isEmpty(deptProRelList) && CollUtil.isEmpty(productIdList)) {
                return new ArrayList<>();
            }
        }
        wrapper.like(StrUtil.isNotEmpty(param.getSearchKey()), BaseProduct::getProductName, SqlUtils.likeEscape(param.getSearchKey()));
        wrapper.select(BaseProduct::getParentId);
        return this.iBaseProductService.list(wrapper).stream().map(BaseProduct::getParentId).collect(Collectors.toSet());
    }
}
