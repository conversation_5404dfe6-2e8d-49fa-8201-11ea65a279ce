package com.rootcloud.ids.ucenter.vo.rest.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: ling.kang
 * @create: 2022-02-25 15:31
 **/
@Data
@ApiModel(value = "BaseProductDelParam", description = "产品分类(机械大类、机型、设备号)删除参数")
public class BaseProductDelParam implements Serializable {

    private static final long serialVersionUID = -6770393425590569477L;

    @ApiModelProperty(value = "分类ID", required = true)
    private Long id;
}
