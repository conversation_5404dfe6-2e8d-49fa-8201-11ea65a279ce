package com.rootcloud.ids.ucenter.entity.backup;

import com.rootcloud.ids.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 菜单实体对象
 * @ClassName SysMenu
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Getter
@Setter
@TableName("ucenter_menu")
public class UcenterMenu extends BaseEntity {

    /**
     * 菜单ID
     */
    @TableId(value = "menu_id", type = IdType.ASSIGN_ID)
    private Long menuId;
    /**
     * 应用ID
     */
    private Long appId;
    /**
     * 上级菜单id
     */
    private Long parentId;
    /**
     * 菜单名称
     */
    private String menuName;
    /**
     * 菜单英文名称
     */
    private String englishName;
    /**
     * 菜单显示名称
     */
    private String showName;
    /**
     * 菜单hash地址(路由地址)
     */
    private String menuUrl;
    /**
     * 菜单图标
     */
    private String menuIcon;
    /**
     * 排序号
     */
    private Integer sortNo;

}
