package com.rootcloud.ids.ucenter.entity.backup;

import com.rootcloud.ids.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 角色实体对象
 * @ClassName SysRole
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Getter
@Setter
@TableName("ucenter_role")
public class UcenterRole extends BaseEntity {

    /**
     * 角色ID
     */
    @TableId(value = "role_id", type = IdType.ASSIGN_ID)
    private Long roleId;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 英文名称
     */
    private String englishName;
    /**
     * 备注
     */
    private String remark;

}
