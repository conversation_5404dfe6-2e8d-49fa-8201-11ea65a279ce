package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 应用查询条件DTO
 * @ClassName SysAppCondition
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "应用查询条件DTO")
@Getter
@Setter
public class SysAppCondition extends BasePageQuery {

    @ApiModelProperty(value = "应用名称")
    private String appName;

}
