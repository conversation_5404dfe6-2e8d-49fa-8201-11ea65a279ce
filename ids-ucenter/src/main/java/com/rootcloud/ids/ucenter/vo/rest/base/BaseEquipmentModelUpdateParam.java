package com.rootcloud.ids.ucenter.vo.rest.base;

import com.rootcloud.esmp.common.enums.RegisterTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: ling.kang
 * @create: 2022-02-25 15:05
 **/
@Data
@ApiModel(value = "BaseEquipmentModelUpdateParam", description = "设备型号属性更改")
public class BaseEquipmentModelUpdateParam implements Serializable {

    private static final long serialVersionUID = -7340278387128511958L;

    @ApiModelProperty("#分类id#")
    private Long id;

    @ApiModelProperty("#topologyId#")
    private Long topologyId;

    @ApiModelProperty(value = "#总线拓扑结构版本#")
    private Integer topologyVersion;

    @ApiModelProperty(value = "监管类型 0:工程机械:ENGINEERING_MACHINES 1:非工程机械:NON_ENGINEERING_MACHINES 2:其它:OTHERS")
    private RegisterTypeEnum registerType;

}
