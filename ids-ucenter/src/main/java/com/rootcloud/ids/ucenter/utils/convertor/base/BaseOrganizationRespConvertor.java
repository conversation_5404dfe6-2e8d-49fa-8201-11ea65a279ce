package com.rootcloud.ids.ucenter.utils.convertor.base;

import com.rootcloud.esmp.common.dto.iam.organization.OrganizationInfoDTO;
import com.rootcloud.esmp.common.dto.iam.organizationUser.OrganizeUserInfoDTO;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseOrganizationResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseUserListResp;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;

/**
 * <AUTHOR>
 * @description
 * @since 2022/3/1 3:25 下午
 */
public class BaseOrganizationRespConvertor {

    public static List<BaseOrganizationResp> change2OrganizationRespList(Iterator<OrganizationInfoDTO> entityList) {
        List<BaseOrganizationResp> list = new ArrayList<>();
        if(entityList != null && entityList.hasNext()) {
            entityList.forEachRemaining(entity -> list.add(change2OrganizationResp(entity)));
        }
        return list;
    }

    private static BaseOrganizationResp change2OrganizationResp(OrganizationInfoDTO dto) {
        BaseOrganizationResp resp = null;
        if(dto != null) {
            resp = new BaseOrganizationResp();
            resp.setId(dto.getId());
            resp.setName(dto.getName());
        }
        return resp;
    }

    public static List<BaseUserListResp> change2UserListRespList(ListIterator<OrganizeUserInfoDTO> entityList) {
        List<BaseUserListResp> list = new ArrayList<>();
        if(entityList != null && entityList.hasNext()) {
            entityList.forEachRemaining(entity -> list.add(change2UserListResp(entity)));
        }
        return list;
    }

    public static BaseUserListResp change2UserListResp(OrganizeUserInfoDTO dto) {
        BaseUserListResp resp = null;

        if(dto != null) {
            resp = new BaseUserListResp();
            resp.setUserId(dto.getId());
            resp.setUsername(dto.getUsername());
            resp.setDisplayName(dto.getDisplayName());
            resp.setDeptList(dto.getDepartments());
            resp.setOrganizationList(dto.getOrganizations());
            resp.setEmail(dto.getEmail());
            resp.setCellphone(dto.getCellphone());
            resp.setOrganizationId(dto.getMainOrganization());
        }
        return resp;
    }
}
