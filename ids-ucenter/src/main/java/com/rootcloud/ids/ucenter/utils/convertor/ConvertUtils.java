package com.rootcloud.ids.ucenter.utils.convertor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.rootcloud.ids.ucenter.entity.base.BaseProUserRel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 通用转换类
 * @since 2022/3/14 11:30 上午
 */
public class ConvertUtils {

    public static <T> List<T> parseObj2Class(List<Object> list, Class<T> beanClass) {
        return Optional.ofNullable(list).map(obj -> JSONUtil.toList(JSONUtil.parseArray(obj), beanClass)).orElse(new ArrayList<>(0));
    }

    public static List<BaseProUserRel> getProUseRelList(List<String> ids, Long equipmentId, Map<String, List<BaseProUserRel>> userList) {
        List<BaseProUserRel> proUserRelList = new ArrayList<>();
        ids.forEach(id -> {
            //判断用户是否拥有机型权限
            if (CollUtil.isNotEmpty(userList.get(id))) {
                if (!userList.get(id).stream().map(BaseProUserRel::getProductId).collect(Collectors.toSet()).contains(equipmentId)) {
                    BaseProUserRel baseProUserRel = new BaseProUserRel();
                    baseProUserRel.setUserId(id);
                    baseProUserRel.setProductId(equipmentId);
                    proUserRelList.add(baseProUserRel);
                }
            } else {
                BaseProUserRel baseProUserRel = new BaseProUserRel();
                baseProUserRel.setUserId(id);
                baseProUserRel.setProductId(equipmentId);
                proUserRelList.add(baseProUserRel);
            }
        });
        return proUserRelList;
    }
}
