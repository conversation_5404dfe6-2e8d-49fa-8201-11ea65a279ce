package com.rootcloud.ids.ucenter.service.base;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.rootcloud.esmp.common.enums.PermissionQueryEnum;
import com.rootcloud.esmp.common.enums.ProductTypeEnum;
import com.rootcloud.ids.ucenter.entity.base.BaseProduct;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseDepartmentResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseEquipmentModelResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseEquipmentPageResp;
import com.rootcloud.ids.ucenter.vo.resp.base.BaseProductDetailResp;
import com.rootcloud.esmp.common.dto.cache.BaseProductResp;
import com.rootcloud.ids.ucenter.vo.resp.openapi.BaseProductApiResp;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseEquipmentModelPageParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseEquipmentModelReleaseParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseEquipmentModelUpdateParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseEquipmentPageParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseEquipmentUpParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseProEquipmentParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseProductParam;
import com.rootcloud.ids.ucenter.vo.rest.base.BaseProductUpParam;
import com.rootcloud.ids.ucenter.vo.rest.base.ProductListParam;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import javax.validation.constraints.NotNull;

/**
 * 产品分类表 服务类
 *
 * <AUTHOR>
 * @since 2022-02-24
 */
public interface IBaseProductService extends IService<BaseProduct> {

    /**
     * 机械大类属性结构列表
     *
     * @return
     */
    List<BaseProductResp> listProduct();

    /**
     * 产品分类权限结构列表
     *
     * @return
     */
    List<BaseProductResp> listAuthProduct();

    /**
     * 产品分类详情查询
     *
     * @param id
     * @return
     */
    BaseProductDetailResp getInfo(long id);

    /**
     * 新增机型
     *
     * @param param
     * @return
     */
    Long add(BaseProductParam param);

    /**
     * 产品分类名称(机械大类、机型、设备号)更改
     *
     * @param param
     * @return
     */
    boolean updateById(BaseProductUpParam param);

    /**
     * 产品分类(机械大类、机型、设备号)删除
     *
     * @param id
     * @return
     */
    boolean del(Long id);

    /**
     * 修改设备号
     *
     * @param param
     * @return
     */
    boolean updateEquipment(BaseEquipmentUpParam param);

    /**
     * 新增设备号
     *
     * @param param
     * @return
     */
    long addEquipment(BaseProEquipmentParam param);

    /**
     * 机型下设备号列表分页查询
     *
     * @param param
     * @return
     */
    Page<BaseEquipmentModelResp> pageModelByParam(BaseEquipmentModelPageParam param);

    /**
     * 机型下设备号列表分页查询-单租户
     *
     * @param param 参数
     * @return Page<BaseEquipmentModelResp>
     */
    Page<BaseEquipmentModelResp> pageModelByParam2(BaseEquipmentModelPageParam param);

    /**
     * 机型列表分页查询
     *
     * @param param
     * @return
     */
    Page<BaseEquipmentPageResp> pageEquipmentByParam(BaseEquipmentPageParam param);

    /**
     * 所有机械大类
     *
     * @return List<BaseProduct>
     */
    List<BaseProduct> allConstructionMachinery();

    /**
     * 所有机械大类(包含公用)
     *
     * @return
     */
    List<BaseProduct> listMachineryPub();

    /**
     * 设备型号列表
     *
     * @param equipmentId 机械ID
     * @return List<BaseProductResp>
     */
    List<BaseProductResp> listModel(Long equipmentId);

    /**
     * 查询机型的名称列表
     *
     * @param ids id数组
     * @return List<BaseProductApiResp>
     */
    List<BaseProductApiResp> listProductName(List<Long> ids);

    /**
     * 查询数据
     *
     * @param parentIds 父级ids
     * @return List<BaseProduct>
     */
    List<BaseProduct> listByParentIds(Set<Long> parentIds);

    /**
     * 查询用户拥有权限范围机型
     *
     * @return
     */
    List<BaseProduct> getUserAuthEquipment();

    /**
     * 查询跨租户用户拥有权限范围机型
     *
     * @return List<BaseProduct>
     */
    List<BaseProduct> getUserAuthEquipment2();

    /**
     * 查询用户拥有权限范围机型
     *
     * @param userId     用户ID
     * @param deptIdList 用户部门ID数组
     * @param orgId      用户组织ID
     * @param isSysAdmin 是否组织管理员
     * @return
     */
    List<BaseProduct> getUserAuthEquipmentByParam(String userId, List<String> deptIdList, String orgId, Boolean isSysAdmin);

    /**
     * 查询机型信息
     *
     * @param equipmentModelId
     * @return
     */
    List<BaseProductApiResp> productInfo(String equipmentModelId);

    /**
     * 查询组织信息
     *
     * @param productId
     * @return
     */
    List<BaseDepartmentResp> groupInfo(String productId);

    /**
     * 设备型号详情
     *
     * @param id
     * @return
     */
    BaseProductDetailResp getEquipmentModelById(long id);
    

    List<BaseProductDetailResp> getEquipmentModelByIds(List<Long> ids);

    /**
     * 设备型号详情 根据业务编号查询
     *
     * @param code
     * @return
     */
    BaseProductDetailResp getEquipmentModelByCode(String code);

    /**
     * 设备型号发布状态变更
     *
     * @param param
     * @return
     */
    Boolean equipmentModelRelease(BaseEquipmentModelReleaseParam param);


    /**
     * 更新设备型号对应总线拓扑结构版本
     *
     * @param param
     * @return
     */
    Boolean updateEquipmentModel(BaseEquipmentModelUpdateParam param);

    /**
     * 机型列表分页查询
     *
     * @param param
     * @return
     */
    Collection<Long> pageEquipmentByParamFilterIds(BaseEquipmentPageParam param);


    /**
     * 机型下设备号列表分页查询
     *
     * @param param
     * @return
     */
    Collection<Long> pageModelByParamFilterIds(BaseEquipmentModelPageParam param);


    Collection<Long> pageModelByParamFilterDeviceTypeIds(BaseEquipmentModelPageParam param);

    List<BaseProductResp> listByParam(ProductListParam param);

    String getProductName(long id);

    List<BaseProduct> getByPermission(@NotNull PermissionQueryEnum query);

    /**
     * 只包含某一个类型的数据.
     * @param query
     * @param productType
     * @param otherWrapper
     * @return
     */
    List<BaseProduct> onlyProductTypeByPermission(@NotNull PermissionQueryEnum query, @NotNull ProductTypeEnum productType, LambdaQueryWrapper<BaseProduct> otherWrapper);

    List<BaseProduct> getByPermission(@NotNull PermissionQueryEnum query, @NotNull ProductTypeEnum lowestProductType, LambdaQueryWrapper<BaseProduct> otherWrapper);
}
