package com.rootcloud.ids.ucenter.service.redis;

import com.rootcloud.ids.ucenter.vo.resp.base.BaseOrganizationResp;

import java.util.List;

/**
 * <AUTHOR>
 * @description 组织列表缓存接口类
 * @since 2022/3/1 2:59 下午
 */
public interface IRedisOrganizationService {

    /**
     * 获取缓存组织列表
     *
     * @return List<BaseOrganizationResp>
     */
    List<BaseOrganizationResp> get();

    /**
     * 放入缓存组织列表
     *
     * @param value 数据
     * @return Boolean
     */
    Boolean set(List<BaseOrganizationResp> value);
}
