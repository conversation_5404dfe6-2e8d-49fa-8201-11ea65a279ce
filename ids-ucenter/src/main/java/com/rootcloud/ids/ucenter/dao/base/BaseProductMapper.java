package com.rootcloud.ids.ucenter.dao.base;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rootcloud.ids.ucenter.entity.base.BaseProduct;

import java.util.List;
import java.util.Map;
import lombok.Data;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 产品分类表 Mapper 接口
 * <AUTHOR>
 * @since 2022-02-24
 */
@Mapper
public interface BaseProductMapper extends BaseMapper<BaseProduct> {

    /**
     * 根据设备id向上递归查询产品名称
     * @param id
     * @return
     */
    String getProductName(@Param("id") long id, @Param("isEnglish") boolean isEnglish);
    
	List<ProductName> findSuperProductNamesByProducts(
			@Param("products") List<BaseProduct> products, @Param("isEnglish") boolean isEnglish
	);


	@Data
	class ProductName {

		private Long id;

		private String name;

	}

}
