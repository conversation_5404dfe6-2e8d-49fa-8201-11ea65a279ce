package com.rootcloud.ids.ucenter.vo.resp.openapi;

import com.rootcloud.esmp.common.enums.EquipmentTypeEnum;
import com.rootcloud.esmp.common.enums.ProductStatusEnum;
import com.rootcloud.esmp.common.enums.ProductTypeEnum;
import com.rootcloud.esmp.common.enums.ReleaseStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: ling.kang
 * @create: 2022-03-01 15:37
 **/
@Data
@ApiModel(value = "BaseProductApiResp", description = "openApi组织结构")
public class BaseProductApiResp {

    @ApiModelProperty("#分类id#")
    private Long id;

    @ApiModelProperty("#父级id#")
    private Long parentId;

    @ApiModelProperty("#机械大类名称#")
    private String parentName;

    @ApiModelProperty("#分类名称#")
    private String productName;

    @ApiModelProperty("#产品状态 1:启用:ENABLE,0:未启用:NOT_ENABLED#")
    private ProductStatusEnum productStatus;

    @ApiModelProperty("#分类层级# 1:机械大类:CONSTRUCTION_MACHINERY,2:机型:EQUIPMENT,3:设备号:EQUIPMENT_MODEL")
    private ProductTypeEnum productType;

    @ApiModelProperty("#发布状态#")
    private ReleaseStatusEnum releaseStatus;

    @ApiModelProperty("#租户ID#")
    private String tenantId;

    @ApiModelProperty("#产品大类编码#")
    private EquipmentTypeEnum productCode;
}
