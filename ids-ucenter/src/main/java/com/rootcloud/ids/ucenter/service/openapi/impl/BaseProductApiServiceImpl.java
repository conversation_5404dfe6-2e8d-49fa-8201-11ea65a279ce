package com.rootcloud.ids.ucenter.service.openapi.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.rootcloud.esmp.common.enums.PermissionQueryEnum;
import com.rootcloud.ids.ucenter.entity.base.BaseProduct;
import com.rootcloud.esmp.common.enums.ProductTypeEnum;
import com.rootcloud.ids.ucenter.service.base.IBaseProductService;
import com.rootcloud.ids.ucenter.service.openapi.BaseProductApiService;
import com.rootcloud.ids.ucenter.utils.convertor.base.BaseProductRespConvertor;
import com.rootcloud.esmp.common.dto.cache.BaseProductResp;
import com.rootcloud.ids.ucenter.vo.resp.openapi.BaseProductApiResp;
import com.rootcloud.ids.ucenter.vo.resp.openapi.BaseProductDetailApiResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: ling.kang
 * @create: 2022-03-01 15:00
 **/
@Service
public class BaseProductApiServiceImpl implements BaseProductApiService {

    @Autowired
    private IBaseProductService baseProductService;

    @Value("${equipmentModel.unPublishedExclude}")
    private Boolean unPublishedExclude;

    /**
     * 产品分类(机械大类、机型、设备号)详情
     *
     * @param id
     * @return
     */
    @Override
    public BaseProductDetailApiResp getInfo(long id) {
        BaseProductDetailApiResp baseProductDetailApiResp = new BaseProductDetailApiResp();
        BaseProduct baseProduct = baseProductService.getOne(Wrappers.lambdaQuery(BaseProduct.class)
                .eq(BaseProduct::getId, id));
        BeanUtil.copyProperties(baseProduct, baseProductDetailApiResp);
        return baseProductDetailApiResp;
    }

    /**
     * 产品分类结构列表
     *
     * @return
     */
    @Override
    public List<BaseProductApiResp> listProduct() {
        //查询用户拥有的机型权限
        List<BaseProduct> modelList = baseProductService.getByPermission(PermissionQueryEnum.CROSS_TENANT, ProductTypeEnum.EQUIPMENT, null);
        return BaseProductRespConvertor.change2ProductApiRespList(modelList.stream()
            .sorted(Comparator.comparing(BaseProduct::getId).reversed().thenComparing(BaseProduct::getCreateTime))
            .collect(Collectors.toList()).listIterator());
    }

    /**
     * 机型结构列表
     */
    @Override
    public List<BaseProductApiResp> equipmentList(boolean acrossTheTenant) {
        PermissionQueryEnum permissionQuery = acrossTheTenant ? PermissionQueryEnum.CROSS_TENANT : PermissionQueryEnum.CURRENT_TENANT;
        //查询用户拥有的机型权限
        List<BaseProduct> equipments = baseProductService.getByPermission(permissionQuery);
        equipments = equipments.stream().filter(bp -> !Objects.equals(bp.getProductType(), ProductTypeEnum.CONSTRUCTION_MACHINERY)).collect(Collectors.toList());
        // 过滤掉未发布的设备型号
        return BaseProductRespConvertor.change2ProductApiRespList(equipments.stream()
                .sorted(Comparator.comparing(BaseProduct::getId).reversed().thenComparing(BaseProduct::getCreateTime))
                // filter匹配 不是设备型号类型 或者 是设备型号类型 但是为已发布
                .filter(baseProductResp -> baseProductResp.getProductType() != ProductTypeEnum.EQUIPMENT_MODEL || (Objects.nonNull(unPublishedExclude) && unPublishedExclude && (Objects.isNull(baseProductResp.getHasPublished()) || baseProductResp.getHasPublished() == 0)))
                .collect(Collectors.toList()).listIterator());
    }

    /**
     * 设备型号列表
     *
     * @return
     */
    @Override
    public List<BaseProductApiResp> modelList() {
        //查询用户拥有的机型权限
        List<BaseProduct> modelList = baseProductService.onlyProductTypeByPermission(PermissionQueryEnum.CROSS_TENANT, ProductTypeEnum.EQUIPMENT, null);
        return BaseProductRespConvertor.change2ProductApiRespList(modelList.stream()
                .sorted(Comparator.comparing(BaseProduct::getId).reversed().thenComparing(BaseProduct::getCreateTime))
                .collect(Collectors.toList()).listIterator());
    }

}
