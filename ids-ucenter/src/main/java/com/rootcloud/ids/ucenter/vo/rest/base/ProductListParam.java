package com.rootcloud.ids.ucenter.vo.rest.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import lombok.Data;

/**
 *
 *
 * <AUTHOR>
@Data
@ApiModel(value = "ProductListParam", description = "查询产品列表")
public class ProductListParam implements Serializable {

    private static final long serialVersionUID = -7793482116909492431L;

    @ApiModelProperty("#设备型号id-可选#")
    private List<Long> equipmentModelIds;

    @ApiModelProperty("#设备型号code-可选#")
    private Collection<String> equipmentModelCodeList;

}
