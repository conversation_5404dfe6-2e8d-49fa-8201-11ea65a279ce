package com.rootcloud.ids.ucenter.entity.backup;

import com.rootcloud.ids.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 用户实体对象
 * @ClassName SysUser
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Getter
@Setter
@TableName("ucenter_user")
public class UcenterUser extends BaseEntity {

    /**
     * 用户ID
     */
    @TableId(value = "user_id", type = IdType.ASSIGN_ID)
    private Long userId;
    /**
     * 用户账号
     */
    private String account;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 用户密码
     */
    private String password;
    /**
     * 用户昵称
     */
    private String nickName;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 用户性别 1男，0女，2其它
     */
    private Integer sex;
    /**
     * 用户状态 1正常，5冻结，0停用
     */
    private Integer state;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 头像链接(Base64地址)
     */
    private String portrait;
    /**
     * 证件类型（1：身份证，2：军官证，3：驾驶证）
     */
    private Integer cardType;
    /**
     * 证件号码
     */
    private String cardNo;
    /**
     * 注册来源（1：App，2：Web，3：Wechat，4：Unknown）
     */
    private Integer registFrom;
}
