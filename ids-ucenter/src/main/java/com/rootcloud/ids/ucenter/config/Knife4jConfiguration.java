package com.rootcloud.ids.ucenter.config;

import com.rootcloud.ids.common.core.constant.SecurityConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.request.async.DeferredResult;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description Knife4j配置文件
 * @ClassName Knife4jConfiguration
 * <AUTHOR>
 * @Date 2021/12/15
 * @Version 1.0
 */
//@Configuration
//@EnableSwagger2WebMvc
public class Knife4jConfiguration {

    static List<Parameter> webParameters = new ArrayList<>();

    static {
        ParameterBuilder webToken = new ParameterBuilder();
        webToken.name(SecurityConstants.ACCESS_TOKEN).description("令牌").modelRef(new ModelRef("string")).parameterType("header")
                .required(false).build();
        Parameter webPar = webToken.build();

        webParameters.add(webPar);
    }

    //@Bean(value = "defaultApi2")
    public Docket defaultApi2() {
        final String regex = "/api/.*";
        Docket docket = new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(new ApiInfoBuilder()
                        .title("数据权限中心接口文档")
                        .description("# ids-ucenter RESTful APIs")
                        .termsOfServiceUrl("http://www.xxx.cn/")
                        .contact("<EMAIL>")
                        .version("1.0")
                        .build())
                //分组名称
                .groupName("ids-ucenter")
                .select()

                //这里指定Controller扫描包路径
                .apis(RequestHandlerSelectors.basePackage("com.rootcloud.ids.ucenter.controller"))
                .paths(PathSelectors.regex(regex))

                .build();
        docket.enable(true).groupName("ids-ucenter")
                .genericModelSubstitutes(DeferredResult.class)
                .globalOperationParameters(webParameters)
                // base，最终调用接口后会和paths拼接在一起
                .useDefaultResponseMessages(false).forCodeGeneration(true).pathMapping("/");
        return docket;
    }

    //TODO 移到公共配置
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
