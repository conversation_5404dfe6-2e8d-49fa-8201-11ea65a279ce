package com.rootcloud.ids.ucenter.config;

import cn.hutool.extra.spring.SpringUtil;
import com.rootcloud.esmp.web.config.LanguageLocalConfig;
import com.rootcloud.esmp.web.converter.EsmpReadHttpMessageConverter;
import com.rootcloud.esmp.web.converter.EsmpWriteHttpMessageConverter;
import com.rootcloud.ids.ucenter.handle.ClearInterceptor;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @description
 * @since 2022/3/2 5:48 下午
 */
@Configuration
@Import(SpringUtil.class)
public class WebConfig implements WebMvcConfigurer {

    @Value("${converter.timeZoneId:UTC}")
    private String timeZoneId;

    @Value("${converter.ignoreUris:}")
    private List<String> ignoreUris;

    @Value("${converter.writeDateFormat:}")
    private String supportDateFormat;

    @Value("${converter.readDateFormats:}")
    private List<String> readSupportDateFormats;

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(0, new EsmpWriteHttpMessageConverter(ignoreUris, supportDateFormat, timeZoneId));
        converters.add(1, new EsmpReadHttpMessageConverter(ignoreUris, readSupportDateFormats, timeZoneId));
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(clearInterceptor()).addPathPatterns("/api/**");
    }

    /**
     * 解决拦截器中不能依赖注入的问题
     *
     * @return
     */
    @Bean
    public ClearInterceptor clearInterceptor() {
        return new ClearInterceptor();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public LocaleResolver localeResolver() {
        return new LanguageLocalConfig();
    }
}
