package com.rootcloud.ids.ucenter.service.redis;

import com.rootcloud.ids.ucenter.vo.resp.base.BaseDepartmentResp;

import java.util.List;

/**
 * <AUTHOR>
 * @description 部门列表缓存接口类
 * @since 2022/3/1 2:59 下午
 */
public interface IRedisDepartmentService {

    /**
     * 获取缓存部门列表
     *
     * @return List<BaseDepartmentResp>
     */
    List<BaseDepartmentResp> get(String id);

    /**
     * 放入缓存部门列表
     *
     * @param value 数据
     * @return Boolean
     */
    Boolean set(String id ,List<BaseDepartmentResp> value);
}
