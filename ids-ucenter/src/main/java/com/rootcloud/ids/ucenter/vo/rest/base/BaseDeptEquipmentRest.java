package com.rootcloud.ids.ucenter.vo.rest.base;

import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: kang.ling
 * @create: 2022-03-04 17:29
 **/
@Data
@ApiModel(value = "BaseDeptEquipmentRest", description = "部门所拥有机型参数")
public class BaseDeptEquipmentRest {

    @ApiModelProperty(value = "#部门或者组织ID#", required = true)
    @NotBlank(message = "id不能为空")
    private String id;

    @ApiModelProperty(value = "#部门或者组织ID#", required = true)
    @NotNull(message = "deptType不能为空")
    private DeptTypeEnum deptType;

}
