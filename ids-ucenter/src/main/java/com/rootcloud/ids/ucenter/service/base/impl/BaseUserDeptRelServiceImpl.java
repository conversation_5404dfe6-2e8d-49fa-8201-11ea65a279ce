package com.rootcloud.ids.ucenter.service.base.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rootcloud.ids.ucenter.entity.base.BaseUserDeptRel;
import com.rootcloud.ids.ucenter.dao.base.BaseUserDeptRelMapper;
import com.rootcloud.ids.ucenter.service.base.IBaseUserDeptRelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 部门-用户-关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Service
public class BaseUserDeptRelServiceImpl extends ServiceImpl<BaseUserDeptRelMapper, BaseUserDeptRel> implements IBaseUserDeptRelService {

    @Override
    public List<BaseUserDeptRel> listByDeptId(String deptId) {
        return this.list(Wrappers.lambdaQuery(BaseUserDeptRel.class).eq(BaseUserDeptRel::getDeptId, deptId));
    }

    @Override
    public List<BaseUserDeptRel> listUserId(String userId) {
        return this.list(Wrappers.lambdaQuery(BaseUserDeptRel.class).eq(BaseUserDeptRel::getUserId, userId));

    }

    @Override
    public BaseUserDeptRel getInfoByUserId(String userId) {
        return this.getOne(Wrappers.lambdaQuery(BaseUserDeptRel.class).eq(BaseUserDeptRel::getUserId, userId));
    }

}
