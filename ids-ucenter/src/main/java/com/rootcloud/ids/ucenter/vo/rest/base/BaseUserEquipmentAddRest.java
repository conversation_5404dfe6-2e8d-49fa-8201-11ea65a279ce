package com.rootcloud.ids.ucenter.vo.rest.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: ling.kang
 * @create: 2022-03-01 11:16
 **/
@Data
@ApiModel(value = "BaseUserEquipmentAddRest", description = "用户新增机型权限参数")
public class BaseUserEquipmentAddRest {

    @ApiModelProperty("#用户id#")
    @NotNull(message = "用户id不能为空")
    private String userId;

    @ApiModelProperty("#机型ids#")
    @NotNull(message = "机型ids不能为空")
    private List<Long> ids;

    @ApiModelProperty("#用户所属主组织ID#")
    private String organizationId;

    @ApiModelProperty("#用户所属的部门#")
    private List<String> deptIdList;

}
