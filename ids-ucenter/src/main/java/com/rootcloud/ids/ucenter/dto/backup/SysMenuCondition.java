package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 菜单条件查询对象
 * @ClassName SysMenuCondition
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "菜单条件查询对象")
@Getter
@Setter
public class SysMenuCondition extends BasePageQuery {

    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    @ApiModelProperty(value = "菜单英文名称")
    private String englishName;

    @ApiModelProperty(value = "菜单显示名称")
    private String showName;
}
