package com.rootcloud.ids.ucenter.entity.backup;

import com.rootcloud.ids.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 角色-权限关联对象
 * @ClassName SysRolePermission
 * <AUTHOR>
 * @Date 2021/12/21
 * @Version 1.0
 */
@Getter
@Setter
@TableName("ucenter_role_permission")
public class UcenterRolePermission extends BaseEntity {

    /**
     * 角色ID
     */
    private Long roleId;
    /**
     * 权限ID
     */
    private Long pmsId;
}
