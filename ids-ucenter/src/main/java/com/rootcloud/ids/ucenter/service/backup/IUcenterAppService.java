package com.rootcloud.ids.ucenter.service.backup;

import com.rootcloud.ids.ucenter.dto.backup.SysAppCondition;
import com.rootcloud.ids.ucenter.dto.backup.SysAppSaveDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysAppUpdateDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterApp;
import com.rootcloud.ids.ucenter.vo.backup.SysAppQueryVO;
import com.rootcloud.ids.ucenter.vo.backup.SysAppVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;

/**
 * @Description 系统应用服务类接口
 * @InterfaceName ISysAppService
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public interface IUcenterAppService extends IService<UcenterApp> {

    /**
     * 保存应用
     * @param appSaveDTO
     * @return
     */
    Long save(SysAppSaveDTO appSaveDTO);

    /**
     * 更新应用
     * @param appUpdateDTO
     * @return
     */
    Boolean update(SysAppUpdateDTO appUpdateDTO);

    /**
     * 批量删除
     * @param appIds
     */
    Boolean batchDelete(Long[] appIds);

    /**
     * 根据ID查询应用
     * @param appId
     * @return
     */
    SysAppVO findById(Long appId);

    /**
     * 条件查询
     * @param cnd
     * @return
     */
    PageInfo<SysAppQueryVO> query(SysAppCondition cnd);

}
