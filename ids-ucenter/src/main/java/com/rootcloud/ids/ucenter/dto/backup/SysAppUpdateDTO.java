package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @Description 系统应用更新DTO
 * @ClassName SysAppUpdateDTO
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "系统应用更新DTO")
@Getter
@Setter
public class SysAppUpdateDTO extends BaseDTO {


    @ApiModelProperty(value = "应用ID", required = true)
    @NotNull(message = "应用ID不能为空")
    private Long appId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "应用简介")
    private String remark;

    @ApiModelProperty(value = "应用logo地址")
    private String appLogo;

    @ApiModelProperty(value = "应用首页地址")
    private String indexUrl;

    @ApiModelProperty(value = "授权登录回调接口地址")
    private String callBackUrl;

}
