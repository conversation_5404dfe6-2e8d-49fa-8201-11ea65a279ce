package com.rootcloud.ids.ucenter.entity.backup;

import com.rootcloud.ids.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 系统权限对象
 * @ClassName Permission
 * <AUTHOR>
 * @Date 2021/12/20
 * @Version 1.0
 */
@Getter
@Setter
@TableName("ucenter_permission")
public class UcenterPermission extends BaseEntity {

    /**
     * 权限ID
     */
    @TableId(value = "permission_id", type = IdType.ASSIGN_ID)
    private Long permissionId;
    /**
     * 所属模块
     */
    private String module;

    /**
     * request请求方式
     */
    private String method;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限值
     */
    private String permissionValue;
    /**
     * 描述信息
     */
    private String remark;

}
