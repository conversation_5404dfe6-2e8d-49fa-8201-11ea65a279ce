package com.rootcloud.ids.ucenter.dto.backup;

import com.rootcloud.ids.common.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 角色查询条件
 * @ClassName SysRoleCondition
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@ApiModel(value = "角色查询条件")
@Getter
@Setter
public class SysRoleCondition extends BasePageQuery {

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "英文名称")
    private String englishName;
}
