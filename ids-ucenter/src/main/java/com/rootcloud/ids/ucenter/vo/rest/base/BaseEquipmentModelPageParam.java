package com.rootcloud.ids.ucenter.vo.rest.base;

import com.rootcloud.esmp.common.enums.PermissionQueryEnum;
import com.rootcloud.ids.common.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: ling.kang
 * @create: 2022-02-25 17:01
 **/
@Data
@ApiModel(value = "BaseEquipmentModelPageParam", description = "设备号列表参数")
public class BaseEquipmentModelPageParam extends BasePageQuery implements Serializable {

    @ApiModelProperty("#机械大类id、机型id#")
    private Long id;

    @ApiModelProperty("#设备型号名称#")
    private String productName;

    /**
     * 是否跨租户
     */
    @Deprecated
    @ApiModelProperty(value = "#是否跨组织，废弃#", hidden = true)
    private boolean acrossTheTenant;

    @ApiModelProperty("#设备型号id#")
    private Long equipmentModelId;

    @ApiModelProperty(value = "#查询数据范围，默认：CROSS_TENANT，CROSS_TENANT: 查询有权限的所有组织数据；CURRENT_TENANT: 查询当前组织数据；DELEGATED_TENANT：查询授权给本组织的数据#")
    private PermissionQueryEnum permissionQuery;
}
