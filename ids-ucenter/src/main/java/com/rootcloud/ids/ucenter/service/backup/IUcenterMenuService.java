package com.rootcloud.ids.ucenter.service.backup;

import com.rootcloud.ids.ucenter.dto.backup.SysMenuCondition;
import com.rootcloud.ids.ucenter.dto.backup.SysMenuSaveDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysMenuUpdateDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterMenu;
import com.rootcloud.ids.ucenter.vo.backup.SysMenuQueryVO;
import com.rootcloud.ids.ucenter.vo.backup.SysMenuVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * @Description 系统菜单服务接口类
 * @InterfaceName ISysMenuService
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public interface IUcenterMenuService extends IService<UcenterMenu> {

    /**
     * 保存菜单
     * @param menuSaveDTO
     * @return
     */
    Long save(SysMenuSaveDTO menuSaveDTO);

    /**
     * 更新菜单
     * @param menuUpdateDTO
     * @return
     */
    Boolean update(SysMenuUpdateDTO menuUpdateDTO);


    /**
     * 批量删除
     * @param menuIds
     */
    Boolean batchDelete(Long[] menuIds);

    /**
     * 根据ID查询菜单
     * @param menuId
     * @return
     */
    SysMenuVO findById(Long menuId);

    /**
     * 条件查询
     * @param cnd
     * @return
     */
    PageInfo<SysMenuQueryVO> query(SysMenuCondition cnd);

    /**
     * 查询子集菜单（一级）
     * @param menuId
     * @return
     */
    List<SysMenuVO> getChilds(Long menuId);

    /**
     * 级联获取子菜单集合（所有层级）
     * @param menuId
     * @return
     */
    List<SysMenuVO> getAllChilds(Long menuId);

    /**
     * 查询用户的菜单信息
     * @param userId
     * @return
     */
    List<SysMenuVO> findMenuByUserId(Long userId);

}
