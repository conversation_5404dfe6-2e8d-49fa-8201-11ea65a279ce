package com.rootcloud.ids.ucenter.service.backup;

import com.rootcloud.ids.ucenter.dto.backup.SysGroupCondition;
import com.rootcloud.ids.ucenter.dto.backup.SysGroupSaveDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysGroupUpdateDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterGroup;
import com.rootcloud.ids.ucenter.vo.backup.SysGroupQueryVO;
import com.rootcloud.ids.ucenter.vo.backup.SysGroupVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;

/**
 * @Description 系统组织服务类接口
 * @InterfaceName ISysGroupService
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public interface IUcenterGroupService extends IService<UcenterGroup> {

    /**
     * 保存组织
     * @param groupSaveDTO
     * @return
     */
    Long save(SysGroupSaveDTO groupSaveDTO);

    /**
     * 更新组织
     * @param groupUpdateDTO
     * @return
     */
    Boolean update(SysGroupUpdateDTO groupUpdateDTO);

    /**
     * 根据ID查询组织
     * @param groupId
     * @return
     */
    SysGroupVO findById(Long groupId);


    /**
     * 批量删除
     * @param groupIds
     */
    Boolean batchDelete(Long[] groupIds);

    /**
     * 分页查询
     * @param cnd
     * @return
     */
    PageInfo<SysGroupQueryVO> query(SysGroupCondition cnd);

}
