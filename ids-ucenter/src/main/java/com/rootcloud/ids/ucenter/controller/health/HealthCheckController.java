package com.rootcloud.ids.ucenter.controller.health;


import com.rootcloud.ids.common.core.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "HealthCheckController", tags = "健康检查")
@RestController
@Slf4j
@RequestMapping("/health")
public class HealthCheckController {

  @ApiOperation(value = "健康检查", notes = "健康检查")
  @GetMapping(value = "/liveness")
  public Result<Boolean> check() {
	return null;
  }
}
