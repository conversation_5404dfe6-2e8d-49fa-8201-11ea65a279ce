package com.rootcloud.ids.ucenter.entity.backup;

import com.rootcloud.ids.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 应用实体对象
 * @ClassName SysApp
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
@Getter
@Setter
@TableName("ucenter_app")
public class UcenterApp extends BaseEntity {

    /**
     * 应用ID
     */
    @TableId(value = "app_id", type = IdType.ASSIGN_ID)
    private Long appId;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 英文名称
     */
    private String englishName;
    /**
     * 应用简介
     */
    private String remark;
    /**
     * 应用logo地址
     */
    private String appLogo;
    /**
     * 应用首页地址
     */
    private String homePage;
    /**
     * 授权登录回调接口地址
     */
    private String redirectUri;

}
