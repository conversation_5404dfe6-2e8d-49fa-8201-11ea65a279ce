package com.rootcloud.ids.ucenter.vo.rest.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author: ling.kang
 * @create: 2022-03-01 11:09
 **/
@Data
@ApiModel(value = "BaseUserDelParam", description = "用户机型权限删除参数")
public class BaseUserDelParam {

    @ApiModelProperty("#主键id#")
    @NotNull(message = "用户id不能为空")
    private Long id;

}
