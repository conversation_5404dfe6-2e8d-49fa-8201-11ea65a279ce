package com.rootcloud.ids.ucenter.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.context.request.async.DeferredResult;
import springfox.bean.validators.configuration.BeanValidatorPluginsConfiguration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-10-16
 */

@Configuration
@EnableSwagger2WebMvc
@EnableKnife4j
@Import({BeanValidatorPluginsConfiguration.class})
public class SwaggerConfig {


    //@Value("${swagger.show}")
    private boolean swaggerShow = true;

    private static final String TITLE_FORMAT = "UCENTER API: %s";

    private static final String DESCRIPTION_FORMAT = "UCENTER %s REST API, all the applications could access the Object model data via JSON.";


    static List<Parameter> webParameters = new ArrayList<Parameter>();

    static {
        ParameterBuilder webToken = new ParameterBuilder();
        webToken.name("access_token").description("令牌").modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(false).build();
        Parameter webPar = webToken.build();

        ParameterBuilder webTokenExpires = new ParameterBuilder();
        webTokenExpires.name("expires").description("令牌过期时间")
                .modelRef(new ModelRef("string")).parameterType("header")
                .required(false).build();
        Parameter webTokenExpiresPar = webTokenExpires.build();

        ParameterBuilder dgInfo = new ParameterBuilder();
        dgInfo.name("dg-info").description("是否返回异常（dg-info=show）")
                .modelRef(new ModelRef("boolean")).parameterType("header")
                .required(false).build();
        Parameter dgInfoPar = dgInfo.build();

        ParameterBuilder devKey = new ParameterBuilder();
        devKey.name("devKey").description("开发key").modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(false).build();
        Parameter devKeyPar = devKey.build();
        webParameters.add(webPar);
        webParameters.add(webTokenExpiresPar);
        webParameters.add(dgInfoPar);
        webParameters.add(devKeyPar);
    }

    private Docket createDocket(String apiName, String basePackage, String regex) {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).groupName(apiName)
                .genericModelSubstitutes(DeferredResult.class)
                // base，最终调用接口后会和paths拼接在一起
                .useDefaultResponseMessages(false).forCodeGeneration(true).pathMapping("/")
                .select()
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                // 过滤的接口
                .paths(PathSelectors.regex(regex))
                .build().globalOperationParameters(webParameters).apiInfo(apiInfo(apiName));
    }

    private Docket createDocket(String apiName, String basePackage) {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).groupName(apiName)
                .genericModelSubstitutes(DeferredResult.class)
                // base，最终调用接口后会和paths拼接在一起
                .useDefaultResponseMessages(false).forCodeGeneration(true).pathMapping("/")
                .select()
                .apis(RequestHandlerSelectors.basePackage(basePackage))
                // 过滤的接口
                //                .paths(PathSelectors.regex(regex))
                .build().globalOperationParameters(webParameters).apiInfo(apiInfo(apiName));
    }

    private ApiInfo apiInfo(String title) {
        return new ApiInfoBuilder().
                // 大标题
                        title(String.format(TITLE_FORMAT, title))
                // 详细描述
                .description(String.format(DESCRIPTION_FORMAT, title))
                // 版本
                .version("1.0")
                .termsOfServiceUrl("glian of service")
                // 作者
                .contact(new Contact("glian", "", ""))
                .license("The Apache License, Version 2.0")
                .licenseUrl("http://www.apache.org/licenses/LICENSE-2.0.html").build();
    }

    public SwaggerConfig() {
    }

    //    @Bean({"defaultApi"})
    public Docket defaultApi2() {

        //添加header参数
        ParameterBuilder ticketPar = new ParameterBuilder();
        List<Parameter> pars = new ArrayList<>();
        ticketPar.name("Authorization")
                .description("oauth2 协议需要在参数前面添加Bearer ")
                .modelRef(new ModelRef("String")).parameterType("header")
                .required(false).build();
        pars.add(ticketPar.build());

        Docket docket = (new Docket(DocumentationType.SWAGGER_2))
                .apiInfo(this.apiInfo())
                .groupName("接口文档")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.tuns.team.controller")).paths(
                        PathSelectors.any()).build().globalOperationParameters(pars);
        return docket;
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                // 大标题
                .title(String.format(TITLE_FORMAT, "UCENTER API"))
                // 详细描述
                .description(String.format(DESCRIPTION_FORMAT, "UCENTER API"))
                // 版本
                .version("1.0")
                .termsOfServiceUrl("glian of service")
                // 作者
                .contact(new Contact("glian", "", ""))
                .license("The Apache License, Version 2.0")
                .licenseUrl("http://www.apache.org/licenses/LICENSE-2.0.html").build();
    }

    @Bean
    public Docket baseApi() {
        return createDocket("ids-ucenter base API", "com.rootcloud.ids.ucenter.controller");
    }

//    @Bean
//    public Docket dmpApi() {
//        return createDocket("DMP SERVER dmp API", "com.rootcloud.dmp.controller.dmp", "/api/dmp/.*");
//    }
//
//    @Bean
//    public Docket dmpOpenApi() {
//        return createDocket("DMP SERVER dmp Open API", "com.rootcloud.dmp.controller.dmp.openapi",
//                "/api/dmp/.*");
//    }
}
