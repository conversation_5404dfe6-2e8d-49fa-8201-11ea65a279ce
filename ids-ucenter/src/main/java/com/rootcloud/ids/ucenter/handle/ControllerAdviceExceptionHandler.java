package com.rootcloud.ids.ucenter.handle;

import static com.rootcloud.ids.common.i18n.I18nCode.SYS_100000;
import static com.rootcloud.ids.common.i18n.I18nCode.SYS_100161;
import static com.rootcloud.ids.common.i18n.I18nCode.SYS_100162;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.rootcloud.ids.common.core.result.Result;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
public class ControllerAdviceExceptionHandler {

  /**
   * 统一处理非自定义异常外的所有异常，前端有特殊处理，这里不能处理
   */
  //@ExceptionHandler(Exception.class)
  //public Result handleException(Exception e) {
  //  log.error("Exception error message:{}", e);
  //  return Result.failed(SYS_100000.getMessage());
  //}

  @ExceptionHandler(InvalidFormatException.class)
  public Result handHttpConverterException(InvalidFormatException e) {
    List<JsonMappingException.Reference> path = e.getPath();
    String field = path.stream().map(JsonMappingException.Reference::getFieldName)
        .collect(Collectors.joining("."));
    if (StringUtils.hasLength(field)) {
      return Result.failed(SYS_100161.getMessage(field));
    }
    return Result.failed(SYS_100161.getMessage());
  }

  @ExceptionHandler(HttpMessageNotReadableException.class)
  public Result<?> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
    Throwable rootCause = e.getRootCause();
    if (rootCause instanceof InvalidFormatException) {
      return handHttpConverterException((InvalidFormatException) rootCause);
    }
    return Result.failed(SYS_100162.getMessage());
  }
}
