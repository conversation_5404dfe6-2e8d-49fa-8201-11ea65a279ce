package com.rootcloud.ids.ucenter.vo.resp.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rootcloud.esmp.common.enums.EquipmentTypeEnum;
import com.rootcloud.esmp.common.enums.ProductStatusEnum;
import com.rootcloud.esmp.common.enums.ProductTypeEnum;
import com.rootcloud.esmp.common.enums.ReleaseStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: ling.kang
 * @create: 2022-02-25 11:10
 **/
@Data
@ApiModel(value = "BaseProductResp", description = "机械大类结构")
@Deprecated
public class BaseProductResp implements Serializable {

    private static final long serialVersionUID = 1334176005815047842L;

    @ApiModelProperty("#分类id#")
    private Long id;

    @ApiModelProperty("#父级id#")
    private Long parentId;

    @ApiModelProperty("#分类名称#")
    private String productName;

    @ApiModelProperty("#产品状态 1:启用:ENABLE,0:未启用:NOT_ENABLED#")
    private ProductStatusEnum productStatus;

    @ApiModelProperty("#分类层级# 1:机械大类:CONSTRUCTION_MACHINERY,2:机型:EQUIPMENT,3:设备号:EQUIPMENT_MODEL")
    private ProductTypeEnum productType;

    @ApiModelProperty("#租户ID#")
    private String tenantId;

    @ApiModelProperty("#创建人#")
    private String creator;

    @ApiModelProperty("#修改人#")
    private String modifier;

    @ApiModelProperty("#创建时间#")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("#更新时间#")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("#产品编码#")
    private EquipmentTypeEnum productCode;

    @ApiModelProperty("#发布状态 0:已发布 1:未发布#")
    private ReleaseStatusEnum releaseStatus;

    @ApiModelProperty(value = "#是否发布过 0 已发布过 1 未发布过#")
    private Integer hasPublished;

    @ApiModelProperty("#产品业务编码#")
    private String productBusinessCode;

}
