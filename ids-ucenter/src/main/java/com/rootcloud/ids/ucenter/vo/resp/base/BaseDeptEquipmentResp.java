package com.rootcloud.ids.ucenter.vo.resp.base;

import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: ling.kang
 * @create: 2022-02-28 14:40
 **/
@Data
@ApiModel(value = "BaseDeptEquipmentResp", description = "机型列表")
public class BaseDeptEquipmentResp implements Serializable {

    private static final long serialVersionUID = -2080958508773237502L;

    @ApiModelProperty("#机械大类id#")
    private Long conMacId;

    @ApiModelProperty("#机械大类#")
    private String constructionMachineryName;

    @ApiModelProperty("#机型id#")
    private Long equipmentId;

    @ApiModelProperty("#机型名称#")
    private String equipmentName;

    @ApiModelProperty("#部门id#")
    private String deptId;

    @ApiModelProperty("#所有权归属#")
    private String deptName;

    @ApiModelProperty("#部门类型#")
    private DeptTypeEnum deptType;

    @ApiModelProperty("#租户ID#")
    private String tenantId;
}
