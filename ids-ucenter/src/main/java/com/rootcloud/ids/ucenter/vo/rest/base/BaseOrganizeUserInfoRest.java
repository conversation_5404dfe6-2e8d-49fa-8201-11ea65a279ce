package com.rootcloud.ids.ucenter.vo.rest.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-02-28 16:49
 **/
@Data
@ApiModel(value = "BaseOrganizeUserInfoRest", description = "组织用户详情查询")
public class BaseOrganizeUserInfoRest implements Serializable {

    private static final long serialVersionUID = 2967694873994066529L;
    @ApiModelProperty(value = "#组织ID#", required = true)
    @NotBlank(message = "组织ID不能为空")
    private String cid;

    @ApiModelProperty(value = "#用户ID#", required = true)
    @NotBlank(message = "用户ID不能为空")
    private String userId;

}
