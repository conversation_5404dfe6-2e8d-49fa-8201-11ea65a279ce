/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.ids.ucenter.service.auth.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rootcloud.esmp.common.enums.IamPermissionActionEnum;
import com.rootcloud.esmp.common.enums.PermissionQueryEnum;
import com.rootcloud.esmp.common.enums.ProductTypeEnum;
import com.rootcloud.esmp.common.utils.SecurityUtils;
import com.rootcloud.esmp.iam.dto.permission.IamResourcePropertyValueDto;
import com.rootcloud.esmp.iam.dto.permission.IamResourcePropertyValueQueryDto;
import com.rootcloud.esmp.iam.dto.permission.PagingMetadata;
import com.rootcloud.esmp.iam.dto.permission.PagingResult;
import com.rootcloud.esmp.iam.service.auth.IDataPermissionService;
import com.rootcloud.ids.common.mybatis.utils.PageUtils;
import com.rootcloud.ids.ucenter.entity.base.BaseProduct;
import com.rootcloud.ids.ucenter.service.auth.IIamAuthorizationService;
import com.rootcloud.ids.ucenter.service.base.IBaseProductService;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class IamAuthorizationService implements IIamAuthorizationService {
  @Resource
  private IBaseProductService baseProductService;

  private List<BaseProduct> getBaseProduct(@NotNull ProductTypeEnum productType, IamResourcePropertyValueQueryDto param) {
    final String tenantId = SecurityUtils.getCurrentTenantIdOrElseThrow();
    LambdaQueryWrapper<BaseProduct> wrapper = Wrappers.lambdaQuery(BaseProduct.class);

    final List<String> tenantIds = param.getTenantIds();
    // 如果tenantIds为空，那么查询当前组织且有权限的数据
    if (CollectionUtils.isEmpty(tenantIds)) {
      // tenantIds为空，查询本组织的数据
      wrapper.eq(BaseProduct::getTenantId, tenantId);
    } else if (tenantIds.size() == 1 && "*".equals(tenantIds.get(0))) {
      // 如果tenantIds的元素是*，那么查询所有有权限的数据
    } else {
      // 如果tenantIds不为空，则查询指定组织的数据（目前IAM没这种场景，但可以先适配）
      wrapper.in(BaseProduct::getTenantId, tenantIds);
    }

    return baseProductService.getByPermission(PermissionQueryEnum.CROSS_TENANT, productType, wrapper);
  }

  @Override
  public PagingResult<List<IamResourcePropertyValueDto>> permissionTree(
      @NotNull ProductTypeEnum productType, IamResourcePropertyValueQueryDto param) {
    List<BaseProduct> list = getBaseProduct(productType, param);

    // 顶级节点的parentId值是0
    final String topParentId = "0";
    List<IamResourcePropertyValueDto> valueDtos = list.stream()
        .map(bp -> {
          return new IamResourcePropertyValueDto()
              .setId(bp.getId().toString())
              .setName(bp.getProductName())
              .setParent(Optional.ofNullable(bp.getParentId()).map(l -> l.toString()).orElse(topParentId))
              .setTenantId(bp.getTenantId());
        })
        .collect(Collectors.toList());
    // 获得机械大类节点（顶级节点）
    List<IamResourcePropertyValueDto> result = valueDtos.stream()
        .filter(v -> v.getParent().equals(topParentId))
        .collect(Collectors.toList());
    result.forEach(r -> setChildren(r, valueDtos));

    int total = result.size();
    PagingMetadata metadata = new PagingMetadata().setTotal(total).setSkip(0).setLimit(total);
    return new PagingResult<List<IamResourcePropertyValueDto>>().setPayload(result).setMetadata(metadata);
  }

  private void setChildren(IamResourcePropertyValueDto parent, List<IamResourcePropertyValueDto> allValues) {
    List<IamResourcePropertyValueDto> children = allValues.stream().filter(child -> parent.getId().equals(child.getParent()))
        .collect(Collectors.toList());
    children.forEach(child -> {
      child.setParentName(parent.getName());
      setChildren(child, allValues);
    });
    parent.setChildren(children);
  }

  @Override
  public PagingResult<List<IamResourcePropertyValueDto>> permissionValues(
      IamResourcePropertyValueQueryDto param) {
    if (param == null || CollectionUtils.isEmpty(param.getFilterIds())) {
      PagingMetadata metadata = new PagingMetadata().setTotal(0).setSkip(0).setLimit(0);
      return new PagingResult<List<IamResourcePropertyValueDto>>().setPayload(Collections.emptyList())
          .setMetadata(metadata);
    }

    final String tenantId = SecurityUtils.getCurrentTenantIdOrElseThrow();
    List<Long> ids = param.getFilterIds().stream().map(Long::valueOf).collect(Collectors.toList());
    LambdaQueryWrapper<BaseProduct> wrapper = Wrappers.lambdaQuery(BaseProduct.class)
        .in(BaseProduct::getId, ids);

    final List<String> tenantIds = param.getTenantIds();
    // 如果tenantIds为空，那么查询当前组织且有权限的数据
    if (CollectionUtils.isEmpty(tenantIds)) {
      // tenantIds为空，查询本组织的数据
      wrapper.and(w -> w.eq(BaseProduct::getProductType, ProductTypeEnum.CONSTRUCTION_MACHINERY)
          .or().eq(BaseProduct::getTenantId, tenantId));
    } else if (tenantIds.size() == 1 && "*".equals(tenantIds.get(0))) {
      // 如果tenantIds的元素是*，那么查询所有有权限的数据
    } else {
      // 如果tenantIds不为空，则查询指定组织的数据（目前IAM没这种场景，但可以先适配）
      wrapper.and(w -> w.eq(BaseProduct::getProductType, ProductTypeEnum.CONSTRUCTION_MACHINERY)
          .or().in(BaseProduct::getTenantId, tenantIds));
    }

    int limit = Optional.ofNullable(param.getLimit()).orElse(10);
    int pageSize = Optional.ofNullable(param.getSkip()).map(s -> s / limit + 1).orElse(1);
    IPage<BaseProduct> page = baseProductService.page(new Page(pageSize, limit), wrapper);
    IPage<IamResourcePropertyValueDto> resultPage = PageUtils.convert(page, bp -> {
      return new IamResourcePropertyValueDto()
          .setId(bp.getId().toString())
          .setName(bp.getProductName())
          .setParent(Optional.ofNullable(bp.getParentId()).map(l -> l <= 0 ? null : l.toString()).orElse(null));
    });

    int total = Optional.ofNullable(resultPage.getTotal()).map(Long::intValue).orElse(0);
    PagingMetadata metadata = new PagingMetadata().setTotal(total).setSkip(0).setLimit(total);
    return new PagingResult<List<IamResourcePropertyValueDto>>().setPayload(resultPage.getRecords()).setMetadata(metadata);
  }
}
