package com.rootcloud.ids.ucenter.vo.rest.base;

import com.rootcloud.ids.ucenter.enums.base.DeptTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @author: ling.kang
 * @create: 2022-02-28 16:29
 **/
@Data
@ApiModel(value = "BaseDeptEquipmentAddRest", description = "新增部门机型参数")
public class BaseDeptEquipmentAddRest implements Serializable {

    private static final long serialVersionUID = -7327110512116081492L;

    @ApiModelProperty("#部门id#")
    @NotBlank(message = "deptId不能为空")
    private String deptId;

    @ApiModelProperty("#组织id#")
    @NotBlank(message = "cid不能为空")
    private String cid;

    @ApiModelProperty("#部门类型#")
    @NotNull(message = "deptType数组不能为空")
    private DeptTypeEnum deptType;

    @ApiModelProperty("#机型id数组#")
    @NotNull(message = "机型id数组不能为空")
    private List<BaseDeptEquipmentAddDTO> equipmentList;

}
