package com.rootcloud.ids.ucenter.vo.resp.base;

import com.rootcloud.esmp.common.enums.ProductStatusEnum;
import com.rootcloud.esmp.common.enums.ProductTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: kang.ling
 * @create: 2022-03-04 17:04
 **/
@Data
@ApiModel(value = "BaseEquipmentResp", description = "机型列表")
public class BaseEquipmentResp {

    @ApiModelProperty("#分类id#")
    private Long id;

    @ApiModelProperty("#父级id#")
    private Long parentId;

    @ApiModelProperty("#分类名称#")
    private String productName;

    @ApiModelProperty("#产品状态 1:启用:ENABLE,0:未启用:NOT_ENABLED#")
    private ProductStatusEnum productStatus;

    @ApiModelProperty("#分类层级# 1:机械大类:CONSTRUCTION_MACHINERY,2:机型:EQUIPMENT,3:设备号:EQUIPMENT_MODEL")
    private ProductTypeEnum productType;
}
