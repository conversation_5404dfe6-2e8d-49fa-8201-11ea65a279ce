package com.rootcloud.ids.ucenter.entity.backup;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rootcloud.ids.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 系统组织权限关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
@Getter
@Setter
@TableName("ucenter_group_permission")
@ApiModel(value = "GroupPermission对象", description = "系统组织权限关联表")
public class GroupPermission extends BaseEntity {

    @ApiModelProperty("组织ID")
    @TableId("group_id")
    private Long groupId;

    @ApiModelProperty("权限ID")
    @TableField("permission_id")
    private Long permissionId;

    @ApiModelProperty("创建用户ID")
    @TableField("create_user")
    private Long createUser;

    @ApiModelProperty("更新用户ID")
    @TableField("update_user")
    private Long updateUser;


}
