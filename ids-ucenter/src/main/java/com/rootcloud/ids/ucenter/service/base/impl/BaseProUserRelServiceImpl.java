package com.rootcloud.ids.ucenter.service.base.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rootcloud.ids.ucenter.dao.base.BaseProUserRelMapper;
import com.rootcloud.ids.ucenter.entity.base.BaseProUserRel;
import com.rootcloud.ids.ucenter.service.base.IBaseProUserRelService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 产品分类-用户-关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Service
public class BaseProUserRelServiceImpl extends ServiceImpl<BaseProUserRelMapper, BaseProUserRel> implements IBaseProUserRelService {

    @Override
    public List<BaseProUserRel> getProUserRelByUserId(String userId) {
        return this.list(Wrappers.lambdaQuery(BaseProUserRel.class).eq(BaseProUserRel::getUserId, userId));
    }

    @Override
    public void handle(List<String> userIdList, List<Long> equipmentIds) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return;
        }
        List<BaseProUserRel> entityList = new ArrayList<>();
        for (String userId : userIdList) {
            equipmentIds.forEach(id -> {
                BaseProUserRel entity = new BaseProUserRel();
                entity.setUserId(userId);
                entity.setProductId(id);
                entityList.add(entity);
            });
        }
        this.saveBatch(entityList);
    }

    @Override
    public List<BaseProUserRel> listByUserId(String userId) {
        return this.list(Wrappers.lambdaQuery(BaseProUserRel.class).eq(BaseProUserRel::getUserId, userId));
    }

    @Override
    public List<BaseProUserRel> listByUserId(List<String> userIdList) {
        if (CollUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.lambdaQuery(BaseProUserRel.class).in(BaseProUserRel::getUserId, userIdList));
    }
}
