package com.rootcloud.ids.ucenter.entity.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rootcloud.ids.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 部门-用户-关系
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Getter
@Setter
@TableName("ucenter_base_user_dept_rel")
@ApiModel(value = "BaseUserDeptRel", description = "部门-用户-关系")
public class BaseUserDeptRel extends BaseEntity {

    @ApiModelProperty("#主键id#")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("#用户id#")
    private String userId;

    @ApiModelProperty("#部门id#")
    private String deptId;


}
