package com.rootcloud.ids.ucenter.service.backup;

import com.rootcloud.ids.ucenter.dto.backup.SysRoleAddUserDTO;
import com.rootcloud.ids.ucenter.dto.backup.SysRoleRemoveUserDTO;
import com.rootcloud.ids.ucenter.entity.backup.UcenterUserRole;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description 系统用户-角色服务接口类
 * @InterfaceName ISysUserRoleService
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public interface IUcenterUserRoleService extends IService<UcenterUserRole> {

    /**
     * 添加用户
     * @param roleAddUserDTO
     * @return
     */
    Boolean addUser(SysRoleAddUserDTO roleAddUserDTO);

    /**
     * 删除用户
     * @param roleRemoveUserDTO
     */
    Boolean removeUser(SysRoleRemoveUserDTO roleRemoveUserDTO);

}
