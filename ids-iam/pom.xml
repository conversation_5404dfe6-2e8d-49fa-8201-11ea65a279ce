<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>ids</artifactId>
    <groupId>com.rootcloud.ids</groupId>
    <version>1.0.0-RELEASE</version>
  </parent>

  <groupId>com.rootcloud.ids</groupId>
  <artifactId>ids-iam</artifactId>
  <version>1.0.0-RELEASE</version>
  <name>ids-iam</name>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.rootcloud.ids</groupId>
      <artifactId>ids-common</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>druid-spring-boot-starter</artifactId>
          <groupId>com.alibaba</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.nimbusds</groupId>
      <artifactId>nimbus-jose-jwt</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
    </dependency>
  </dependencies>

</project>