import com.rootcloud.ids.common.i18n.ChineseFinder;
import java.util.Collections;
import java.util.List;

public class I18nTest {

  public static void main(String[] args) throws Exception {
    List<String> modules = Collections.singletonList("ids-iam");
    String codeStart = "SYS_100151";
    int enumStartRowNum = 157;
    int zhPropertiesInsertRow = 151;
    int enPropertiesInsertRow = 151;
    ChineseFinder.multiModuleScan(
        codeStart,
        enumStartRowNum,
        zhPropertiesInsertRow,
        enPropertiesInsertRow,
        "/ids-common",
        modules);
  }
}
