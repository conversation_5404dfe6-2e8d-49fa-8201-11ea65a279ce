package com.rootcloud.ids.iam.client;

import cn.hutool.core.util.StrUtil;
import com.rootcloud.ids.iam.config.IamConfig;
import com.rootcloud.esmp.common.dto.iam.organizationUser.OrganizationUsersDTO;
import com.rootcloud.esmp.common.dto.iam.organizationUser.OrganizeUserInfoDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/10/13 16:37
 */
@Slf4j
public class IamOrganizationUserClient extends IamClient {

    /**
     * 查询组织列表
     * <p>
     * 普通用户只能查看自己加入的所有组织
     * 组织管理员可以查看自己所属的组织和自己的客户组织
     * 根云管理员查看组织列表时，所有组织（除了超管组织，System.company）都认为是客户组织
     *
     * @param cid             组织ID
     * @param resolveServices 是否返回用户可用服务
     * @param unassigned      该参数为true时，只返回未分配部门的用户
     * @param isAdmin         是否管理员(含子管理员)
     * @param disabled        查询用户的禁用状态, 不传返回全部
     * @param skip            跳过结果数量，选填项，默认值为0
     * @param limit           返回结果总量，选填项，默认值为50, 支持最大值为200（注意：数据超过最大值需分批请求，否则可能引起bug）
     * @param filter          富查询条件，选填项,详情查看 http://confluence.irootech.com/pages/viewpage.action?pageId=*********
     * @param resolve         需解析的引用字段，选填项，目前支持"superAdmin", "admins", "operators", "industries", "permissions"
     * @return OrganizationUsersDTO
     */
    public static OrganizationUsersDTO organizationUsers(String cid, Boolean resolveServices, Boolean unassigned,
                                                         Boolean isAdmin, Boolean disabled, Integer skip, Integer limit,
                                                         String filter, String resolve) {

        Map<String, Object> paramMap = new HashMap<>(16);
        Optional.ofNullable(cid).ifPresent(obj -> paramMap.put("cid", obj));
        Optional.ofNullable(resolveServices).ifPresent(obj -> paramMap.put("resolveServices", obj));
        Optional.ofNullable(unassigned).ifPresent(obj -> paramMap.put("unassigned", obj));
        Optional.ofNullable(disabled).ifPresent(obj -> paramMap.put("disabled", obj));
        Optional.ofNullable(isAdmin).ifPresent(obj -> paramMap.put("isAdmin", obj));
        Optional.ofNullable(skip).ifPresent(obj -> paramMap.put("skip", obj));
        Optional.ofNullable(limit).ifPresent(obj -> paramMap.put("limit", obj));
        Optional.ofNullable(filter).ifPresent(obj -> paramMap.put("filter", obj));
        Optional.ofNullable(resolve).filter(StrUtil::isNotEmpty).ifPresent(obj -> paramMap.put("resolve", obj));
        return getByHuTool("/api/v1/organizations/{cid}/users", paramMap, IamConfig.getSuperToken(), OrganizationUsersDTO.class);
    }

    /**
     * 根据用户id查询用户信息
     * <p>
     * 组织管理员和组织用户可以使用本接口查看本组织内部及其所有部门的用户
     * 根云超级管理员可以查看所有组织的信息
     *
     * @param cid     组织ID
     * @param id      是否返回用户可用服务
     * @param resolve 需解析的引用字段，选填项，目前支持"mainCompany", "mainOrganization", "roles", "companies", "organizations", "departments", "groups"
     * @return OrganizeUserInfoDTO
     */
    public static OrganizeUserInfoDTO organizationUserInfo(String cid, String id, String resolve) {

        Map<String, Object> paramMap = new HashMap<>(16);
        Optional.ofNullable(cid).ifPresent(obj -> paramMap.put("cid", obj));
        Optional.ofNullable(id).ifPresent(obj -> paramMap.put("id", id));
        Optional.ofNullable(resolve).ifPresent(obj -> paramMap.put("resolve", obj));
        return getByHuTool("/api/v1/organizations/{cid}/users/{id}", paramMap, IamConfig.getSuperToken(), OrganizeUserInfoDTO.class);
    }

}
