package com.rootcloud.ids.iam.client;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import com.rootcloud.esmp.common.dto.iam.organization.OrganizationsDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/10/13 16:37
 */
@Slf4j
public class IamInternalClient extends IamClient {

    /**
     * 查询组织列表
     * <p>
     * 普通用户只能查看自己加入的所有组织
     * 组织管理员可以查看自己所属的组织和自己的客户组织
     * 根云管理员查看组织列表时，所有组织（除了超管组织，System.company）都认为是客户组织
     *
     * @param search  正则匹配组织超管的手机号，用户名，邮箱，id
     * @param service 按所订阅的服务查询组织
     * @param skip    跳过结果数量，选填项，默认值为0
     * @param limit   返回结果总量，选填项，默认值为50, 支持最大值为200（注意：数据超过最大值需分批请求，否则可能引起bug）
     * @param filter  富查询条件，选填项,详情查看 http://confluence.irootech.com/pages/viewpage.action?pageId=*********
     * @param resolve 需解析的引用字段，选填项，目前支持"superAdmin", "admins", "operators", "industries", "permissions"
     * @return OrganizationsDTO
     */
    public static OrganizationsDTO organizations(String search, String service, String resolve, String token,
                                                 Integer skip, Integer limit, String filter) {
        Map<String, Object> paramMap = new HashMap<>(16);
        Optional.ofNullable(search).ifPresent(obj -> paramMap.put("search", obj));
        Optional.ofNullable(service).ifPresent(obj -> paramMap.put("service", obj));
        Optional.ofNullable(skip).ifPresent(obj -> paramMap.put("skip", obj));
        Optional.ofNullable(limit).ifPresent(obj -> paramMap.put("limit", obj));
        Optional.ofNullable(filter).ifPresent(obj -> paramMap.put("filter", obj));
        Optional.ofNullable(resolve).ifPresent(obj -> paramMap.put("resolve", obj));

        HttpRequest request = createRequest(Method.GET, "/internal/organizations", paramMap);
        request.header("x-rc-service-authorization", token);
        System.out.println(I18nUtil.message(I18nCode.SYS_100157) + request.getUrl());

        return executeReBean(request, OrganizationsDTO.class);
    }


}
