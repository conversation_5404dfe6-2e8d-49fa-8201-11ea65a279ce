/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.ids.iam.dto.permission;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@NoArgsConstructor
@Getter
@Setter
@ToString
@Accessors(chain = true)
@ApiModel("IAM数据权限属性值查询参数")
public class IamResourcePropertyValueQueryDto extends PageRequest {
  @ApiModelProperty(value = "按条件过滤查询时传入的查询条件字段串", hidden = true)
  private String search;
  @ApiModelProperty("按id列表查询时传入的id列表，filterIds只是举例说明的字段，别的字段名也支持")
  private List<String> filterIds;
}
