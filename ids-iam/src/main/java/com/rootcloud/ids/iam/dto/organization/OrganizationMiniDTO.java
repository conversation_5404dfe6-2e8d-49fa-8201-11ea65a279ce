package com.rootcloud.ids.iam.dto.organization;

import com.rootcloud.esmp.common.dto.IamBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 组织信息
 * @since 2022/2/28 10:40 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrganizationMiniDTO extends IamBaseDTO {

    /**
     * 组织名称
     */
    private String name;

    /**
     * 组织的根管理员id
     */
    private String superAdmin;
}
