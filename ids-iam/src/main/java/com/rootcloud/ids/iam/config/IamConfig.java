package com.rootcloud.ids.iam.config;

import cn.hutool.extra.spring.SpringUtil;
import com.rootcloud.ids.iam.exception.IamException;

/**
 * <AUTHOR>
 * @since 2021/10/11 14:36
 */
public class IamConfig {
    private static final class ConfigHolder {
        static String DOMAIN = SpringUtil.getProperty("os.iam.domain");
        static String SUPER_TOKEN = SpringUtil.getProperty("os.iam.superToken");
//        static String CLIENT_SECRET = SpringUtil.getProperty("os.iam.clientSecret");

        static String CLIENT_ID = SpringUtil.getProperty("os.iam.clientId");
        static String CLIENT_SECRET = SpringUtil.getProperty("os.iam.clientSecret");
    }

    public static String getDomain() {
        return ConfigHolder.DOMAIN;
    }

    public static String setDomain(String domain) {
        return ConfigHolder.DOMAIN = domain;
    }



    public static String getSuperToken() {
        if (ConfigHolder.SUPER_TOKEN == null){
            throw new IamException("superToken cannot be null");
        }
        return ConfigHolder.SUPER_TOKEN;
    }

    public static void setSuperToken(String superToken) {
        ConfigHolder.SUPER_TOKEN = superToken;
    }
//
//    public static String getClientSecret() {
//        if (ConfigHolder.CLIENT_SECRET == null){
//            throw new IamException("clientSecret cannot be null");
//        }
//        return ConfigHolder.CLIENT_SECRET;
//    }
//
//    public static void setClientSecret(String clientSecret) {
//        ConfigHolder.CLIENT_SECRET = clientSecret;
//    }

    public static String getClientId() {
        if (ConfigHolder.CLIENT_ID == null){
            throw new IamException("clientId cannot be null");
        }
        return ConfigHolder.CLIENT_ID;
    }

    public static void setClientId(String clientId) {
        ConfigHolder.CLIENT_ID = clientId;
    }

    public static String getClientSecret() {
        if (ConfigHolder.CLIENT_SECRET == null){
            throw new IamException("clientSecret cannot be null");
        }
        return ConfigHolder.CLIENT_SECRET;
    }

    public static void setClientSecret(String clientSecret) {
        ConfigHolder.CLIENT_SECRET = clientSecret;
    }
}
