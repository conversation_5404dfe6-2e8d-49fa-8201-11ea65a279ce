package com.rootcloud.ids.iam.client;

import com.rootcloud.ids.iam.config.IamConfig;
import com.rootcloud.esmp.common.dto.iam.organization.OrganizationInfoDTO;
import com.rootcloud.esmp.common.dto.iam.organization.OrganizationsDTO;
import com.rootcloud.ids.iam.enums.organization.OrganizationTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/10/13 16:37
 */
@Slf4j
public class IamOrganizationClient extends IamClient {

    /**
     * 查询组织列表
     * <p>
     * 普通用户只能查看自己加入的所有组织
     * 组织管理员可以查看自己所属的组织和自己的客户组织
     * 根云管理员查看组织列表时，所有组织（除了超管组织，System.company）都认为是客户组织
     *
     * @param superAdmin       正则匹配组织超管的手机号，用户名，邮箱，id
     * @param organizationType 筛选组织类型, 自己加入的组织（joined）或者自己的客户（customer）
     * @param skip             跳过结果数量，选填项，默认值为0
     * @param limit            返回结果总量，选填项，默认值为50, 支持最大值为200（注意：数据超过最大值需分批请求，否则可能引起bug）
     * @param filter           富查询条件，选填项,详情查看 http://confluence.irootech.com/pages/viewpage.action?pageId=*********
     * @param resolve          需解析的引用字段，选填项，目前支持"superAdmin", "admins", "operators", "industries", "permissions"
     * @return OrganizationsDTO
     */
    public static OrganizationsDTO organizations(String superAdmin, OrganizationTypeEnum organizationType, String resolve,
                                                 Integer skip, Integer limit, String filter) {
        Map<String, Object> paramMap = new HashMap<>(16);
        Optional.ofNullable(superAdmin).ifPresent(obj -> paramMap.put("superAdmin", obj));
        Optional.ofNullable(skip).ifPresent(obj -> paramMap.put("skip", obj));
        Optional.ofNullable(limit).ifPresent(obj -> paramMap.put("limit", obj));
        Optional.ofNullable(filter).ifPresent(obj -> paramMap.put("filter", obj));
        Optional.ofNullable(resolve).ifPresent(obj -> paramMap.put("resolve", obj));
        Optional.ofNullable(organizationType).ifPresent(obj -> paramMap.put("organizationType", obj.getCode()));


        return getByHuTool("/api/v1/organizations", paramMap, IamConfig.getSuperToken(), OrganizationsDTO.class);
    }

    /**
     * 查询某个组织信息
     * <p>
     * 根云管理员可以看到任意组织的信息
     * 普通用户可以查看自己加入的任意组织的信息
     *
     * @param id      组织id，必填项
     * @param resolve 需解析的引用字段，选填项，目前支持"superAdmin", "admins", "operators", "industries", "permissions"
     * @return OrganizationInfoDTO
     */
    public static OrganizationInfoDTO organizationInfo(String id, String resolve) {
        Map<String, Object> paramMap = new HashMap<>(16);
        Optional.ofNullable(id).ifPresent(obj -> paramMap.put("id", obj));
        Optional.ofNullable(resolve).ifPresent(obj -> paramMap.put("resolve", obj));

        String url = "/api/v1/organizations/{id}";
        return getByHuTool(url, paramMap, IamConfig.getSuperToken(), OrganizationInfoDTO.class);

    }

}
