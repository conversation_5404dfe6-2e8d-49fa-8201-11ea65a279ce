/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.ids.iam.dto.permission;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@NoArgsConstructor
@Getter
@Setter
@ToString
@Accessors(chain = true)
@ApiModel("IAM数据权限属性值")
public class IamResourcePropertyValueDto {
  @ApiModelProperty("唯一标识")
  private String id;
  @ApiModelProperty("名称")
  private String name;

  @ApiModelProperty("可选属性，关联父级ID")
  private String parent;
  @ApiModelProperty("可选属性，关联父级名称")
  private String parentName;

  @ApiModelProperty("可选属性，子节点")
  private List<IamResourcePropertyValueDto> children;
}
