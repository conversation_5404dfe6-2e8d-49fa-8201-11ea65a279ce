package com.rootcloud.ids.iam.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.*;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rootcloud.ids.common.web.utils.HutoolRequestUtil;
import com.rootcloud.ids.iam.config.IamConfig;
import com.rootcloud.ids.iam.exception.IamException;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static java.util.Optional.ofNullable;

/**
 * <AUTHOR>
 * @since 2021/10/13 16:05
 */
public abstract class IamClient {

    private static final String TOKEN_TYPE = "Bearer";
    private static final String AUTHORIZATION = "Authorization";

    protected static String getUrl(String path) {
        String domain = IamConfig.getDomain();
        if (domain == null) {
            throw new IamException("domain cannot be null");
        }
        return domain + path;
    }


    protected static String buildUrl(String path, Map<String, Object> param) {
        Map<String, Object> paramMap = ofNullable(param).map(HashMap::new).orElse(new HashMap<>(0));
        UrlBuilder urlBuilder = UrlBuilder.of(getUrl(path));
        List<String> segments = urlBuilder.getPath().getSegments();

        for (int i = 0; i < segments.size(); i++) {
            String segment = segments.get(i);
            if (segment.startsWith("{") && segment.endsWith("}")) {
                String key = segment.substring(1, segment.length() - 1);
                Object value = paramMap.get(key);
                if (value == null) {
                    throw new IamException("url param:" + key + " ,Cannot be null");
                }
                segments.set(i, URLUtil.encodeAll(toParamStr(value), StandardCharsets.UTF_8));
                paramMap.remove(key);
            }
        }

        urlBuilder.getQuery().addAll(paramMap);
        return urlBuilder.build();
    }

    private static String toParamStr(Object value) {
        String result;
        if (value instanceof Iterable) {
            result = CollUtil.join((Iterable<?>) value, ",");
        } else if (value instanceof Iterator) {
            result = IterUtil.join((Iterator<?>) value, ",");
        } else {
            result = Convert.toStr(value);
        }
        return result;
    }

    /**
     * 创建需要授权的请求
     *
     * @param method 请求方法类型
     * @param path   请求路径
     * @param param  请求参数
     * @return 请求信息
     */
    protected static HttpRequest createAuthRequest(Method method, String path, String token, Map<String, Object> param) {
        HttpRequest request = createRequest(method, path, param);
        request.header(AUTHORIZATION, jointToken(TOKEN_TYPE, token));
        return request;
    }

    /**
     * 创建需要授权的请求
     *
     * @param method 请求方法类型
     * @param path   请求路径
     * @param param  请求参数
     * @return 请求信息
     */
    protected static HttpRequest createAuthRequest(Method method, String path, String tokenType, String token, Map<String, Object> param) {
        HttpRequest request = createRequest(method, path, param);
        request.header(AUTHORIZATION, jointToken(tokenType, token));
        return request;
    }

    /**
     * 创建请求对象
     *
     * @param method 请求方法
     * @param path   请求路径
     * @param param  请求参数
     * @return HttpRequest
     */
    protected static HttpRequest createRequest(Method method, String path, Map<String, Object> param) {
        String url = buildUrl(path, param);
        return HttpUtil.createRequest(method, url);
    }


    static <T> T postByHuTool(String path, Map<String, Object> paramMap, String token, Class<T> resultClass, ContentType contentType) {
        HttpRequest request;
        if (StrUtil.isEmpty(token)) {
            request = createRequest(Method.POST, path, paramMap);
        } else {
            request = createAuthRequest(Method.POST, path, token, paramMap);
        }

        //组装body
        if (paramMap != null && !paramMap.isEmpty()) {
            request.body(JSONUtil.toJsonStr(paramMap), contentType.getValue());
        }

        return executeReBean(request, resultClass);
    }

    static <T> T getByHuTool(String path, Map<String, Object> paramMap, String token, Class<T> resultClass) {
        HttpRequest request;
        if (StrUtil.isEmpty(token)) {
            request = createRequest(Method.GET, path, paramMap);
        } else {
            request = createAuthRequest(Method.GET, path, token, paramMap);
        }

        return toBean(execute(request), resultClass);
    }


    protected static void buildHeader(HttpRequest request, Map<String, Object> headerMap) {
        if (headerMap != null && !headerMap.isEmpty()) {
            for (String headerKey : headerMap.keySet()) {
                request.header(headerKey, headerMap.get(headerKey) + "");
            }
        }
    }

    /**
     * 执行请求
     *
     * @param request 请求参数
     * @return 响应数据
     */
    protected static HttpResponse execute(HttpRequest request) {
        HttpResponse response = HutoolRequestUtil.intercept(request, HttpRequest::execute);
        responseDataCheck(response);
        return response;
    }

    /**
     * 执行请求
     *
     * @param request     请求参数
     * @param resultClass 响应数据类型
     * @param <T>         响应数据类型
     * @return 响应数据
     */
    protected static <T> T executeReBean(HttpRequest request, Class<T> resultClass) {
        return toBean(execute(request), resultClass);
    }

    /**
     * 执行请求
     *
     * @param request     请求参数
     * @param resultClass 响应数据类型
     * @param <T>         响应数据类型
     * @return 响应数据
     */
    protected static <T> List<T> executeReList(HttpRequest request, Class<T> resultClass) {
        return toList(execute(request), resultClass);
    }

    private static <T> T toBean(HttpResponse response, Class<T> resultClass) {
        return toBean(response.body(), resultClass);
    }

    private static <T> List<T> toList(HttpResponse response, Class<T> resultClass) {
        return toList(response.body(), resultClass);

    }

    private static <T> T toBean(String content, Class<T> resultClass) {
        if (JSONUtil.isJson(content)) {
            return JSONUtil.toBean(content, resultClass);
        }
        return null;
    }

    private static <T> List<T> toList(String content, Class<T> resultClass) {
        if (JSONUtil.isJson(content)) {
            return JSONUtil.toList(content, resultClass);
        }
        return null;
    }


    private static void responseDataCheck(HttpResponse response) {
        if (response.isOk()) {
            return;
        }
        String body = response.body();
        String message = "request fail";
        if (JSONUtil.isJson(body)) {
            JSONObject jsonData = JSONUtil.parseObj(body);
            message = jsonData.getStr("message");
        }
        throw new IamException(message);
    }


    static String jointToken(String tokenType, String token) {
        if (tokenType == null) {
            tokenType = TOKEN_TYPE;
        }
        return tokenType + " " + token;
    }
}
