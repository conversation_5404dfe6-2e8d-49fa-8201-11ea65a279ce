package com.rootcloud.ids.iam.dto.organization;

import com.rootcloud.esmp.common.dto.IamBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @description 组织信息
 * @since 2022/2/28 10:08 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrganizationInfoDTO extends IamBaseDTO {

    /**
     * 组织名
     */
    private String name;

    /**
     * 组织的管理员id列表
     */
    private List<String> admins;

    /**
     * 组织的根管理员id
     */
    private String superAdmin;

    /**
     * 组织是否经过实名认证
     */
    private Boolean verified;

    /**
     * 组织加入的订阅根云应用开发服务的组织
     */
    private List<String> operators;

    /**
     * 组织所属行业
     */
    private List<String> industries;

    /**
     * 组织拥有的权限列表
     */
    private List<String> permissions;

    /**
     * 组织拥有的物资源包列表
     */
    private List<String> aclGroups;

    /**
     * 统一社会信用码
     */
    private String uniformSocialCreditCode;

    /**
     * 邮政编码
     */
    private String postcode;

    /**
     * 组织电话
     */
    private String phoneNumber;

    /**
     * 组织地址
     */
    private String address;

    /**
     * 联系传真
     */
    private String faxNumber;

    /**
     * 组织描述
     */
    private String description;

    /**
     * 组织微信
     */
    private String wechatId;

    /**
     * 组织营业执照文件等信息
     */
    private BusinessLicenseDTO businessLicense;

    /**
     * IAM私有化部署标识
     */
    private Boolean licenseCheck;

    /**
     * 组织状态(Trial:试用期 Examining:审批中 Regular:正式付费组织)
     */
    private String status;

    /**
     * 组织邮箱
     */
    private String email;

    /**
     * 是否存量（内测中）
     */
    private Boolean legacy;

    /**
     * 是否禁用
     */
    private Boolean disabled;




}
