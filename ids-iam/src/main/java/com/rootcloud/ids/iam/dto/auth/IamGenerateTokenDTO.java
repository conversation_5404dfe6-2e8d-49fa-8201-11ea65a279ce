package com.rootcloud.ids.iam.dto.auth;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 根云平台生成自定义过期时间token返回对象
 * @since 2021/12/20 7:10 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IamGenerateTokenDTO {

    /**
     * 返回token
     */
    private String accessToken;

    /**
     * token类型
     */
    private String tokenType;

    /**
     * 过期时间，秒
     */
    private Long expiresIn;
}
