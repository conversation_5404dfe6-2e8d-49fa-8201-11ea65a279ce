package com.rootcloud.ids.iam.dto.department;

import com.rootcloud.esmp.common.dto.IamBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @description 目标组织下所有部门
 * @since 2022/2/28 10:40 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DepartmentsDTO extends IamBaseDTO {

    /**
     * 部门名称，同一组织下部门名称不可重复
     */
    private String name;

    /**
     * 部门下的默认角色
     */
    private List<String> defaultRoles;

    /**
     * 默认物资源包列表
     */
    private List<String> defaultAclGroups;

    /**
     * 上级部门
     */
    private String parent;

    /**
     * 上级部门列表（包括上级部门的上级）
     */
    private List<String> parents;

    /**
     * 部门管理员
     */
    private List<String> admins;

    /**
     * 上级部门的管理员
     */
    private List<String> adminsFromParents;

    /**
     * 部门管理员（包括上级部门的管理员）
     */
    private List<String> adminsIncludingParents;

    /**
     * 部门编码
     */
    private String code;

    /**
     * 所属组织
     */
    private String organization;

    /**
     * 子部门
     */
    private List<DepartmentsDTO> children;

}
