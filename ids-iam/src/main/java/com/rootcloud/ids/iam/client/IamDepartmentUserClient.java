package com.rootcloud.ids.iam.client;

import com.rootcloud.ids.iam.config.IamConfig;
import com.rootcloud.esmp.common.dto.iam.departmentUser.DeptUsersDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/10/13 16:37
 */
@Slf4j
public class IamDepartmentUserClient extends IamClient {

    /**
     * 查询部门的用户列表
     *
     * @param deptId   部门ID
     * @param all      是否展示全部用户（包含子部门的用户）
     * @param disabled 查询用户的禁用状态, 不传返回全部
     * @param skip     跳过结果数量，选填项，默认值为0
     * @param limit    返回结果总量，选填项，默认值为50, 支持最大值为200（注意：数据超过最大值需分批请求，否则可能引起bug）
     * @param filter   富查询条件，选填项,详情查看 http://confluence.irootech.com/pages/viewpage.action?pageId=270826648
     * @return DeptUsersDTO
     */
    public static DeptUsersDTO users(String deptId, Boolean all, Boolean disabled, Integer skip,
                                     Integer limit, String filter) {
        log.info("查询部门的用户列表param deptId:{},all:{},disabled:{},skip:{},limit:{},filter:{}"
                ,deptId,all,disabled,skip,limit,filter);
        Map<String, Object> paramMap = new HashMap<>(16);
        Optional.ofNullable(deptId).ifPresent(obj -> paramMap.put("id", obj));
        Optional.ofNullable(all).ifPresent(obj -> paramMap.put("all", obj));
        Optional.ofNullable(disabled).ifPresent(obj -> paramMap.put("disabled", obj));
        Optional.ofNullable(skip).ifPresent(obj -> paramMap.put("skip", obj));
        Optional.ofNullable(limit).ifPresent(obj -> paramMap.put("limit", obj));
        Optional.ofNullable(filter).ifPresent(obj -> paramMap.put("filter", obj));
        DeptUsersDTO deptUsersDTO = getByHuTool("/api/v1/department/{id}/users", paramMap, IamConfig.getSuperToken(), DeptUsersDTO.class);
        log.info("查询部门的用户列表resp:{}",deptUsersDTO);
        return deptUsersDTO;
    }


}
