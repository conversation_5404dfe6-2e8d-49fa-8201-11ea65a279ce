package com.rootcloud.ids.iam.client;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import com.rootcloud.ids.iam.config.IamConfig;
import com.rootcloud.esmp.common.dto.iam.department.DepartmentsDTO;
import com.rootcloud.esmp.common.dto.iam.organization.OrganizationInfoDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/10/13 16:37
 */
@Slf4j
public class IamDepartmentClient extends IamClient {

    /**
     * 查看目标组织下的所有部门
     *
     * @param parseToTree 是否构建成树
     * @param cid         组织id
     * @param filter      富查询条件，选填项,详情查看 http://confluence.irootech.com/pages/viewpage.action?pageId=270826648
     * @return List<DepartmentsDTO>
     */
    public static List<DepartmentsDTO> departments(boolean parseToTree, String cid, String filter) {
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("parseToTree", parseToTree);
        Optional.ofNullable(cid).ifPresent(obj -> paramMap.put("cid", obj));
        Optional.ofNullable(filter).ifPresent(obj -> paramMap.put("filter", obj));
        String url = "/api/v1/organizations/{cid}/departments";
        HttpRequest request = createAuthRequest(Method.GET, url, IamConfig.getSuperToken(), paramMap);

        return executeReList(request, DepartmentsDTO.class);
    }

    /**
     * 查询部门信息
     * @param id 部门ID
     * @param showRoot 是否返回组织根部门
     * @param resolve 需解析的引用字段
     * @return
     */
    public static DepartmentsDTO department(String id ,boolean showRoot, String resolve) {
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("showRoot", showRoot);
        Optional.ofNullable(id).ifPresent(obj -> paramMap.put("id", obj));
        Optional.ofNullable(resolve).ifPresent(obj -> paramMap.put("resolve", obj));
        return getByHuTool("/api/v1/department/{id}", paramMap, IamConfig.getSuperToken(), DepartmentsDTO.class);
    }


}
