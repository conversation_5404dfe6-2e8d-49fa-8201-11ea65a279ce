/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2021 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.ids.iam.dto.permission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * ServerResponse.
 *
 * <AUTHOR>
 * @date 2020/11/24
 */
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PagingResult<T> {

  @ApiModelProperty("请求成功时的返回结果")
  private T payload;

  @ApiModelProperty("返回结果分页信息")
  private PagingMetadata metadata;

}
