package com.rootcloud.ids.iam.dto.permission;

import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
public class PagingMetadata {

  @ApiModelProperty("查询到的结果总量")
  private Long total;

  @ApiModelProperty("分页大小")
  private Integer limit;

  @ApiModelProperty("当前页")
  private Integer skip;
}
