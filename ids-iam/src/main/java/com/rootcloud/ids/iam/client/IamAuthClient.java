package com.rootcloud.ids.iam.client;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import com.rootcloud.ids.iam.config.IamConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/10/13 16:37
 */
@Slf4j
public class IamAuthClient extends IamClient {

    /**
     * 检查token是否有效
     *
     * @param tokenType token类型
     * @param token     token
     * @return boolean
     */
    public static boolean checkToken(String tokenType, String token) {
        log.info("===== IamAuthClient =====checkToken domain:{}", IamConfig.getDomain());
        String url = "/api/v1/auth/ping";
        HttpRequest request = createAuthRequest(Method.GET, url, tokenType, token, null);
        HttpResponse response = execute(request);
        // 如果当前token有效，返回200，否则返回401
        return response.isOk();
    }
}
