package com.rootcloud.ids.iam.dto.currentUser;

import lombok.Data;

/**
 * <AUTHOR>
 * @description iam根据token获取用户信息返回对象
 * @since 2021/12/7 9:48 上午
 */
@Data
public class IamUserInfoDTO {
    /**
     * 用户id
     */
    private String id;

    /**
     * 用户标识
     */
    private String sub;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 用户名
     */
    private String preferredUsername;

    /**
     * 邮箱是否认证
     */
    private Boolean emailVerified;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 手机号是否认证
     */
    private Boolean phoneNumberVerified;

}
