package com.rootcloud.ids.iam.client;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import com.rootcloud.esmp.common.dto.iam.currentUser.IamUserInfoDTO;

/**
 * <AUTHOR>
 * @since 2021/10/13 16:37
 */
public class IamCurrentUserClient extends IamClient {

    /**
     * 当前用户信息
     *
     * @param tokenType token类型
     * @param token     token
     * @return IamPermissionDTO
     */
    public static IamUserInfoDTO currentUserInfo(String tokenType, String token) {
        String url = "/api/v1/current-user/info";
        HttpRequest request = createAuthRequest(Method.GET, url,tokenType, token, null);
        return executeReBean(request, IamUserInfoDTO.class);
    }
}
