package com.rootcloud.ids.iam.dto.permission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */

@NoArgsConstructor
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class PageRequest {
  @ApiModelProperty("是否分页")
  private Boolean total = false;

  @ApiModelProperty("分页大小")
  private Integer limit = 10;

  @ApiModelProperty("当前页")
  private Integer skip = 0;
}
