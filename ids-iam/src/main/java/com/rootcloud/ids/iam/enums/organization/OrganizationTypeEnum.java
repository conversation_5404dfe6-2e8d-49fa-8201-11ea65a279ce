package com.rootcloud.ids.iam.enums.organization;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;


/**
 * 根云登录方式
 *
 * <AUTHOR>
 * @since 2021-07-19
 */

public enum OrganizationTypeEnum {

    /**
     * joined：自己加入的组织
     * customer：自己的客户
     */
    JOINED("joined", I18nCode.SYS_100151),
    CUSTOMER("customer", I18nCode.SYS_100152),
    ;
    private final String code;
    private final I18nCode label;

    OrganizationTypeEnum(String code, I18nCode label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return I18nUtil.message(label);
    }

    public static String getLabel(String code) {
        if (code != null) {
            for (OrganizationTypeEnum value : OrganizationTypeEnum.values()) {
                if (value.code.equals(code)) {
                    return value.getLabel();
                }
            }
        }
        return null;
    }

    public static OrganizationTypeEnum codeOf(String code) {
        for (OrganizationTypeEnum value : OrganizationTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("cant not change code: " + code + " to OrganizationTypeEnum.");
    }

}
