package com.rootcloud.ids.iam.dto.auth;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 根云平台登陆返回对象
 * @since 2021/8/6 7:10 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IamLoginDTO {

    /**
     * 登陆返回token
     */
    private String accessToken;

    /**
     * token类型
     */
    private String tokenType;

    /**
     * 刷新token
     */
    private String refreshToken;

    /**
     * 过期时间，秒
     */
    private Integer expiresIn;
}
