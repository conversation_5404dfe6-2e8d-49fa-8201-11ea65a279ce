package com.rootcloud.ids.iam.enums.auth;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;


/**
 * 根云登录方式
 *
 * <AUTHOR>
 * @since 2021-07-19
 */

public enum GrantTypeEnum {

    /**
     * password：用户名密码模式
     * refresh_token：刷新token
     * client_credentials：客户端模式
     * authorization_code：授权码模式
     */
    PASSWORD("password", I18nCode.SYS_100153),
    REFRESH_TOKEN("refresh_token", I18nCode.SYS_100154),
    CLIENT_CREDENTIALS("client_credentials", I18nCode.SYS_100155),
    AUTHORIZATION_CODE("authorization_code", I18nCode.SYS_100156),
    ;
    private final String code;
    private final I18nCode label;

    GrantTypeEnum(String code, I18nCode label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return I18nUtil.message(label);
    }

    public static String getLabel(String code) {
        if (code != null) {
            for (GrantTypeEnum value : GrantTypeEnum.values()) {
                if (value.code.equals(code)) {
                    return value.getLabel();
                }
            }
        }
        return null;
    }

    public static GrantTypeEnum codeOf(String code) {
        for (GrantTypeEnum value : GrantTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("cant not change code: " + code + " to GrantTypeEnum.");
    }

}
