package com.rootcloud.ids.iam.dto.departmentUser;

import com.rootcloud.esmp.common.dto.IamBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @description 部门的用户信息
 * @since 2022/2/28 10:08 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeptUserInfoDTO extends IamBaseDTO {

    /**
     * 用户验证信息
     */
    private VerificationDTO verification;

    /**
     * 是否需要修改密码(用于首次登录时)
     */
    private Boolean changePw;

    /**
     * 用户所属的组织
     */
    private List<Object> organizations;

    /**
     * 用户所属的部门
     */
    private List<Object> departments;

    /**
     * 用户的角色
     */
    private List<String> roles;

    /**
     * 组织拥有的物资源包列表
     */
    private List<String> aclGroups;

    /**
     * 用户组列表
     */
    private List<String> groups;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 手机号国家码
     */
    private String nationCode;

    /**
     * 用户手机号
     */
    private String cellphone;

    /**
     * 用户的真实姓名，用于展示信息，不用于登录
     */
    private String displayName;

    /**
     * 用户所属主组织
     */
    private String mainOrganization;

    /**
     * 身份证号码
     */
    private String nationalIdNo;

    /**
     * 用户绑定的钉钉微应用
     */
    private List<String> dingTalkApp;

    /**
     * 是否禁用
     */
    private Boolean disabled;

    /**
     * 当查询列表传入assignedRoleIds时，此字段标识该用户是否已分配assignedRoleIds的角色
     */
    private Boolean assigned;


}
