package com.rootcloud.ids.iam.dto.currentUser;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/9/7 14:59
 */
@Data
public class IamPermissionDTO {
    /**
     * 该权限关联的资源列表
     */
    private List<String> resources;
    /**
     * 该权限是否为系统内置权限(内置权限不可更改，不可删除)
     */
    private Boolean builtIn;
    /**
     * 权限名称
     */
    private String name;
    /**
     * 权限描述
     */
    private String description;
    /**
     * 权限所属服务
     */
    private String service;
    /**
     * 所属组织id
     */
    private String organization;
    /**
     * 权限模板ID
     */
    private String template;
    /**
     * 自定义权限规则
     */
    private List<IamPolicyDTO> policies;
    /**
     * 命名空间
     */
    private String scope;
    /**
     * 所属上级
     */
    private String parent;
    /**
     * 依赖权限
     */
    private List<String> needs;
    /**
     * 是否禁用
     */
    private Boolean disabled;
    /**
     * 该权限属于哪个SaaS应用，如果是平台创建的权限，就属于系统初始化时创建的Client
     */
    private String client;
    /**
     * 权限组
     */
    private String group;
    /**
     * 唯一id
     */
    private String id;

}
