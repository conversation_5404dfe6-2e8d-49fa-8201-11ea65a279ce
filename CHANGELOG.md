## [1.6.28](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.27...v1.6.28) (2024-10-08)


### Bug Fixes

* SANYIC-15961 设备型号：修改监管类型，修改成非工程机械类型，提交发布后，监管类型会自动变成工程机械 ([e3bd362](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/e3bd362e8bef22ea73362835cd25cad026e9764a))

## [1.6.27](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.26...v1.6.27) (2024-09-24)


### Bug Fixes

* SANYIC-15804【新非道国四】设备型号添加非道路国四类型标记 修改设备型号默认描述 ([a7f7c07](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/a7f7c07f67d174dbac0a7177cd81109d15b55cb6))

## [1.6.26](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.25...v1.6.26) (2024-09-20)


### Bug Fixes

* SANYIC-15804【新非道国四】设备型号添加非道路国四类型标记 备案监管分类标识 ([f661f22](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/f661f221eb1a781fd83f65b396098fa2871c3b15))

## [1.6.25](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.24...v1.6.25) (2024-09-19)


### Bug Fixes

* SANYIC-15804【新非道国四】设备型号添加非道路国四类型标记 备案监管分类标识 ([11bef15](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/11bef158054875773731e3b85e6f10857a9472cb))

## [1.6.24](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.23...v1.6.24) (2024-09-19)


### Bug Fixes

* SANYIC-15804【新非道国四】设备型号添加非道路国四类型标记 备案监管分类标识 ([bce1680](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/bce1680ad1d1d8a8d21eb89c29f2569f3245c9f5))

## [1.6.23](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.22...v1.6.23) (2024-09-12)


### Bug Fixes

* SANYIC-15804【新非道国四】设备型号添加非道路国四类型标记 基本信息修改添加备案分类标识 ([d1d01f0](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/d1d01f0a19ac9f4a95d355f0d8678a74e44f8af6))

## [1.6.22](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.21...v1.6.22) (2024-09-11)


### Bug Fixes

* SANYIC-15804【新非道国四】设备型号添加非道路国四类型标记 基本信息修改添加备案分类标识 ([6703978](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/6703978f6b1b3dfec207ae9d10c73b190e187b07))

## [1.6.21](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.20...v1.6.21) (2024-09-06)


### Bug Fixes

* SANYIC-15804【新非道国四】设备型号添加非道路国四类型标记 发布新增备案分类 ([d8a3147](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/d8a31477bcf607d6d4bae0d70e719f62413b1fc8))

## [1.6.20](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.19...v1.6.20) (2024-09-06)


### Bug Fixes

* SANYIC-15804【新非道国四】设备型号添加非道路国四类型标记 ([22407d3](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/22407d305ecbc76cae0b7b51afdb4ab4389753b4))

## [1.6.19](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.18...v1.6.19) (2024-07-24)


### Bug Fixes

* 设备型号查询成功后没有返回型号列表 ([26e1da3](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/26e1da393a688c3f2e4d82e03a4a55f0c7de26e9))

## [1.6.18](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.17...v1.6.18) (2024-07-24)


### Bug Fixes

* 调大请求头大小。 ([09c311e](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/09c311ea42f5f5b772f3c797043b2b6c99a6e4cc))

## [1.6.17](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.16...v1.6.17) (2024-07-11)


### Bug Fixes

* 修复查询机型的名称列表报错 ([fb14c15](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/fb14c15e973489ac7038ce22fb98e7617ca8f812))
* 触发镜像 ([f503aef](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/f503aefe384438b982628d235064f85e8ebe45f7))

## [1.6.16](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.15...v1.6.16) (2024-07-10)


### Bug Fixes

* 产品列表接口添加产品大类修改 ([e1aad39](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/e1aad391d6191b96d7bf806972f6cdf139af7e7c))
* 触发新镜像生成 ([2c7c2d7](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/2c7c2d74b561632bbf7c41c2f0af5d601d61ec7c))

## [1.6.15](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.14...v1.6.15) (2024-07-10)


### Bug Fixes

* 触发新镜像生成 ([bb074a2](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/bb074a229251ed7271d6292872bd829e3617e5de))

## [1.6.14](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.13...v1.6.14) (2024-07-10)


### Bug Fixes

* 查询机型的名称列表接口添加产品编码字段 ([a302fa2](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/a302fa2f665fded881614ce814e191e715137c3c))

## [1.6.13](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.12...v1.6.13) (2024-06-21)


### Bug Fixes

* qa CD 加速配置。 ([71292a6](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/71292a67ebe2423f1e593aa3b987862466f0737d))

## [1.6.12](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.11...v1.6.12) (2024-05-27)


### Bug Fixes

* <SANYIC-15361>修复数字为空校验的错误使用 @NotBlank --> @NotNull ([d46717c](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/d46717cd3015fbfbcca1e42f32eefe9f9a4431e8))

## [1.6.11](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.10...v1.6.11) (2024-05-23)


### Bug Fixes

* release version trigger ([b7e52ce](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/b7e52ceec7caf6305982516159442333d806625f))

## [1.6.10](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.9...v1.6.10) (2024-05-23)


### Bug Fixes

* <SANYIC-15246>整体升级 ([f66a1d2](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/f66a1d2c96f9114deb5159c3c3b5218a6b097071))

## [1.6.9](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.8...v1.6.9) (2024-05-23)


### Bug Fixes

* release version trigger ([1d42cf8](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/1d42cf87a255064642ee405c69d0b729a647e026))

## [1.6.8](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.7...v1.6.8) (2024-05-23)


### Bug Fixes

* <SANYIC-15246>修复编译错误 ([9cb1210](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/9cb12108cac5e72ca25e5df7880f0eb8f311e40a))

## [1.6.7](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.6...v1.6.7) (2024-05-23)


### Bug Fixes

* <SANYIC-15246>修改 Maven 配置 ([21db4f2](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/21db4f23b3de8f4346ef8fbed5ad270aa972269f))

## [1.6.6](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.5...v1.6.6) (2024-05-23)


### Bug Fixes

* <SANYIC-15246>调整判断方案 ([258d31b](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/258d31b821d60b5784480d2ea368732ce86689ce))

## [1.6.5](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.4...v1.6.5) (2024-05-22)


### Bug Fixes

* <SANYIC-15332>修复租户管理员权限问题 ([bf0b48e](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/bf0b48e7bce4467d95d06526d6026b016e8be4b6))

## [1.6.4](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.3...v1.6.4) (2024-05-22)


### Bug Fixes

* <SANYIC-15246>调整代码逻辑 ([0d00e29](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/0d00e2948bf4fc3ca4d8440f65d8e77a4aae7e56))

## [1.6.3](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.2...v1.6.3) (2024-05-22)


### Bug Fixes

* <SANYIC-15246>调整影射方式 ([02333ac](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/02333ace48887907881122fa4d7748662208c4cf))

## [1.6.2](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.1...v1.6.2) (2024-05-22)


### Bug Fixes

* <SANYIC-15246>修复 SQL 错误 ([fa8e11e](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/fa8e11e02a8e9d34d288926f093babdc670e8755))

## [1.6.1](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.6.0...v1.6.1) (2024-05-22)


### Bug Fixes

* <SANYIC-15246>增加批量查询接口 ([4019875](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/401987538332ad8044eb90216e058398ea135398))

# [1.6.0](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.5.0...v1.6.0) (2024-04-11)


### Features

* 设备型号和部件型号新增所属站点字段 ([63ceb41](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/63ceb41e764a012884d91e582c47411df21f8461))

# [1.5.0](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.4.0...v1.5.0) (2024-04-11)


### Features

* 设备型号和部件型号新增所属站点字段 ([6596440](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/6596440fd3461d3e82b488bae262f539acfefa0a))

# [1.4.0](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.3.1...v1.4.0) (2024-04-11)


### Features

* 设备型号和部件型号新增所属站点字段 ([efaf115](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/efaf115f86cd1ab34422dd3f3e00fff7f836cc76))

## [1.3.1](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.3.0...v1.3.1) (2024-04-11)


### Bug Fixes

* 配置自动CD ([9a052a6](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/9a052a6cd97f19aed62ab1cfc231ff935b0f4a9f))

# [1.3.0](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.126...v1.3.0) (2024-04-11)


### Features

* 设备型号和部件型号新增所属站点字段 ([21f7266](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/21f72661462f1b6daa356ccb957e84c354809241))

## [1.2.126](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.125...v1.2.126) (2024-04-08)


### Bug Fixes

* 删除无用文件 ([614feea](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/614feeafde459e013e0f85af58c114020f1b171a))

## [1.2.125](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.124...v1.2.125) (2024-04-08)


### Bug Fixes

* 添加gitignore。 ([524709a](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/524709a92781aac125ecda120bb6d2c83ad4d044))

## [1.2.124](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.123...v1.2.124) (2024-03-06)


### Bug Fixes

* 生成新镜像。 ([4301272](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/430127282cd0a0d1ebd71fffbc4fab66a5afb02b))

## [1.2.123](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.122...v1.2.123) (2024-03-01)


### Bug Fixes

* flyway数据库脚本命名错误更正。 ([b36bc23](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/b36bc2331244fa085720c83703e72e976e5e8886))

## [1.2.122](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.121...v1.2.122) (2024-02-29)


### Bug Fixes

* 新增商用车脚本 ([a9b0c9c](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/a9b0c9cd10023839c9d16f528c43a081cdb6dff2))

## [1.2.121](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.120...v1.2.121) (2024-02-29)


### Bug Fixes

* 新增商用车的设备列表 ([cb790f1](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/cb790f1f0130b5bbeb35313de0f33ebea05de3c1))

## [1.2.120](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.119...v1.2.120) (2024-02-02)


### Bug Fixes

* 移除无用文件 ([4b60e7a](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/4b60e7aa5cfbaa00875f627fb176160f3b274010))

## [1.2.119](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.118...v1.2.119) (2024-01-24)


### Bug Fixes

* 更换基础镜像。 ([e57ec5d](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/e57ec5d4bc2f19b97183097deeeb6b8ab7d0b54d))

## [1.2.118](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.117...v1.2.118) (2024-01-24)


### Bug Fixes

* 更换基础镜像。 ([c1ed170](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/c1ed1706e705cfb57d3f6b3582ea54a43846f526))

## [1.2.117](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.116...v1.2.117) (2024-01-22)


### Bug Fixes

* 移除统一处理异常的代码，因为前端对ParamException有特殊处理 ([2ceb8bd](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/2ceb8bd9603daadfc125e279d3088d0496e19a65))

## [1.2.116](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.115...v1.2.116) (2024-01-22)


### Bug Fixes

* Flyway没有SQL文件报错问题处理。 ([a2c60e2](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/a2c60e2da3fb87d96af78ef96e3678f3bab6ff7a))

## [1.2.115](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.114...v1.2.115) (2024-01-21)


### Bug Fixes

* 添加CICD企业微信通知。 ([ddfc49b](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/ddfc49b41535c86269673ec89f380bcd23152154))
* 集成Flyway ([ed6b547](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/ed6b547bb58356c76a0a8befce7b7694b2083e1f))

## [1.2.114](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.113...v1.2.114) (2024-01-19)


### Bug Fixes

* 升级esmp-web-framework版本到0.0.17 ([db9777c](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/db9777c96922ec1421c4504878e9b640f1565f2b))

## [1.2.113](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.112...v1.2.113) (2024-01-19)


### Bug Fixes

* SANYIC-14370 修改"矿山机械类型"为"矿山机械" ([67d49ca](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/67d49ca1556b01bd9b6eb902cf18c9409cb14461))
* SANYIC-14395 统一处理异常信息 ([f98dfdc](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/f98dfdc0b26a3bc7446e4772e58b26976ce6fdb5))

## [1.2.112](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.111...v1.2.112) (2024-01-09)


### Bug Fixes

* SANYIC-14226 英文环境新增pg,设备类型存在未国际化数据 ([e84e282](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/e84e282a9db74f78429ae1c28dc95b27e387609f))

## [1.2.111](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.110...v1.2.111) (2023-12-15)


### Bug Fixes

* SANYIC-14121 数据权限功能，文件管理的机型列表 ([2a322f6](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/2a322f680baabee10dc0494de13eeb23e9b46099))

## [1.2.110](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.109...v1.2.110) (2023-12-13)


### Bug Fixes

* imageName ([daa0d05](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/daa0d05a13892d7a0aaacba138b977e1b2b4c6a8))

## [1.2.109](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.108...v1.2.109) (2023-12-13)


### Bug Fixes

* data script ([3e8e310](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/3e8e3105db333b61f818c740a5c08b69e66f567d))

## [1.2.108](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.107...v1.2.108) (2023-12-12)


### Bug Fixes

* 升级esmp-common包版本 ([79c98fb](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/79c98fbc6171bd551e7c932ff2720302e480ca53))

## [1.2.107](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.106...v1.2.107) (2023-12-11)


### Bug Fixes

* 更新初始化数据库脚本 ([0d8d5e9](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/0d8d5e951752ba29274f4ed659ad051841afeab3))

## [1.2.106](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.105...v1.2.106) (2023-12-05)


### Bug Fixes

* SANYIC-13931 增加修改设备型号描述操作日志 ([17c3fa1](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/17c3fa112939cd3e2e3f3d99448a076f817b8788))

## [1.2.105](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.104...v1.2.105) (2023-12-05)


### Bug Fixes

* 添加刷新Redis用户缓存接口 ([c072977](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/c072977f2d7519a0f7befa4e64ab3e09ebc40081))

## [1.2.104](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.103...v1.2.104) (2023-12-04)


### Bug Fixes

* 修复通过机械大类、机型ID无法精确查询设备型号的Bug ([3df779b](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/3df779bb4d85cf05b8e047989dcc1f220d2c11f6))

## [1.2.103](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.102...v1.2.103) (2023-12-01)


### Bug Fixes

* 修改esmp-web-framework的版本号 ([f0e4d91](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/f0e4d91975a3737c3f624c3624be521f8c76b433))

## [1.2.102](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.101...v1.2.102) (2023-11-30)


### Bug Fixes

* 统一序列化与反序列化 ([4c9281f](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/4c9281fb539326f110a502d54bedc03bd7807403))

## [1.2.101](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.100...v1.2.101) (2023-11-30)


### Bug Fixes

* 统一Yml文件 ([8413703](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/8413703cd6f35af7520fff20031a332d9c6e56f4))

## [1.2.100](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.99...v1.2.100) (2023-11-29)


### Bug Fixes

* 支持Redis可以切换不同的部署方式，支持将Date和LocalDateTime转换为UTC格式 ([9449f67](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/9449f67661a56c68e1194434b7bc7db280cf50bc))

## [1.2.99](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.98...v1.2.99) (2023-11-28)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 修复查询不到机械大类值的问题 ([2b290d4](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/2b290d4ed0268ef2e5cd574c74b9de912fbbb457))

## [1.2.98](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.97...v1.2.98) (2023-11-28)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 修改组织内与跨组织的关系为且的关系 ([1a2a51c](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/1a2a51c5a4660fc8606197a6be11e25edd7edaac))

## [1.2.97](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.96...v1.2.97) (2023-11-28)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 修改组织内与跨组织的关系为且的关系 ([640fb22](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/640fb22dcbec7f75c0fe2544449851aacba9ad0d))

## [1.2.96](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.95...v1.2.96) (2023-11-28)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 修改组织内与跨组织的关系为且的关系 ([a2a77b3](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/a2a77b373d81830394d2a59396f7de0a82d43856))

## [1.2.95](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.94...v1.2.95) (2023-11-24)


### Bug Fixes

* Revert "fix: SANYIC-13394 数据权限功能实现 - 修改组织内与跨组织的关系为且的关系" ([8f45c5c](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/8f45c5c89a1434708fdf33dfd65ad5de9edba93c))

## [1.2.94](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.93...v1.2.94) (2023-11-24)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 修改组织内与跨组织的关系为且的关系 ([a48fed7](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/a48fed7503aa9cdea9ef1ec04d2ff1ba58f7397e))

## [1.2.93](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.92...v1.2.93) (2023-11-24)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 修改组织内与跨组织的关系为且的关系 ([1325e48](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/1325e48443b7317701ca2760a4210cb9e7ba747d))

## [1.2.92](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.91...v1.2.92) (2023-11-24)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 修改组织内与跨组织的关系为且的关系 ([199c505](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/199c5057a5e41bfe0cec15ebb7d5a4f433d2c11f))

## [1.2.91](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.90...v1.2.91) (2023-11-24)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 修改组织内与跨组织的关系为且的关系 ([e6b8d6a](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/e6b8d6a6ce2a11ef2271cbd187fbb6ed9dd9fab7))

## [1.2.90](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.89...v1.2.90) (2023-11-23)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 修改组织内与跨组织的关系为且的关系 ([9480019](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/94800195ca30d94cb3b19f56e096c64b09e7fe56))

## [1.2.89](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.88...v1.2.89) (2023-11-22)


### Bug Fixes

* SANYIC-13399 列表接口添加数据权限过滤条件 ([023e045](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/023e0459434d892b4d3d8f2e359f489e4c3e284c))

## [1.2.88](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.87...v1.2.88) (2023-11-21)


### Bug Fixes

* SANYIC-13399 列表接口添加数据权限过滤条件 ([a140e6e](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/a140e6e232c98b2dfcb5dfc926fd7021d80efe47))

## [1.2.87](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.86...v1.2.87) (2023-11-20)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 更新esmp-iam版本号 ([07b11a9](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/07b11a993add0def405840e6c36f2c7aa8f305de))
* 添加初始化数据库脚本 ([be8e9bf](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/be8e9bf08154f6bb10b031c81e421ab57f4dac9a))

## [1.2.86](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.85...v1.2.86) (2023-11-17)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 更新esmp-iam版本号 ([7cb08a0](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/7cb08a0392560f42c5540b4c69cb4be48e2bc0e0))

## [1.2.85](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.84...v1.2.85) (2023-11-17)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 设备型号列表分页查询接口实现IAM数据权限过滤 ([a14e956](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/a14e95602367c09c69d02e0b2032e255dbbb29de))

## [1.2.84](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.83...v1.2.84) (2023-11-16)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 设备型号列表分页查询接口实现IAM数据权限过滤 ([a3652c3](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/a3652c38c0df8f3c98a49077d8b356a3964a4bed))

## [1.2.83](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.82...v1.2.83) (2023-11-16)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 设备型号列表页面下拉框数据权限控制 ([c04a0c7](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/c04a0c7233e028e7ab28c98f1201f0bafff50519))

## [1.2.82](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.81...v1.2.82) (2023-11-15)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 修复机型权限SQL的Bug ([f26e8db](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/f26e8db36ef1b2fce1ad29476767da7457b739a1))

## [1.2.81](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.80...v1.2.81) (2023-11-14)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 机型列表页面查询添加数据权限过滤 ([b9855df](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/b9855dfd369952ee54818a2a4262bc14169eb6fc))

## [1.2.80](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.79...v1.2.80) (2023-11-14)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 机型列表页面查询添加数据权限过滤 ([d951af8](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/d951af8db936fd0e47ac24177956e037e064cbdb))

## [1.2.79](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.78...v1.2.79) (2023-11-14)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - /listPossess接口参数调整 ([f2963ef](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/f2963ef0bd63bbdf7d14360fbeca3529dd4b37a9))

## [1.2.78](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.77...v1.2.78) (2023-11-13)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 将从上下文中获得的token缓存起来 ([4033f2d](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/4033f2d89aa6c9891e4d67214f7407d7bf31da86))

## [1.2.77](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.76...v1.2.77) (2023-11-13)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 修改依赖包版本号 ([40d321a](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/40d321ab684ab671910482f548ab9d0bc3ef963c))

## [1.2.76](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.75...v1.2.76) (2023-11-13)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 修改nexus仓库版本号 ([12dfed5](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/12dfed51f66ffbe60ef8b9551c4248084e31eb28))

## [1.2.75](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.74...v1.2.75) (2023-11-13)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 - 修改设备型号ID的字段映射 ([07859b1](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/07859b123fd9b448d5488100df1e17b168ad9934))

## [1.2.74](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.73...v1.2.74) (2023-11-13)


### Bug Fixes

* SANYIC-13394 数据权限功能实现 ([5648e50](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/5648e508a57348a54fb0befc6a781060cb14b5ea))

## [1.2.73](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.72...v1.2.73) (2023-11-03)


### Bug Fixes

* SANYIC-13534 支持使用Authorization传递token ([af0ca1b](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/af0ca1b798de8fe9354ef8e764fc05c53a903a8b))

## [1.2.72](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.71...v1.2.72) (2023-11-03)


### Bug Fixes

* SANYIC-13534 创建模型与设备型号时，指定租户ID ([e85ceac](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/e85ceac1e6b92cc0e9a278c405cc4fa6397d7a96))

## [1.2.71](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.70...v1.2.71) (2023-11-02)


### Bug Fixes

* SANYIC-13534 添加数据权限配置接口 ([b3a8192](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/b3a8192b6c8bc6710d0ac81857f919007968f167))

## [1.2.70](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.69...v1.2.70) (2023-11-02)


### Bug Fixes

* SANYIC-13534 添加数据权限配置接口 ([4b6be13](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/4b6be13d5b0230721907b118194b714960fe3057))

## [1.2.69](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.68...v1.2.69) (2023-11-01)


### Bug Fixes

* SANYIC-13534 添加数据权限配置接口 ([81eec23](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/81eec235fb269670a27d72b13abb0fd96b1dc018))

## [1.2.68](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.67...v1.2.68) (2023-10-30)


### Bug Fixes

* SANYIC-13577 修复默认语言环境不生效的问题 ([cd004b8](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/cd004b841e3d1c96f1cb3b32019a446143d3f929))

## [1.2.67](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.66...v1.2.67) (2023-10-30)


### Bug Fixes

* SANYIC-13577 修复默认语言环境不生效的问题 ([b98fe70](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/b98fe700c41fc91636b542945ce5e2f9e1d78ca0))

## [1.2.66](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.65...v1.2.66) (2023-10-17)


### Bug Fixes

* SANYIC-13271 初始化数据国际化 - 查询机型名称支持中英文环境 ([629cdc3](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/629cdc30c69ca6d48e5870cc429a9e309caccbe8))

## [1.2.65](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.64...v1.2.65) (2023-10-17)


### Bug Fixes

* SANYIC-13271 初始化数据国际化 ([4e9c147](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/4e9c147c623b62f5e9bb933dd47ae29af2d70326))

## [1.2.64](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.63...v1.2.64) (2023-10-16)


### Bug Fixes

* SANYIC-12760 国际化远程请求没有传递language的header ([a8cd1e8](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/a8cd1e814955260f13833b5e0eb8868eb7f0af46))

## [1.2.63](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.62...v1.2.63) (2023-10-10)


### Bug Fixes

* SANYIC-12760 国际化英文信息添加 ([e439946](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/e439946cbb9d673336085ca54c7527314d47b2b6))

## [1.2.62](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.61...v1.2.62) (2023-09-25)


### Bug Fixes

* 开启master分支自动部署QA环境 ([cabf81f](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/cabf81f982f3a3b961bee97dccc33831232b4b3f))

## [1.2.61](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.60...v1.2.61) (2023-09-21)


### Bug Fixes

* i18n自动替换 ([d55446e](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/d55446eb6224bf9c6a0c2d074c502ac4a63cebde))

## [1.2.60](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.59...v1.2.60) (2023-09-19)


### Bug Fixes

* i18n支持多模块扫描. ([8b4ddaf](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/8b4ddaf180c6f9280a2b6267d04a0bb605d2e35a))
* i18n自动替换中文 ([e86f50e](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/e86f50ea75b1961e2edb1925c47305326a212400))

## [1.2.59](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.58...v1.2.59) (2023-09-18)


### Bug Fixes

* NC2309版本集成期间禁止master分支自动更新OAM，改为release分支 ([16fc2c0](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/16fc2c0196323668fd5ee2474fcec79142f8227d))

## [1.2.58](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.57...v1.2.58) (2023-09-14)


### Bug Fixes

* SANYIC-13160 将机型授权从组织移除时，同时移除部件关于此机型的权限 ([3a94bc4](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/3a94bc464b5ba6558746fee2f93df0f030b913d7))

## [1.2.57](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.56...v1.2.57) (2023-08-21)


### Bug Fixes

* SANYIC-12608 请求信息中添加requestId ([eb24e51](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/eb24e51f7981890cddcc8b9cd908489f22a98606))

## [1.2.56](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.55...v1.2.56) (2023-08-21)


### Bug Fixes

* 查询租户的所有设备型号方法按创建时间倒叙排序. ([a352da4](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/a352da4870789be950cb8e3ba1225f2e56a51e88))

## [1.2.55](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.54...v1.2.55) (2023-08-17)


### Bug Fixes

* 批量查询设备型号信息接口增加型号业务编码参数. ([dc250e5](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/dc250e50e949b968dddd58dd4b3b01ef79494baf))

## [1.2.54](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.53...v1.2.54) (2023-08-14)


### Bug Fixes

* SANYIC-12608 请求信息中添加requestId ([cf7ae73](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/cf7ae7321dca2099faaa0b35ed1401d411bcbd90))

## [1.2.53](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.52...v1.2.53) (2023-08-14)


### Bug Fixes

* 批量查询设备型号信息接口增加型号业务编码返回. ([f7e27e8](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/f7e27e88f6bdf957cf5e20a96bb3620e1659b4d7))

## [1.2.52](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.51...v1.2.52) (2023-08-07)


### Bug Fixes

* SANYIC-12608 请求信息中添加requestId ([5f12002](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/5f120028271d867de349987750d92af43944700c))

## [1.2.51](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.50...v1.2.51) (2023-08-04)


### Bug Fixes

* 增加根据设备型号ID批量查询设备型号信息接口bugfix. ([bea6f5f](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/bea6f5ffa1b32778ffcab611629a6f8c5509e7b5))

## [1.2.50](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.49...v1.2.50) (2023-08-04)


### Bug Fixes

* SANYIC-12608 请求信息中添加requestId ([72cdf6b](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/72cdf6b7fcc0d6a4bd0411d3f298582d91149cac))

## [1.2.49](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.48...v1.2.49) (2023-08-04)


### Bug Fixes

* SANYIC-12608 请求信息中添加requestId ([770494f](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/770494f6db73f82099810755c2ca2f92f2cfc9ac))

## [1.2.48](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.47...v1.2.48) (2023-08-04)


### Bug Fixes

* SANYIC-12608 请求信息中添加requestId ([78477bc](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/78477bc3faf356fb08639dbf2cef372988dd46de))

## [1.2.47](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.46...v1.2.47) (2023-08-03)


### Bug Fixes

* 增加根据设备型号ID批量查询设备型号信息接口. ([4d7bb92](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/4d7bb92890357ea31b00080fc6e5a14be86ae0c8))

## [1.2.46](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.45...v1.2.46) (2023-08-03)


### Bug Fixes

* 增加根据设备型号ID批量查询设备型号信息接口. ([ac92c02](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/ac92c02c8d6f9faac11747426fedaaac2d2a4185))

## [1.2.45](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.44...v1.2.45) (2023-07-28)


### Bug Fixes

* 修改CI中的URL ([57f03df](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/57f03df07e32ca028e79a86b85587b17a1593553))

## [1.2.44](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.43...v1.2.44) (2023-07-28)


### Bug Fixes

* SANYIC-12547 获得所有机型部门关系列表 ([5bd1886](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/5bd1886fd661d093e142351a831480ed50fecf1c))

## [1.2.43](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.42...v1.2.43) (2023-05-16)


### Bug Fixes

* sql提交 ([4ec5b6d](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/4ec5b6d24d485857a3f5cba9d173cccb822047ee))

## [1.2.42](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.41...v1.2.42) (2023-05-16)


### Bug Fixes

* bump version ([28ea2fa](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/28ea2fae62ec88a14237fa4c9cd878f8fbd2ec8e))
* remove DLP ([dc2c5e9](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/dc2c5e96a78e72d8be0e1c5bc1e34bda5a2273fd))

## [1.2.41](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.40...v1.2.41) (2023-05-16)


### Bug Fixes

* DLP解密 ([9f8f051](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/9f8f051fd8acfc39b8e88c2a67761a9caa1150df))

## [1.2.40](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.39...v1.2.40) (2023-05-16)


### Bug Fixes

* ci cache ([c3b1245](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/c3b1245752fc6d74b82d38e74a0e340c359b558a))
* ci cache ([02641a8](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/02641a8552ceb45852f45b6329c642eb78cf70c8))

## [1.2.39](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.38...v1.2.39) (2023-05-16)


### Bug Fixes

* ci ([60aca3e](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/60aca3e2a681cbed5a96a0730e0faa300b4f3a4a))

## [1.2.38](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.37...v1.2.38) (2023-05-16)


### Bug Fixes

* 更新@semantic-release/gitlab ([800a59f](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/800a59f398e8bcd132aa3ff7b86a4778cc32f3d9))

## [1.2.37](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.36...v1.2.37) (2023-05-16)


### Bug Fixes

* 关闭代码检查 ([b89bc4d](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/b89bc4db14ba18648428e6697070a6b7af8a5432))
* 工程机械设备大类增加矿山机械类型 ([e20d7d9](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/e20d7d93f23e6d16af8ea4f43c4e5ea656ad60d9))

## [1.2.36](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.35...v1.2.36) (2023-01-13)


### Bug Fixes

* 修改代码 ([c35937d](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/c35937d735a57534b20882948a4edbdf2cc6c4d2))

## [1.2.35](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.34...v1.2.35) (2023-01-13)


### Bug Fixes

* 删除存留数据 ([975d020](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/975d020780927e72f23715bd0d9dbe98f0ae9d9d))

## [1.2.34](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.33...v1.2.34) (2023-01-12)


### Bug Fixes

* 修改代码 ([8d4b3e8](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/8d4b3e8b8bba5412b23a329e51e8146085aa5a04))

## [1.2.33](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.32...v1.2.33) (2023-01-11)


### Bug Fixes

* 修改.gitlab-ci.yml ([33fc615](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/33fc6155f0e7ebf297f91d5e4190123bd28a5afd))

## [1.2.32](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.31...v1.2.32) (2022-12-05)


### Bug Fixes

* 修改xxl-job支持环境变量 ([b28d5b6](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/b28d5b6524ec811e95c3438b64edb0ff54e69f10))

## [1.2.31](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.30...v1.2.31) (2022-11-02)


### Bug Fixes

* 修改版本号 ([8a6ed39](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/8a6ed39902d5c6d68ed533c684aa58d95b2379b0))

## [1.2.30](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.29...v1.2.30) (2022-11-02)


### Bug Fixes

* 修改.gitlab-ci.yml ([1eeaa07](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/1eeaa078377c631f5a6408c7374be10d044f4886))

## [1.2.29](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.28...v1.2.29) (2022-11-02)


### Bug Fixes

* 修改单租户接口 ([58ea1c6](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/58ea1c68915bebb1b7509eefec8e5c4b66502203))
* 跨租户修复 ([dc12aba](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/dc12aba8a3ee88327976ac3d302f38c81d8a496f))

## [1.2.28](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.27...v1.2.28) (2022-10-13)


### Bug Fixes

* 组织权限操作日志 ([0c67929](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/0c67929a4177d348e0d8a760a3c4942219cee758))

## [1.2.27](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.26...v1.2.27) (2022-10-13)


### Bug Fixes

* 组织权限操作日志 ([37fd3a5](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/37fd3a52c51544f2a78aa61960e2e85f6a5deb29))

## [1.2.26](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.25...v1.2.26) (2022-10-12)


### Bug Fixes

* 组织权限日志 ([6efbd9b](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/6efbd9bd9a236ec32e2d0739611e44de07a7f242))
* 组织权限日志 ([86b5c72](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/86b5c72c1b863e6a6c24b590be3c917cf3d564ca))

## [1.2.25](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.24...v1.2.25) (2022-10-10)


### Bug Fixes

* 设备型号查询增加设备型号id查询 ([1ac7325](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/1ac73256995660ce5276ef126f2a46ff9cd98149))

## [1.2.24](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.23...v1.2.24) (2022-10-10)


### Bug Fixes

* 移除授权要移除相关用户权限 ([4d719dd](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/4d719dd9d1b0c090b2a240deeb2b35c1db4727af))

## [1.2.23](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.22...v1.2.23) (2022-09-28)


### Bug Fixes

* 部门权限列表返回修改 ([15aee94](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/15aee9472f4f64e3eafb73fe6b2eb66ae311823f))

## [1.2.22](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.21...v1.2.22) (2022-09-28)


### Bug Fixes

* 设备型号列表分页查询-单租户 ([59547a6](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/59547a6e9ef05c0bcec63333a9287b918a11b00f))

## [1.2.21](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.20...v1.2.21) (2022-09-28)


### Bug Fixes

* 添加权限BUG修复 ([d9ec3b8](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/d9ec3b8cc8cb6d2a253f7c9dc688e224f8349b8f))

## [1.2.20](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.19...v1.2.20) (2022-09-27)


### Bug Fixes

* 设备型号查询增加productCode查询 ([db98bfa](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/db98bfa9e3fb193319950099e7ab933b0ee73ef5))

## [1.2.19](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.18...v1.2.19) (2022-09-27)


### Bug Fixes

* 组织列表查询修改 ([97190d5](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/97190d5c92573bc66bcd718a18ebc6968178f852))

## [1.2.18](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.17...v1.2.18) (2022-09-27)


### Bug Fixes

* 新增删除设备类型接口 ([ebce71a](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/ebce71a028c27139fa4df4ec6dc0123dd7413407))

## [1.2.17](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.16...v1.2.17) (2022-09-23)


### Bug Fixes

* 提交对接最新的iam日志推送平台服务 ([fd0d8fe](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/fd0d8fe354a9f39239b9ca9ca9047dad0980d432))
* 提交对接最新的iam日志推送平台服务 ([6a2b730](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/6a2b730d847484d6615de6ab19b30f82d2f3e797))

## [1.2.16](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.15...v1.2.16) (2022-09-23)


### Bug Fixes

* 返回businesscode ([3fbacf3](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/3fbacf37734cd8b645f0b154f5d7b4c4e4a72be7))

## [1.2.15](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.14...v1.2.15) (2022-09-21)


### Bug Fixes

* 设备数据权限 删除缓存 ([ff2dc6e](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/ff2dc6e301b39735453b60f8b67d9daa2756ea0d))

## [1.2.14](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.13...v1.2.14) (2022-09-20)


### Bug Fixes

* 权限查询接口改造 ([84514d3](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/84514d38868081fffc5c64eb2991deda308cc536))

## [1.2.13](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.12...v1.2.13) (2022-09-19)


### Bug Fixes

* 权限查询修改 ([2a49607](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/2a49607e497cd9753fe6419df021f540f743c60e))

## [1.2.12](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.11...v1.2.12) (2022-09-17)


### Bug Fixes

* 权限查询BUG fix ([bdedcb2](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/bdedcb2f248ba4151c07a887da1519875e594526))

## [1.2.11](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.10...v1.2.11) (2022-09-15)


### Bug Fixes

* 移除授权修改 ([059081e](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/059081e4c1abe2791592b9a42a6baab7d22be4ba))
* 移除授权修改 ([f81be9d](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/f81be9dc2d235ebea14253f6d41469e14e845b56))
* 组织权限查询 ([320bd5d](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/320bd5daf51aed7e471a98885f31484a3781f5b6))
* 组织权限查询 ([3e3e459](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/3e3e459ca618f1c444016c23fb58b7938e5c9120))

## [1.2.10](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.9...v1.2.10) (2022-09-15)


### Bug Fixes

* 修改入参 ([76a3a95](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/76a3a95f1e73e50268803c51919a1018622f52c5))

## [1.2.9](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.8...v1.2.9) (2022-09-15)


### Bug Fixes

* 清理不用的包引入 ([f282541](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/f282541b5fcc4ce121373dbde536b49ff2dda536))

## [1.2.8](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.7...v1.2.8) (2022-09-15)


### Bug Fixes

* 组织权限授权 ([519f9fa](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/519f9fa30d579aac0ebbdda844870cf35ace65c1))

## [1.2.7](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.6...v1.2.7) (2022-09-15)


### Bug Fixes

* 组织权限查询 ([ce51151](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/ce51151699e8ae856d609a704ec4bbea2903ada1))

## [1.2.6](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.5...v1.2.6) (2022-09-14)


### Bug Fixes

* 机型授权租户列表 ([9695707](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/9695707d73ade2d8d62c188b5d7d86f28a8e59f3))
* 机型查询改造 ([99c710a](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/99c710a8bd469ed90a586d91cbd2fc1958f1657f))
* 机型添加改造 ([49de6b8](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/49de6b8c17c3c65d2506e507201f1d3dfe5706cb))

## [1.2.5](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.4...v1.2.5) (2022-08-05)


### Bug Fixes

* 提交xxjob配置文件 ([f098fa2](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/f098fa25df058068a031c86045fe4e034a81425a))

## [1.2.4](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.3...v1.2.4) (2022-08-05)


### Bug Fixes

* 提交日志推送定时任务 ([9e6eb1d](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/9e6eb1de1f55da972c3cad00f73a1a90269a6407))

## [1.2.3](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.2...v1.2.3) (2022-07-27)


### Bug Fixes

* M07发布打包 ([b9dd2d7](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/b9dd2d7cf444aa15c26d1e1dfad6a1adfd2b57c6))

## [1.2.2](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.1...v1.2.2) (2022-07-25)


### Bug Fixes

* 测试ci配置01版本 ([cc2ac39](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/cc2ac39b6cc3e071fe78f27b05e31bc6496db078))
* 测试ci配置02版本 ([3828636](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/382863629c92944176e0db42367a4f4bd2fd7c4b))

## [1.2.1](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.2.0...v1.2.1) (2022-07-24)


### Bug Fixes

* 测试ci配置01版本 ([30b202a](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/30b202ae52852a2f3389176b7336d556243fb117))

# [1.2.0](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.1.0...v1.2.0) (2022-07-24)


### Features

* ci缓存机制 ([3ccfa0c](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/3ccfa0c3bc28ece47793a4aae02fa5b15a107cc6))

# [1.1.0](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.0.1...v1.1.0) (2022-07-23)


### Features

* 测试ci版本 ([43f213e](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/43f213ede5849319e67565327d79f8a26fbc093f))

## [1.0.1](http://gitlab.irootech.com/sanyic/ids-ucenter/compare/v1.0.0...v1.0.1) (2022-07-23)


### Bug Fixes

* 测试ci配置 ([c7418a6](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/c7418a6de78551fe23361ab84788f358fd533782))
* 测试ci配置 ([723a942](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/723a9424afacada6a6476068c6f3424e06946b79))

# 1.0.0 (2022-07-23)


### Bug Fixes

* logback 配置 ([e5a08d0](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/e5a08d03220210f305905f83dd39bfc64b60ce4f))
* 保持唯一的yml配置文件 ([e3e5a5b](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/e3e5a5b94860a8bd827718ca30c78dfcd04fb5ac))
* 保持唯一的yml配置文件 ([480cceb](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/480cceb1abbdc3dc18f3890017e47b0f7ecc7cac))
* 修改get请求为post请求 ([5c464ad](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/5c464ad854c49930801debe4ba751c3b38245c4b))
* 修改mybatis日志输出级别。 ([5948228](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/5948228e5a962c5bbe7016ec76a088eb91421a22))
* 修改tomcat连接数配置 ([9cb1d34](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/9cb1d349cb9e34545e83543d56cf4942260f0784))
* 修改tomcat连接数配置 ([46d628b](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/46d628bdea193e52a72da0c773f5a0ac3942a515))
* 修改tomcat连接数配置 ([dc3d2c6](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/dc3d2c6637c062de15704e9baabb6806310d6577))
* 修改设备型号后，查看该型号下的设备为空bug ([546d8bc](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/546d8bcdabe31120ba4cdad8ce4798d49086dd0d))
* 修改连接池参数 ([d556a16](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/d556a1620140e3c2e87d59cb498afbc2fc17bd72))
* 修改配置文件 ([7c093fb](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/7c093fbf8db1d27f19800179afe48a6255671834))
* 去除ids-gateway；改为一个yml文件；增加健康检查 ([aaf1eb0](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/aaf1eb0573a41f44e6f0fa481012bc58ee5a8e39))
* 同步更新总线拓扑发布最新版本 ([3cb354c](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/3cb354c073ed484b64e5cf862f882dbbe948e606))
* 增加单元测试覆盖率 ([24a52eb](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/24a52eb3b9c8d58b5b8e5c8ae3223fce9b7e9249))
* 增加调试日志 ([d109686](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/d1096866c6b073f3d7dc9b66fffaa839e21ad265))
* 字段名称desc改为description ([5bcfe38](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/5bcfe3863bfe89187ef6f2390d0e05914fba36aa))
* 字段名称desc改为description ([f18e818](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/f18e8183b01333e43d6111bf113a05ddd1f5a4e1))
* 改配置 ([4f07494](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/4f07494868ccced596f73ee75701d7f494c3deb0))
* 根据业务编码加设备型号 ([2a07399](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/2a07399d7743e68a4f78c26a49d04f2cd7db8bcd))
* 测试ci配置 ([d8dac7d](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/d8dac7dc5441ebe9aaa57bea600d7816048ea479))
* 设备型号同步总线拓扑结构失败bug ([ba18dc8](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/ba18dc853e2c76d2de121bd0d2f9c19d33e26bba))
* 设备型号过滤已发布bug ([7bd3667](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/7bd3667d17e22021c32b09819cdd672c138a35fa))
* 设备型号过滤已发布bug ([f8a7371](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/f8a7371fc66e6b7a12ca0ccef9f2c04fed160c3b))


### Features

* 更新记录的sql ([4487b4c](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/4487b4cc751512002a6015b55cdf88eb2298b4aa))
* 机型过滤 ([bfc66f7](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/bfc66f7d05917c8e638bc760a5d3f9ca10713027))
* 设备型号列表返回updateTime ([0ce623a](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/0ce623af05f5fc0087eca791f9026e4381b8b720))
* 设备型号发布功能 ([b334dab](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/b334dab9fba24a569ca65d44e4284db6d5cdc3d3))
* 设备型号增加描述信息、总线拓扑结构信息 ([096ae92](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/096ae92efe4792614146ce1fb7da0ae1837a3a30))
* 设备型号增加维护总线拓扑结构版本信息 ([b100088](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/b100088d4d395f937a176669b9fc159af1e41535))
* 设备型号支持发布功能 ([6a0573d](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/6a0573d2bc79aa639a648f0caff46074b18aa87a))
* 设备型号过滤已发布，打开配置开关 ([fb5b01c](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/fb5b01c03819ab503e584957bdac2f813846fc3a))
* 设备型号过滤已发布（默认不过滤，默认配置equipmentModel.unPublishedExclude=false） ([b7e9640](http://gitlab.irootech.com/sanyic/ids-ucenter/commit/b7e9640af516b5dd4eb78ccbf325a00e2c687526))
