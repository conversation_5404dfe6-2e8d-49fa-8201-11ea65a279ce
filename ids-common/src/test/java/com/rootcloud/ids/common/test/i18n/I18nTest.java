package com.rootcloud.ids.common.test.i18n;

import com.rootcloud.ids.common.i18n.ChineseFinder;
import java.util.Collections;
import java.util.List;

public class I18nTest {

  public static void main(String[] args) throws Exception {
    List<String> modules = Collections.singletonList("ids-common");
    String codeStart = "SYS_100121";
    int enumStartRowNum = 127;
    int zhPropertiesInsertRow = 121;
    int enPropertiesInsertRow = 121;
    ChineseFinder.multiModuleScan(
        codeStart,
        enumStartRowNum,
        zhPropertiesInsertRow,
        enPropertiesInsertRow,
        "/ids-common",
        modules);
  }
}
