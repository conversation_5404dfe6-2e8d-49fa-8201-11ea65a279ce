<?xml version="1.0" encoding="UTF-8"?>
<!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 -->
<!-- scan:当此属性设置为true时，配置文档如果发生改变，将会被重新加载，默认值为true -->
<!-- scanPeriod:设置监测配置文档是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。
                 当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<configuration  scan="true" scanPeriod="10 seconds">
    <contextName>logback</contextName>

    <springProperty scope="context" name="APP_NAME" source="spring.application.name"/>

    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />
    <conversionRule converterClass="com.rootcloud.esmp.common.log.TimestampConverter" conversionWord="seconds"/>
    <conversionRule converterClass="com.rootcloud.esmp.common.log.LevelConverter" conversionWord="level"/>
    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <!--1. 输出到控制台-->
    <!-- 树根要求日志格式-->
    <appender class="ch.qos.logback.core.ConsoleAppender" name="CONSOLE">
        <encoder charset="UTF-8" class="com.rootcloud.esmp.common.log.ServicePatternLayoutEncoder">
            <pattern>{ "T": "%seconds", "B": "ESMP", "S": "${APP_NAME}", "file": "%logger", "L": "%level","line": "%L", "short_message": "%replace(%replace(%msg){'\n','\\n'}){'"','\''}", %replace("method": "%method", ){'"method": "null", ', ''}%replace("path": "%path", ){'"path": "null", ', ''}%replace("statusCode": "%statusCode", ){'"statusCode": "null", ', ''}%replace("errorMessage": "%errorMessage", ){'"errorMessage": "null", ', ''}%replace("request": %request, ){'"request": null, ', ''}%replace("requestId": "%X{requestId}", ){'"requestId": "", ', ''}%replace("requestHost": "%requestHost", ){'"requestHost": "null", ', ''}%replace("remoteIp": "%remoteIp", ){'"remoteIp": "null", ', ''}%replace("response": %response, ){'"response": null, ', ''}%replace("duration": "%duration", ){'"duration": "null", ', ''}%replace("stack": "%replace(%replace(%exception){'\n','\\n'}){'"','\''}", ){'"stack": "", ', ''}"_thread": "%thread"} %nopex%n</pattern>
        </encoder>
    </appender>


    <!-- 原始格式日志-->
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <appender class="ch.qos.logback.core.ConsoleAppender" name="DEFAULT">
        <!--日志文件输出格式-->
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
        <!--此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>info</level>
        </filter>
    </appender>

    <!-- 开发环境输出至控制台 -->
    <springProfile name="dev">
        <logger name="com.alibaba.nacos" level="OFF" addtivity="false"> </logger>
        <root level="${LOG_LEVEL:-INFO}">
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>
    <!-- 测试环境输出至控制台 -->
    <springProfile name="test">
        <logger name="com.alibaba.nacos" level="OFF" addtivity="false"> </logger>
        <root level="${LOG_LEVEL:-INFO}">
            <appender-ref ref="CONSOLE" />s
        </root>
    </springProfile>
    <!-- 开发环境输出至控制台 -->
    <springProfile name="develop">
        <logger name="com.alibaba.nacos" level="OFF" addtivity="false"> </logger>
        <root level="${LOG_LEVEL:-INFO}">
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>
    <!--生产环境输出至文件-->
    <springProfile name="prod">
        <root level="${LOG_LEVEL:-INFO}">
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>

</configuration>
