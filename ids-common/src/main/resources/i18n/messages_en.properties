SYS_100000=System error
SYS_100002=Token
SYS_100003=Data permission center API document
SYS_100004=Token expires at
SYS_100005=Whether to return an exception (dg-info=show)
SYS_100006=Develop key
SYS_100007=You need to add <PERSON><PERSON> before the parameters for oauth2 protocol
SYS_100008=API documentation
SYS_100009=Create
SYS_100010=Remove
SYS_100011=Modify
SYS_100012=View
SYS_100013=Department
SYS_100014=User
SYS_100015=Data permission
SYS_100016=Part product model
SYS_100017=Part list
SYS_100018=Device list
SYS_100019=File Management
SYS_100020=Device product model
SYS_100021=Category maintenance
SYS_100022=Organization user permission
SYS_100023=User Data Permission
SYS_100024=Succeed
SYS_100025=Fail
SYS_100026=Abnormal
SYS_100027=Organization
SYS_100028=Enable
SYS_100029=Not enabled
SYS_100030=Mechanical category
SYS_100031=Category
SYS_100032=Ownership
SYS_100033=Use permission of this organization
SYS_100034=Cross-organization uses permission
SYS_100035=Published
SYS_100036=Unpublished
SYS_100037=Default
SYS_100038=Not default
SYS_100039=Excavator
SYS_100040=Engineering lifting machinery
SYS_100041=Concrete machinery
SYS_100042=Compaction machinery
SYS_100043=Road machinery
SYS_100044=Port machinery
SYS_100045=Processing Machinery
SYS_100046=Military engineering machinery
SYS_100047=Firefighting equipment machinery
SYS_100048=Environmental protection machinery
SYS_100049=Tunneling machinery
SYS_100050=Aerial work machinery
SYS_100051=Specialized parts for engineering machinery
SYS_100052=Other specialized construction machinery
SYS_100053=Public
SYS_100054=Electrified machinery
SYS_100055=Construction machinery
SYS_100056=Reinforcement and prestressing machinery
SYS_100057=Processing machinery
SYS_100058=Rock drilling machinery
SYS_100059=Elevators and escalators
SYS_100060=Oil machinery
SYS_100061=Mining machinery
SYS_100062=Total 
SYS_100063=Mechanical classification: 
SYS_100064=Category: 
SYS_100065=Product model of device: 
SYS_100066=Original value: 
SYS_100067= . Value after modification:  
SYS_100068=Version before upgrade: 
SYS_100069=Target version: 
SYS_100070=Task name: 
SYS_100071=Reasons for failure: 
SYS_100072=Succeed
SYS_100073=Fail to issue 
SYS_100074=Modify the information of the bound thing model name
SYS_100075=Bound model name
SYS_100076=Model
SYS_100077=Auth ID
SYS_100078=Auth key
SYS_100079=Bound device name
SYS_100080=Instance
SYS_100081=Part name
SYS_100082=Part: 
SYS_100083=Part node: 
SYS_100084=Product model: 
SYS_100085=Devices: 
SYS_100086=Upload file package from local: 
SYS_100087=Synchronize protocol file package from the protocol modeling tool
SYS_100088=Download file: 
SYS_100089=Product model name
SYS_100090=Device ID
SYS_100091=File package name
SYS_100092=File package: 
SYS_100093=Device car number
SYS_100094=Device VIN code
SYS_100095=Device material ID
SYS_100096=Field info
SYS_100097=Create category: 
SYS_100098=Create device product model: 
SYS_100099=Name
SYS_100100=Add department
SYS_100101=Add
SYS_100102=Authorize
SYS_100103=Executed: An exception occurred while pushing log information to the logging platform dmpJobPushDmpLogToLogging
SYS_100104=Product ID is required
SYS_100105=The name length is not standardized!
SYS_100106=Organization ID is required
SYS_100107=Organization ID is required
SYS_100108=The mechanical classification does not exist!
SYS_100109=Name: 
SYS_100110=Repeated!
SYS_100111=Product does not exist!
SYS_100112=The classification cannot be deleted as there are subsets under it!
SYS_100113=The mechanical classification or category does not exist!
SYS_100114=Model type does not exist!
SYS_100115=Device product model does not exist!
SYS_100116=Default
SYS_100117=The category does not exist!
SYS_100118=The tenant does not exist!
SYS_100119=The tenant admin cannot be removed
SYS_100120=Canceled
SYS_100121=Succeed
SYS_100122=User request parameter error
SYS_100123=The resource requested does not exist
SYS_100124=Request parameter is empty
SYS_100125=Access permission exception
SYS_100126=System execution error
SYS_100127=ID card
SYS_100128=Military ID card
SYS_100129=Driver license
SYS_100130=Username
SYS_100131=Mobile number
SYS_100132=The only identifier of the open identification system
SYS_100133=Male
SYS_100134=Female
SYS_100135=Others
SYS_100136=Normal
SYS_100137=Delete
SYS_100138=On
SYS_100139=Freeze
SYS_100140=Disable
SYS_100141=The increment factor must be greater than 0
SYS_100142=The decreasing factor must be greater than 0
SYS_100143=Protocol modeling tool
SYS_100144=Please pass the authentication header
SYS_100145=Fail to retrieve user login info
SYS_100146=Parameter verification fails
SYS_100147=Type error
SYS_100148=The request body is required
SYS_100149=Exception in calling microservices
SYS_100150=Wrong field type
SYS_100151=Organization I joined
SYS_100152=My customers
SYS_100153=Username and password mode
SYS_100154=Refresh token
SYS_100155=Client
SYS_100156=Authorization Code
SYS_100157=IAM calling path: 
SYS_100158=J1939
SYS_100159=OTA
SYS_100160=Description
SYS_100161=The parameters are invalid. Please refer to the interface documentation. Invalid parameters: {0}
SYS_100162=The parameters are invalid. Please refer to the interface documentation.