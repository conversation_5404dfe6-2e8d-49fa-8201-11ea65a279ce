package com.rootcloud.ids.common.i18n;


import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.UnicodeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import com.rootcloud.ids.common.i18n.Process.AfterRowHandler;
import com.rootcloud.ids.common.i18n.Process.FilterRow;
import com.rootcloud.ids.common.i18n.Process.PropertyAppender;
import com.rootcloud.ids.common.i18n.Process.RowReplaceHandler;
import com.rootcloud.ids.common.i18n.Process.RowReplaceResult;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class ChineseFinder {

  public static final String ENTER = "\n";
  public static final FilterRow commonRowFilter = (String tempString) -> {
    //静态变量
    if (isFieldRow(tempString)) {
      return false;
    }
    //@FieldCheck
    if (isFieldCheckRow(tempString)) {
      return false;
    }
    return true;
  };
  public static final FilterRow fieldRowFilter = (String tempString) -> {
    String str = tempString.trim();
    //静态变量
    if (isFieldRow(str)) {
      return true;
    }
    return false;
  };
  /**
   * public static final String STRING_RESULT_OPERATION_FAILED = "操作失败"; public static final String
   * STRING_RESULT_OPERATION_FAILED(){return I18nUtil.message(I18nCode.SYS_100000);};
   */
  public static final RowReplaceHandler fieldReplaceHandler = (row, key, code, context1) -> {
    boolean hasReplace = false;
    String str = row;
    String[] rowArr = row.split("=");
    if (rowArr.length == 2 && StrUtil.isNotEmpty(rowArr[0].trim())) {
      hasReplace = true;
      str = String.format("%s(){return I18nUtil.message(I18nCode.%s);};", rowArr[0],
          code);
    }
    return new RowReplaceResult(str, hasReplace);
  };
  public static final RowReplaceHandler commonRowReplaceHandler = (row, key, code, context1) -> {
    // I18nUtil.message(I18nCode.SYS_100000)
    String expression = String.format("I18nUtil.message(I18nCode.%s)", code);
    boolean hasReplace = false;
    String str = row;
    if (StrUtil.isNotEmpty(expression)) {
      hasReplace = true;
      str = row.replace("\"" + key + "\"", expression);
    }
    return new RowReplaceResult(str, hasReplace);
  };
  public static final FilterRow fieldCheckRowFilter = (String tempString) -> {
    String str = tempString.trim();
    //@FieldCheck
    if (isFieldCheckRow(str)) {
      return true;
    }
    return false;
  };
  /**
   * @FieldCheck(notNull = true, notNullMessage = "数据ID不允许为空", minNum = 1)
   * @FieldCheck(notNull = true, notNullMessage = "{SYS_100000}", minNum = 1)
   */
  public static final RowReplaceHandler fieldCheckReplaceHandler = (row, key, code, context1) -> {
    String expression = String.format("{%s}", code);
    String str = row.replace("\"" + key + "\"", "\"" + expression + "\"");
    return new RowReplaceResult(str, true);
  };
  public static final FilterRow enumDefineRowFilter = (String tempString) -> {
    //枚举当前扫描到且要改动的行，就是枚举定义的行
    return true;
  };
  /**
   * BUS(1, "总线"),
   * <p>
   * BUS(1, "{SYS_100001}"),
   */
  public static final RowReplaceHandler enumDefineReplaceHandler = (row, key, code, context1) -> {
    String expression = String.format("{%s}", code);
    boolean hasReplace = false;
    String str = row;
    if (StrUtil.isNotEmpty(expression)) {
      hasReplace = true;
      str = row.replace("\"" + key + "\"", "\"" + expression + "\"");
    }
    return new RowReplaceResult(str, hasReplace);
  };
  /**
   * return label; return value.label; =================> return I18nUtil.message(label); return
   * I18nUtil.message(value.label);
   */
  public static final AfterRowHandler enumAfterRowHandler = (row, context1) -> {

    if (row.contains("return label;")) {
      return row.replace("return label;", "return I18nUtil.message(label);");
    } else if (row.contains("return value.label;")) {
      return row.replace("return value.label;", " return value.getLabel();");
    } else {
      return row;
    }
  };

  public static final PropertyAppender zhAppender = (In18Context context, StringBuilder fileContent) -> {
    //SYS_100000=\u7cfb\u7edf\u9519\u8bef\uff0c\u8bf7\u7a0d\u540e\u518d\u8bd5
    Set<String> keys = context.keyCodeMap.keySet();
    //插入了多少行
    int insertNum = 0;
    for (String key : keys) {
      String code = context.keyCodeMap.get(key);
      if (StrUtil.isNotEmpty(code)) {
        //转成unicode编码
        fileContent.append(String.format("%s=%s", code, UnicodeUtil.toUnicode(key, true)))
            .append("\n");
      }
    }
    return insertNum;
  };

  public static final PropertyAppender enAppender = (In18Context context, StringBuilder fileContent) -> {
    //SYS_100000=System error, please try again later
    Set<String> keys = context.keyCodeMap.keySet();
    //插入了多少行
    int insertNum = 0;
    for (String key : keys) {
      String code = context.keyCodeMap.get(key);
      if (StrUtil.isNotEmpty(code)) {
        fileContent.append(String.format("%s=%s", code, code)).append("\n");
      }
    }
    return insertNum;
  };

  //输出文件路径
  public static String outFile = "/Users/<USER>/Desktop/in8-test.txt";

  public static void main(String[] args) throws Exception {
    String codeStart = "SYS_100333";
    In18Context context = initContext(null, null);
    context.enumInfo.startRowNum = 339;
    context.zhPropertiesInsertRow = 333;
    context.enPropertiesInsertRow = 333;
    context.codeStart = codeStart;
    doScan(null, context);
  }

  public static void multiModuleScan(
      String codeStart,
      int enumStartRowNum,
      int zhPropertiesInsertRow,
      int enPropertiesInsertRow,
      String baseModule,List<String> modules
  ) throws Exception {

    for (String module : modules) {
      In18Context context = initContext(baseModule, module);
      context.enumInfo.startRowNum = enumStartRowNum;
      context.zhPropertiesInsertRow = zhPropertiesInsertRow;
      context.enPropertiesInsertRow = enPropertiesInsertRow;
      context.codeStart = codeStart;
      doScan(module, context);
      //回写最终num，多模块可能还要继续用
      enumStartRowNum = context.enumInfo.startRowNum;
      zhPropertiesInsertRow = context.zhPropertiesInsertRow;
      enPropertiesInsertRow = context.enPropertiesInsertRow;
    }
  }


  private static void doScan(String module, In18Context context)
      throws Exception {
    Process process = new Process(new PrintWriter(new FileWriter(new File(outFile))));
    //扫描中文到context
    process.scan(context, context.scanPath);
    //给每个中文生成对应的code
    Map<String, String> existKeyCodeMap = getExistKeyCodeMap(context);
    generateCode(context, existKeyCodeMap);
    //code枚举类新增code
    generateCodeEnum(context);
    //生成不同语言对应的code配置
    generateCodeProperties(context);
    //旧的定义也需要添加到字典里面，下面要完整的 中文->code 字典
    context.keyCodeMap.putAll(existKeyCodeMap);

    //替换除了静态属性/枚举/注解的中文字符串
    replaceCommon(context);

    //替换属性的中文字符串, todo 把用了属性的地方后面追加(),忽略属性类本身的引用还有import
    replaceField(context);

    //替换@FieldCheck的中文字符串
    replaceFieldCheck(context);

    //替换枚举类中的中文
    replaceEnum(context);

    //修正英文配置文件
    //fixEnProperties(context);

    System.out.println(module + ":In18替换完成！");
  }

  public static boolean isFieldRow(String row) {
    String str = row.trim();
    //静态变量
    return str.startsWith("public static final String") || str.startsWith("private static final String") ||str.startsWith("protected static final String") ||
        str.startsWith("public static String") || str.startsWith("private static String") ||str.startsWith("protected static String") ||
        str.startsWith("public final String") || str.startsWith("private final String") ||str.startsWith("protected final String") ||
        str.startsWith("public String") || str.startsWith("private String") ||str.startsWith("protected String");
  }

  public static boolean isFieldCheckRow(String row) {
    String str = row.trim();
    //静态变量
    return str.contains("@FieldCheck");
  }


  private static void fixEnProperties(In18Context context) throws Exception {
    //加载code->英文字典
    Map<String, String> codeEnMap = getCodeEnMap(context);
    //替换当前的英文配置文件
    String enPath = context.resourcePath + context.enPropertiesPath;
    BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(enPath)));
    StringBuilder fileContent = new StringBuilder();
    String tempString = "";
    while ((tempString = reader.readLine()) != null) {
      String[] arr = tempString.split("=");
      if (arr.length == 2 && codeEnMap.containsKey(arr[0])) {
        String code = arr[0];
        String en = codeEnMap.get(code);
        tempString = String.format("%s=%s", code, en);
      }
      fileContent.append(tempString).append(ENTER);
    }
    reader.close();
    //回写内容到.java文件
    writeFile(enPath, fileContent.toString());
  }

  private static Map<String, String> getCodeEnMap(In18Context context) throws Exception {
    //加载中文->英文
    String jsonPath = context.testResourcePath + "i18n/codeMap.json";
    File f = new File(jsonPath);
    JSON json = JSONUtil.readJSON(f, Charset.defaultCharset());
    Map<String, String> zhEnMap = json.toBean(new TypeReference<Map<String, String>>() {
    });

    //加载code->中文配置
    String zhPropertiesPath = context.resourcePath + context.zhPropertiesPath;
    BufferedReader zhReader = new BufferedReader(
        new InputStreamReader(new FileInputStream(zhPropertiesPath)));
    //中文->code
    Map<String, String> zhCodeMap = new HashMap<>();
    String tempString = "";
    while ((tempString = zhReader.readLine()) != null) {
      String[] arr = tempString.split("=");
      if (arr.length == 2) {
        zhCodeMap.put(arr[1], arr[0]);
      }

    }
    zhReader.close();
    Map<String, String> codeEnMap = new HashMap<>();
    for (String zh : zhEnMap.keySet()) {
      String en = zhEnMap.get(zh);
      String code = zhCodeMap.get(zh);
      if (StrUtil.isNotEmpty(en) && StrUtil.isNotEmpty(code)) {
        codeEnMap.put(code, en);
      }
    }
    return codeEnMap;
  }

  private static void generateCodeProperties(In18Context context) throws IOException {
    //中文配置
    String resourcePath = StrUtil.isNotEmpty(context.baseModuleResourcePath) ? context.baseModuleResourcePath
            : context.resourcePath;
    String zhPath = resourcePath + context.zhPropertiesPath;
    int insertRow = context.zhPropertiesInsertRow;
    int insertZhNum = addProperties(zhPath, insertRow, context, zhAppender);
    context.zhPropertiesInsertRow = context.zhPropertiesInsertRow + insertZhNum;

    //英文配置
    String enPath = resourcePath + context.enPropertiesPath;
    int enInsertRow = context.enPropertiesInsertRow;
    int insertEnNum = addProperties(enPath, enInsertRow, context, enAppender);
    context.enPropertiesInsertRow = context.enPropertiesInsertRow + insertEnNum;
  }

  private static int addProperties(String path, int insertRow, In18Context context,
      PropertyAppender appender) throws IOException {
    boolean hasInsert = false;
    BufferedReader reader = new BufferedReader(
        new InputStreamReader(new FileInputStream(path)));
    StringBuilder fileContent = new StringBuilder();
    String tempString = "";
    int insertNum = 0;
    int row = 0;
    while ((tempString = reader.readLine()) != null) {
      row++;
      if (row == insertRow) {
        //到了插入枚举那一行了
        insertNum = appender.appendProperties(context, fileContent);
        hasInsert = true;
      }
      fileContent.append(tempString).append(ENTER);
    }
    reader.close();
    if (!hasInsert) {
      //最末尾插入
      insertNum = appender.appendProperties(context, fileContent);
    }
    //回写内容到.java文件
    writeFile(path, fileContent.toString());
    return insertNum;
  }

  public static Map<String, String> getExistKeyCodeMap(In18Context context) {
    Map<String, String> map = new HashMap<>();
    Class<? extends Enum> enumClass = context.enumInfo.enumClass;
    try {
      Method method = enumClass.getDeclaredMethod("values");
      Object result = method.invoke(null);
      Object[] arr = (Object[]) result;
      for (int i = 0; i < arr.length; i++) {
        Object o = arr[i];
        Field code = o.getClass().getDeclaredField("code");
        code.setAccessible(true);
        Field des = o.getClass().getDeclaredField("desc");
        des.setAccessible(true);
        map.put((String) des.get(o), (String) code.get(o));
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
    return map;
  }


  private static In18Context initContext(String baseModule, String module) {
    String projectPath = System.getProperty("user.dir");
    String holder = StrUtil.isEmpty(module) ? "" : "/" + module;
    In18EnumInfo in18EnumInfo = new In18EnumInfo(I18nCode.class, I18nUtil.class);
    String javaPath = projectPath + holder + "/src/main/java/";
    In18Context context = new In18Context( in18EnumInfo, javaPath);
    context.resourcePath = projectPath + holder + "/src/main/resources/";
    context.testResourcePath = projectPath + holder + "/src/test/resources/";
    //需要国际化的文件目录，默认不包含test
    context.scanPath = projectPath + holder + "/src/main/java/";

    //中文配置文件路径,resource相对路径
    context.zhPropertiesPath = "i18n/messages_zh.properties";
    context.enPropertiesPath = "i18n/messages_en.properties";

    if (StrUtil.isNotEmpty(baseModule)) {
      context.baseModuleJavaPath = projectPath + baseModule + "/src/main/java/";
      context.baseModuleResourcePath = projectPath + baseModule + "/src/main/resources/";
    }

    return context;
  }

  /**
   * 替换除了静态属性/枚举/注解的中文字符串
   */
  private static void replaceCommon(In18Context context) throws Exception {
    Map<String, Map<String, Set<Integer>>> map = getScanMap(context);
    Set<String> classSets = map.keySet();

    for (String classPath : classSets) {
      if (isCommonClass(context, classPath)) {
        Map<String, Set<Integer>> keyRowMap = map.getOrDefault(classPath, new HashMap<>());
        //普通行替换：除了静态变量/注解
        replaceJavaFile(classPath, keyRowMap, context, commonRowFilter, commonRowReplaceHandler,
            null);
      }
    }
  }

  /**
   * 替换属性的中文字符串
   */
  private static void replaceField(In18Context context) throws Exception {
    Map<String, Map<String, Set<Integer>>> map = getScanMap(context);
    Set<String> classSets = map.keySet();

    for (String classPath : classSets) {
      if (isCommonClass(context, classPath)) {
        Map<String, Set<Integer>> keyRowMap = map.getOrDefault(classPath, new HashMap<>());
        replaceJavaFile(classPath, keyRowMap, context, fieldRowFilter, fieldReplaceHandler, null);
      }
    }
  }

  /**
   * 替换@FieldCheck的中文字符串
   */
  private static void replaceFieldCheck(In18Context context) throws Exception {
    Map<String, Map<String, Set<Integer>>> map = getScanMap(context);
    Set<String> classSets = map.keySet();

    for (String classPath : classSets) {
      if (isCommonClass(context, classPath)) {
        Map<String, Set<Integer>> keyRowMap = map.getOrDefault(classPath, new HashMap<>());
        //普通行替换：除了静态变量/注解
        replaceJavaFile(classPath, keyRowMap, context,
            fieldCheckRowFilter,
            fieldCheckReplaceHandler,
            null);
      }
    }
  }

  /**
   * 替换枚举类中的中文
   */
  private static void replaceEnum(In18Context context) throws Exception {
    Map<String, Map<String, Set<Integer>>> map = getScanMap(context);
    Set<String> classSets = map.keySet();

    for (String classPath : classSets) {
      if (isEnumClass(context, classPath)) {
        Map<String, Set<Integer>> keyRowMap = map.getOrDefault(classPath, new HashMap<>());
        //普通行替换：除了静态变量/注解
        replaceJavaFile(classPath, keyRowMap, context,
            enumDefineRowFilter,
            enumDefineReplaceHandler,
            enumAfterRowHandler);
      }
    }
  }

  /**
   * 枚举类忽略
   */
  public static boolean isCommonClass(In18Context context, String classPath) throws Exception {
    //枚举类先不处理
    if (isEnumClass(context, classPath)) {
      return false;
    }
    return true;
  }

  /**
   * 是否枚举类
   */
  public static boolean isEnumClass(In18Context context, String classPath) throws Exception {
    String javaPath = context.javaPath;
    if (classPath.startsWith(javaPath)) {
      String path = classPath.substring(javaPath.length());
      path = path.replace("/", ".");
      if (path.endsWith(".java")) {
        path = path.substring(0, path.length() - ".java".length());
      }
      Class c = Class.forName(path);
      return c.isEnum();
    }
    return false;
  }


  private static void replaceJavaFile(
      String classpath,
      Map<String, Set<Integer>> keyRowMap,
      In18Context context,
      FilterRow filterRow,
      RowReplaceHandler replaceHandler,
      AfterRowHandler afterRowHandler
  ) throws IOException {

    String path = classpath;
    Map<Integer, Set<String>> rowKeyMap = genRowKeyMap(keyRowMap);
    if (MapUtil.isEmpty(rowKeyMap)) {
      return;
    }
    BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(path)));
    List<String> lines = new ArrayList<>();
    String tempString = "";
    int packageLine = 0;
    int row = 0;
    boolean hasReplace = false;
    Set<String> importSet = new HashSet<>();
    while ((tempString = reader.readLine()) != null) {
      row++;
      //package
      if (tempString.startsWith("package ") && packageLine == 0) {
        packageLine = row;
      }
      //import
      if (tempString.startsWith("import ")) {
        String str = tempString.substring("import ".length()).trim();
        //去除后面的;
        str = str.substring(0, str.length() - 1);
        importSet.add(str);
      }
      if (filterRow.filter(tempString) && rowKeyMap.containsKey(row)) {
        //替换中文成In18表达式
        Set<String> keys = rowKeyMap.get(row);
        for (String key : keys) {
          String code = context.keyCodeMap.get(key);
          if (StrUtil.isEmpty(code)) {
            continue;
          }
          RowReplaceResult result = replaceHandler.replaceHandle(tempString, key, code, context);
          if (result.hasChange) {
            tempString = result.result;
            hasReplace = true;
          }
        }
      }
      lines.add(tempString);
    }
    reader.close();

    if (hasReplace) {
      //追加In18的import到java类中
      lines = appendImport(lines, packageLine, context, importSet);
      lines = afterRowHandler(afterRowHandler, lines, context);
      StringBuilder fileContent = new StringBuilder();
      for (String line : lines) {
        fileContent.append(line).append(ENTER);
      }
      //回写内容到.java文件
      writeFile(path, fileContent.toString());
    }
  }

  private static List<String> afterRowHandler(AfterRowHandler afterRowHandler, List<String> lines,
      In18Context context) {
    if (afterRowHandler == null) {
      return lines;
    }
    List<String> results = new ArrayList<>();
    for (String line : lines) {
      String str = afterRowHandler.afterRow(line, context);
      if (str == null) {
        continue;
      }
      results.add(str);
    }
    return results;
  }


  private static List<String> appendImport(List<String> lines, int packageLine,
      In18Context context, Set<String> importSet) {

    int insertLine = packageLine + 1;
    List<String> results = new ArrayList<>();
    int i = 0;
    for (String line : lines) {
      i++;
      if (i == insertLine) {
        if (!importSet.contains(context.enumInfo.enumClass.getName())) {
          results.add(String.format("import %s;", context.enumInfo.enumClass.getName()));
        }
        if (!importSet.contains(context.enumInfo.utilClass.getName())) {
          results.add(String.format("import %s;", context.enumInfo.utilClass.getName()));
        }
      }
      results.add(line);
    }
    return results;
  }

  private static String getCommonExpression(String key, In18Context context) {
    String code = context.keyCodeMap.get(key);
    if (StrUtil.isEmpty(code)) {
      return null;
    }
    // I18nUtil.message(I18nStatusCode.System.SYS_100000)
    return String.format("I18nUtil.message(I18nCode.%s)", code);
  }

  private static Map<Integer, Set<String>> genRowKeyMap(Map<String, Set<Integer>> keyRowMap) {
    Map<Integer, Set<String>> map = new HashMap<>();
    keyRowMap.forEach((key, rows) ->
        rows.forEach(row ->
            map.computeIfAbsent(row, k -> new HashSet<>()).add(key)
        )
    );
    return map;
  }

  private static Map<String, Map<String, Set<Integer>>> getScanMap(In18Context context) {
    String testClass = context.getTestReplaceClassPath();
    if (StrUtil.isNotEmpty(testClass)) {
      Map<String, Map<String, Set<Integer>>> map = new HashMap<>();
      map.put(testClass, context.map.get(testClass));
      return map;
    }
    return context.map;
  }

  private static void generateCodeEnum(In18Context context) throws IOException {

    String enumClassPath = null;
    //多模块项目
    if (StrUtil.isNotEmpty(context.baseModuleJavaPath)) {
      enumClassPath =
          ChineseFinder.getClassPath(context.baseModuleJavaPath,
              context.enumInfo.enumClass.getName()) + ".java";
    } else {
      enumClassPath =
          ChineseFinder.getClassPath(context, context.enumInfo.enumClass.getName()) + ".java";
    }

    int insertRow = context.enumInfo.startRowNum;
    BufferedReader reader = new BufferedReader(
        new InputStreamReader(new FileInputStream(enumClassPath)));
    StringBuilder fileContent = new StringBuilder();
    String tempString = "";
    int row = 0;
    while ((tempString = reader.readLine()) != null) {
      row++;
      if (row == insertRow) {
        //到了插入枚举那一行了
        appendEnumCode(context, fileContent);
      }
      fileContent.append(tempString).append(ENTER);
    }
    reader.close();
    //回写内容到.java文件
    writeFile(enumClassPath, fileContent.toString());
  }

  private static void appendEnumCode(In18Context context, StringBuilder fileContent) {
    //  SYS_100000("SYS_100000", "系统错误，请稍后再试")
    Set<String> keys = context.keyCodeMap.keySet();
    for (String key : keys) {
      String code = context.keyCodeMap.get(key);
      fileContent.append(String.format("  %s(\"%s\", \"%s\"),", code, code, key)).append("\n");
    }
  }

  private static void appendZhProperties(In18Context context, StringBuilder fileContent) {
    //SYS_100000=\u7cfb\u7edf\u9519\u8bef\uff0c\u8bf7\u7a0d\u540e\u518d\u8bd5
    Set<String> keys = context.keyCodeMap.keySet();
    for (String key : keys) {
      String code = context.keyCodeMap.get(key);
      if (StrUtil.isNotEmpty(code)) {
        //转成unicode编码
        fileContent.append(String.format("%s=%s", code, UnicodeUtil.toUnicode(key, true)))
            .append("\n");
      }
    }
  }

  private static void appendEnProperties(In18Context context, StringBuilder fileContent) {
    //SYS_100000=System error, please try again later
    Set<String> keys = context.keyCodeMap.keySet();
    for (String key : keys) {
      String code = context.keyCodeMap.get(key);
      if (StrUtil.isNotEmpty(code)) {
        fileContent.append(String.format("%s=%s", code, code)).append("\n");
      }
    }
  }

  public static void writeFile(String filePath, String content) {
    File file = new File(filePath);
    try (FileOutputStream fos = new FileOutputStream(file)) {
      fos.write(content.getBytes());
      fos.flush();
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  private static void generateCode(In18Context context, Map<String, String> existKeyCode) {
    String codeStart = context.codeStart;
    String[] arr = codeStart.split("_");
    String suffixStr = arr[arr.length - 1];
    int suffixLength = suffixStr.length();
    int suffixNum = Integer.parseInt(suffixStr);
    String pre = codeStart.substring(0, codeStart.lastIndexOf("_") + 1);
    for (String key : context.keys) {
      if (StrUtil.isEmpty(existKeyCode.get(key))) {
        //枚举中已定义过的中文就不需要重复生成code了
        String code = pre + getSuffix(suffixNum++, suffixLength);
        context.keyCodeMap.put(key, code);
      }
    }
    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < arr.length-1; i++) {
      sb.append(arr[i]).append("_");
    }
    sb.append(suffixNum);
    String codeEnd = sb.toString();
    context.codeStart = codeEnd;
  }

  private static String getSuffix(int num, int suffixLength) {
    String numStr = String.valueOf(num);
    int i = suffixLength - numStr.length();
    if (i <= 0) {
      return numStr;
    }
    while (i-- > 0) {
      numStr = "0" + numStr;
    }
    return numStr;
  }

  public static String getClassPath(In18Context context, String classPath) {
    if (StrUtil.isEmpty(classPath)) {
      return classPath;
    }
    return getClassPath(context.javaPath, classPath);
  }

  public static String getClassPath(String javaPath, String classPath) {
    String pathSuffix = classPath.replace(".", "/");
    return javaPath + pathSuffix;
  }
}


class In18EnumInfo {

  //枚举类
  Class<? extends Enum> enumClass;
  //枚举类
  Class utilClass;
  //从第几行开始插入新增的枚举项
  int startRowNum;

  public In18EnumInfo(Class<? extends Enum> enumClass, Class utilClass) {
    this.enumClass = enumClass;
    this.utilClass = utilClass;
  }
}

class In18Context {

  public String baseModuleJavaPath;
  public String baseModuleResourcePath;
  //需要国际化的文件目录，默认不包含test
  String javaPath;
  //需要国际化的文件目录，默认不包含test
  String resourcePath;
  String testResourcePath;

  //需要国际化的文件目录，默认不包含test
  String scanPath;
  //首个code，后续递增
  String codeStart;
  //类中包含那些key，分别在那些行 <classPath,map<key,list<行号>>>
  LinkedHashMap<String, Map<String, Set<Integer>>> map;
  //扫描出来的所有key
  LinkedHashSet<String> keys;
  //每个key对应的code <key,code>
  LinkedHashMap<String, String> keyCodeMap;
  //I8n枚举、工具类信息
  In18EnumInfo enumInfo;
  //中文配置文件路径,resource相对路径
  String zhPropertiesPath;
  //中文配置文件路径,resource相对路径
  int zhPropertiesInsertRow;
  //英文配置文件路径,resource相对路径
  String enPropertiesPath;
  int enPropertiesInsertRow;
  Counter counter = new Counter(0);
  //测试的替换类路径
  private String testReplaceClassPath;
  //需要排除的国际化扫描的包路径
  private Set<String> excludedPacketSet = new HashSet<>();

  public In18Context( In18EnumInfo enumInfo, String javaPath) {
    this.enumInfo = enumInfo;
    this.javaPath = javaPath;
    map = new LinkedHashMap<>();
    keys = new LinkedHashSet<>();
    keyCodeMap = new LinkedHashMap<>();
    String enumClasspath = enumInfo.enumClass.getPackage().getName();
    //剔除i18n枚举类路径
    addExcludedPacketSet(enumClasspath);
  }

  public void addExcludedPacketSet(String... classPaths) {
    if (classPaths != null && classPaths.length > 0) {
      for (int i = 0; i < classPaths.length; i++) {
        String classPath = classPaths[i];
        excludedPacketSet.add(ChineseFinder.getClassPath(this, classPath));
      }
    }
  }

  public Set<String> getExcludedPacketSet() {
    return excludedPacketSet;
  }

  public String getTestReplaceClassPath() {
    return testReplaceClassPath;
  }

  public void setTestReplaceClassPath(String classPath) {
    testReplaceClassPath = ChineseFinder.getClassPath(this, classPath) + ".java";
  }
}

class Counter {

  int count;

  public Counter(int count) {
    this.count = count;
  }

  public int getAndAdd() {
    return count++;
  }

  public int addAndGet() {
    return ++count;
  }
}


class Process {

  static final Pattern p = Pattern.compile("\".*[\u4e00-\u9fa5]{1,}.*\"");
  private PrintWriter output = null;

  public Process(PrintWriter output) {
    this.output = output;
  }

  public List<String> getWord(String names) {
    List<String> word = new ArrayList<>();
    List<String> strings = Arrays.asList(names.split("\""));
    for (String string : strings) {
      Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
      Matcher m = p.matcher(string);
      if (m.find()) {
        String str = !string.endsWith("\\") ? string : string.substring(0, string.length() - 1);
        word.add(str);
      }
    }
    return word;
  }

  public void readTxt(In18Context context, Counter counter,
      File tempFile)
      throws IOException {
    String path = tempFile.getPath();
    BufferedReader reader = new BufferedReader(
        new InputStreamReader(new FileInputStream(tempFile)));
    String tempString = "";
    int i = 0;
    int j = 0;
    while ((tempString = reader.readLine()) != null) {
      j++;
      i++;
      tempString = tempString.trim();
      tempString = filter(tempString);
      if (StrUtil.isBlank(tempString)) {
        continue;
      }
      Matcher m = p.matcher(tempString);
      List<String> word = getWord(tempString);
      for (String s : word) {
        //output.write(tempFile.getName() +"@@@"+ i +"@@@"+tempString +"@@@" +"_"+String.format("%04d" ,j) +"@@@"+s +"\n");
        System.out.println(counter.addAndGet() + ":" + s);
        context.map.computeIfAbsent(path, key -> new HashMap<>())
            .computeIfAbsent(s, key -> new HashSet<>())
            .add(i);
        context.keys.add(s);
      }
    }
    reader.close();
  }

  private String filterWith(String str, String s) {
    if (StrUtil.isEmpty(str) || str.contains(s)) {
      return "";
    }
    return str;
  }

  private String filterWithStart(String str, String s1) {
    if (StrUtil.isEmpty(str) || str.startsWith(s1)) {
      return "";
    }
    return str;
  }

  private String filter(String str) {
    String tempString = str;
    //去掉注释
    tempString = trimAfter(tempString, "//");
    tempString = filterWithStart(tempString, "/*");
    tempString = filterWithStart(tempString, "*");
    tempString = filterWithStart(tempString, "*/");
    //logger
    tempString = filterWithStart(tempString, "logger.");
    tempString = filterWithStart(tempString, "log.");

    if (!tempString.startsWith("@FieldCheck")) {
      //注解的暂时替换不了
      tempString = filterWithStart(tempString, "@");
    }
    //swagger
  /*  tempString = filterWithStart(tempString, "@ApiOperation");
    tempString = filterWithStart(tempString, "@Api");
    tempString = filterWith(tempString, "@ApiParam");
    tempString = filterWith(tempString, "@RequestParam");
    tempString = filterWithStart(tempString, "@ApiModel");
    tempString = filterWithStart(tempString, "@ApiModelProperty");*/
    //check
    //tempString = filterWith(tempString, "@FieldCheck");
    tempString = filterWith(tempString, "@ModelCheck");

    //tempString = filterWith(tempString, "@ExcelProperty");

    return tempString;
  }

  private String trimAfter(String str, String s1) {
    if (StrUtil.isEmpty(str)) {
      return str;
    }
    if (str.startsWith(s1)) {
      return "";
    }
    if (!str.contains(s1)) {
      return str;
    }
    return str.substring(0, str.indexOf(s1));
  }


  public void scan(In18Context context, String folder)
      throws IOException {
    Counter counter = context.counter;
    File dir = new File(folder);
    if (dir.isDirectory()) {
      String[] children = dir.list();
      for (int i = 0; i < children.length; i++) {
        File tempFile = new File(dir, children[i]);
        if (tempFile.isDirectory()) {
          scan(context, tempFile.getPath());
        } else {
          String fileName = tempFile.getName();
          if (fileName.endsWith(".java")) {
            if (!isExcluded(tempFile.getPath(), context)) {
              readTxt(context, counter, tempFile);
            }
          }
        }
      }
    }
    //输出扫描结果并打印
    Map<String, String> map = new HashMap<>();
    context.keys.forEach(k -> map.put(k, k));
    System.out.println(JSONUtil.toJsonStr(map));
  }

  private boolean isExcluded(String fileName, In18Context context) {
    Set<String> excludedPacketSet = context.getExcludedPacketSet();
    for (String excludedPacket : excludedPacketSet) {
      if (fileName.startsWith(excludedPacket)) {
        return true;
      }
    }
    return false;
  }

  @FunctionalInterface
  interface RowReplaceHandler {

    RowReplaceResult replaceHandle(String str, String key, String code, In18Context context);
  }

  @FunctionalInterface
  interface PropertyAppender {

    int appendProperties(In18Context context, StringBuilder fileContent);
  }


  @FunctionalInterface
  interface AfterRowHandler {

    String afterRow(String row, In18Context context);
  }

  @FunctionalInterface
  interface FilterRow {

    boolean filter(String str);
  }

  @FunctionalInterface
  interface FilterClass {

    boolean filterClass(String str);
  }

  public static class RowReplaceResult {

    String result;
    boolean hasChange;

    public RowReplaceResult(String result, boolean hasChange) {
      this.result = result;
      this.hasChange = hasChange;
    }
  }
}