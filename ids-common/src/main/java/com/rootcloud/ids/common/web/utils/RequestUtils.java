package com.rootcloud.ids.common.web.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rootcloud.ids.common.core.constant.SecurityConstants;
import com.rootcloud.ids.common.core.enums.AuthenticationMethodEnum;
import com.nimbusds.jose.JWSObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 请求工具类
 */
@Slf4j
public class RequestUtils {

  @SneakyThrows
  public static String getGrantType() {
    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    String grantType = request.getParameter(SecurityConstants.GRANT_TYPE_KEY);
    return grantType;
  }


  /**
   * 获取登录认证的客户端ID
   * <p>
   * 兼容两种方式获取OAuth2客户端信息（client_id、client_secret） 方式一：client_id、client_secret放在请求路径中
   * 方式二：放在请求头（Request Headers）中的Authorization字段，且经过加密，例如 Basic Y2xpZW50OnNlY3JldA== 明文等于
   * client:secret
   *
   * @return
   */
  @SneakyThrows
  public static String getOAuth2ClientId() {

    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

    // 从请求路径中获取
    String clientId = request.getParameter(SecurityConstants.CLIENT_ID_KEY);
    if (StrUtil.isNotBlank(clientId)) {
      return clientId;
    }

    // 从请求头获取
    String basic = request.getHeader(SecurityConstants.AUTHORIZATION_KEY);
    if (StrUtil.isNotBlank(basic) && basic.startsWith(SecurityConstants.BASIC_PREFIX)) {
      basic = basic.replace(SecurityConstants.BASIC_PREFIX, Strings.EMPTY);
      String basicPlainText = new String(
          Base64.getDecoder().decode(basic.getBytes(StandardCharsets.UTF_8)),
          StandardCharsets.UTF_8);
      clientId = basicPlainText.split(":")[0]; //client:secret
    }
    return clientId;
  }

  /**
   * 解析JWT获取获取认证方式
   *
   * @return
   */
  @SneakyThrows
  public static String getAuthenticationMethod() {
    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    String refreshToken = request.getParameter(SecurityConstants.REFRESH_TOKEN_KEY);

    String payload = StrUtil.toString(JWSObject.parse(refreshToken).getPayload());
    JSONObject jsonObject = JSONUtil.parseObj(payload);

    String authenticationMethod = jsonObject.getStr(SecurityConstants.AUTHENTICATION_METHOD);
    if (StrUtil.isBlank(authenticationMethod)) {
      authenticationMethod = AuthenticationMethodEnum.USERNAME.getValue();
    }
    return authenticationMethod;
  }

  /**
   * 响应网关收到的请求
   *
   * @param request
   * @param exchange
   * @return
   */
  public static Mono<Void> responseRequest(ServerHttpRequest request, ServerWebExchange exchange,
      Integer code, String msg, boolean success) {

    //mock签名验证失败，直接返回错误。下面是个例子，自行封装吧
    ServerHttpResponse response = exchange.getResponse();
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("code", code);
    jsonObject.put("msg", msg);
    jsonObject.put("success", success);
    response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
    return response.writeWith(Mono.just(
        exchange.getResponse().bufferFactory().wrap(jsonObject.toString().getBytes())));
  }


}
