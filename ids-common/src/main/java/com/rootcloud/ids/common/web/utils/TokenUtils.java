package com.rootcloud.ids.common.web.utils;

import org.springframework.http.HttpCookie;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
public class TokenUtils {

  /**
   * 获取token
   * @param cookie
   * @param request
   * @return
   */
  public static String getToken(MultiValueMap<String, HttpCookie> cookie,
      ServerHttpRequest request) {
    String tokenValue = "";

    //未传cookies,就从header中获取。方便swagger调用
    if (cookie == null || cookie.size() == 0) {
      tokenValue = request.getHeaders().get("access_token").get(0);
      //默认过期时间是当前时间的最近一个月
    } else {
      //获取cookies中的过期时间和access_token
      tokenValue = cookie.get("access_token").get(0).getValue();
      if (StringUtils.isEmpty(tokenValue)) {
        tokenValue = request.getHeaders().get("access_token").get(0);
      }
      return tokenValue;
    }
    return tokenValue;
  }

}
