package com.rootcloud.ids.common.web.utils;
import static com.rootcloud.ids.common.core.constant.SecurityConstants.JWT_PREFIX;

import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;


import com.rootcloud.esmp.common.dto.cache.UserDTO;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2020-10-21 10:42
 **/
public class SecurityUtils {
    private static final ThreadLocal<UserDTO> USER_HOLDER = new ThreadLocal<>();

    public static void add(UserDTO userDto) {
        USER_HOLDER.set(userDto);
    }

    public static UserDTO getCurrentUser() {
        return USER_HOLDER.get();
    }

    public static String getCurrentUserId() {
        UserDTO userDto = USER_HOLDER.get();
        if (userDto == null) {
            throw new RuntimeException(I18nUtil.message(I18nCode.SYS_100145));
        }
        return userDto.getUserId();
    }

    public static String getCurrentUserName() {
        UserDTO userDto = USER_HOLDER.get();
        if (userDto == null) {
            throw new RuntimeException(I18nUtil.message(I18nCode.SYS_100145));
        }
        return userDto.getUsername();
    }

    public static String getCurrentTenantId() {
        return Optional.ofNullable(getCurrentUser())
            .map(UserDTO::getTenantId)
            .orElse(null);
    }

    public static String getCurrentToken() {
        return Optional.ofNullable(getCurrentUser())
            .map(UserDTO::getToken)
            .map(t -> JWT_PREFIX + t)
            .orElse(null);
    }

    public static String getCurrentTenantIdOrElseThrow() {
        return Optional.ofNullable(getCurrentTenantId())
            .orElseThrow(() -> new RuntimeException(I18nCode.SYS_100145.message()));
    }

    public static void remove() {
        USER_HOLDER.remove();
    }

}
