package com.rootcloud.ids.common.mybatis.utils;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description wrapper工具，用于数据权限
 * @since 2022/3/23 2:23 下午
 */
public class DSWrapperUtil {

    /**
     * 机型数据权限过滤使用
     *
     * @param entityClass  目标类
     * @param equipmentColumn 机型字段，为null表示跳过当前过滤条件
     * @param <T>          目标类泛型
     * @param <R>          字段泛型
     * @return LambdaQueryWrapper<T>
     */
    public static <T, R extends Serializable> LambdaQueryWrapper<T> lambdaQueryByEquipment(Class<T> entityClass, SFunction<T, R> equipmentColumn) {
        //下面是从用户的缓存获取权限，自行修改
        List<Long> equipmentIds = new ArrayList<>();
        LambdaQueryWrapper<T> wrapper = new LambdaQueryWrapper<>(entityClass);
        //column为null表示跳过当前过滤条件
        if (null != equipmentColumn) {
            if (CollUtil.isEmpty(equipmentIds)) {
                //过滤数据为空表示无权限
                wrapper.apply("1 <> 1");
                return wrapper;
            }
            if (equipmentIds.size() == 1) {
                wrapper.eq(equipmentColumn, equipmentIds.get(0));
            } else {
                wrapper.in(equipmentColumn, equipmentIds);
            }
        }

        return wrapper;
    }

}
