package com.rootcloud.ids.common.core.enums;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @Description 用户性别
 * @ClassName UserSex
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public enum UserSexEnum {

    /**
     * MAN:男、WOMAN:女、OTHER:其它
     */
    MAN(1, I18nCode.SYS_100133),
    WOMAN(0, I18nCode.SYS_100134),
    OTHER(2, I18nCode.SYS_100135);

    UserSexEnum(int code, I18nCode descp) {
        this.code = code;
        this.descp = descp;
    }

    @EnumValue
    private final int code;
    private final I18nCode descp;

    public int getCode() {
        return code;
    }

    public String getDescp() {
        return I18nUtil.message(descp);
    }

}
