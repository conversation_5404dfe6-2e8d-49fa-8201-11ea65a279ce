/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.ids.common.web.utils;

import static com.rootcloud.esmp.common.Constants.LOCALE_HEADER;
import static com.rootcloud.ids.common.core.constant.GlobalConstants.API_REQUEST_ID_HEADER;
import static com.rootcloud.ids.common.core.constant.GlobalConstants.LOG_REQUEST_ID;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import com.rootcloud.ids.common.web.config.LanguageLocalConfig;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.i18n.LocaleContextHolder;

@Slf4j
public class HutoolRequestUtil {

  public static HttpResponse intercept(HttpRequest httpRequest, @NotNull Function<HttpRequest, HttpResponse> function) {
    String requestId = Optional.ofNullable(httpRequest.header(API_REQUEST_ID_HEADER))
        .orElseGet(() -> MDC.get(LOG_REQUEST_ID));
    if (StringUtils.isBlank(requestId)) {
      requestId = UUID.randomUUID().toString().replaceAll("-", "");
    }
    httpRequest.header(API_REQUEST_ID_HEADER, requestId);
    final String language = I18nUtil.getCurrentLanguage();
    httpRequest.header(LOCALE_HEADER, language);

    log.info("Hutool工具拦截请求，并添加request-id: {}, Language: {}", requestId, language);
    return function.apply(httpRequest);
  }
}
