package com.rootcloud.ids.common.i18n;

import com.rootcloud.esmp.common.i18n.I18nStatusCodeProvider;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import lombok.Getter;

/**
 * 国际化code
 */
public enum I18nCode implements I18nStatusCodeProvider {
  SYS_100000("SYS_100000", "系统错误"),
  SYS_100002("SYS_100002", "令牌"),
  SYS_100003("SYS_100003", "数据权限中心接口文档"),
  SYS_100004("SYS_100004", "令牌过期时间"),
  SYS_100005("SYS_100005", "是否返回异常（dg-info=show）"),
  SYS_100006("SYS_100006", "开发key"),
  SYS_100007("SYS_100007", "oauth2 协议需要在参数前面添加Bearer "),
  SYS_100008("SYS_100008", "接口文档"),
  SYS_100009("SYS_100009", "新建"),
  SYS_100010("SYS_100010", "移除"),
  SYS_100011("SYS_100011", "修改"),
  SYS_100012("SYS_100012", "查看"),
  SYS_100013("SYS_100013", "部门"),
  SYS_100014("SYS_100014", "用户"),
  SYS_100015("SYS_100015", "数据权限"),
  SYS_100016("SYS_100016", "部件型号"),
  SYS_100017("SYS_100017", "部件清单"),
  SYS_100018("SYS_100018", "设备清单"),
  SYS_100019("SYS_100019", "文件管理"),
  SYS_100020("SYS_100020", "设备型号"),
  SYS_100021("SYS_100021", "机型维护"),
  SYS_100022("SYS_100022", "组织用户权限"),
  SYS_100023("SYS_100023", "用户数据权限"),
  SYS_100024("SYS_100024", "成功"),
  SYS_100025("SYS_100025", "失败"),
  SYS_100026("SYS_100026", "异常"),
  SYS_100027("SYS_100027", "组织"),
  SYS_100028("SYS_100028", "启用"),
  SYS_100029("SYS_100029", "未启用"),
  SYS_100030("SYS_100030", "机械大类"),
  SYS_100031("SYS_100031", "机型"),
  SYS_100032("SYS_100032", "所有权"),
  SYS_100033("SYS_100033", "本组织使用权"),
  SYS_100034("SYS_100034", "跨组织使用权"),
  SYS_100035("SYS_100035", "已发布"),
  SYS_100036("SYS_100036", "未发布"),
  SYS_100037("SYS_100037", "默认"),
  SYS_100038("SYS_100038", "非默认"),
  SYS_100039("SYS_100039", "挖掘机械"),
  SYS_100040("SYS_100040", "工程起重机械"),
  SYS_100041("SYS_100041", "混凝土机械"),
  SYS_100042("SYS_100042", "压实机械"),
  SYS_100043("SYS_100043", "路面机械"),
  SYS_100044("SYS_100044", "港口机械"),
  SYS_100045("SYS_100045", "桩工机械"),
  SYS_100046("SYS_100046", "军用工程机械"),
  SYS_100047("SYS_100047", "消防装备机械"),
  SYS_100048("SYS_100048", "环保机械"),
  SYS_100049("SYS_100049", "掘进机械"),
  SYS_100050("SYS_100050", "高空作业机械"),
  SYS_100051("SYS_100051", "工程机械专用零部件"),
  SYS_100052("SYS_100052", "其他专用工程机械"),
  SYS_100053("SYS_100053", "公用"),
  SYS_100054("SYS_100054", "电动化机械"),
  SYS_100055("SYS_100055", "装修机械"),
  SYS_100056("SYS_100056", "钢筋及预应力机械"),
  SYS_100057("SYS_100057", "筑工机械"),
  SYS_100058("SYS_100058", "凿岩机械"),
  SYS_100059("SYS_100059", "电梯及扶梯"),
  SYS_100060("SYS_100060", "石油机械"),
  SYS_100061("SYS_100061", "矿山机械"),
  SYS_100062("SYS_100062", "总计"),
  SYS_100063("SYS_100063", "机械大类："),
  SYS_100064("SYS_100064", "机型："),
  SYS_100065("SYS_100065", "设备型号："),
  SYS_100066("SYS_100066", "修改前值："),
  SYS_100067("SYS_100067", "，修改后值："),
  SYS_100068("SYS_100068", "升级前版本："),
  SYS_100069("SYS_100069", "目标版本："),
  SYS_100070("SYS_100070", "任务名称："),
  SYS_100071("SYS_100071", "失败原因："),
  SYS_100072("SYS_100072", "下发成功"),
  SYS_100073("SYS_100073", "下发失败"),
  SYS_100074("SYS_100074", "修改关联物模型名称字段信息"),
  SYS_100075("SYS_100075", "关联物模型名称"),
  SYS_100076("SYS_100076", "物模型"),
  SYS_100077("SYS_100077", "认证标识"),
  SYS_100078("SYS_100078", "认证密钥"),
  SYS_100079("SYS_100079", "关联设备名称"),
  SYS_100080("SYS_100080", "物实例"),
  SYS_100081("SYS_100081", "部件名称"),
  SYS_100082("SYS_100082", "部件："),
  SYS_100083("SYS_100083", "部件节点："),
  SYS_100084("SYS_100084", "型号："),
  SYS_100085("SYS_100085", "设备："),
  SYS_100086("SYS_100086", "从本地上传文件包："),
  SYS_100087("SYS_100087", "从协议建模工具同步协议文件包："),
  SYS_100088("SYS_100088", "下载文件："),
  SYS_100089("SYS_100089", "型号名称"),
  SYS_100090("SYS_100090", "设备编号"),
  SYS_100091("SYS_100091", "文件包名称"),
  SYS_100092("SYS_100092", "文件包:"),
  SYS_100093("SYS_100093", "设备车牌号"),
  SYS_100094("SYS_100094", "设备VIN码"),
  SYS_100095("SYS_100095", "设备物料编码"),
  SYS_100096("SYS_100096", "字段信息"),
  SYS_100097("SYS_100097", "新建机型:"),
  SYS_100098("SYS_100098", "新建设备型号:"),
  SYS_100099("SYS_100099", "名称"),
  SYS_100100("SYS_100100", "部门添加"),
  SYS_100101("SYS_100101", "添加"),
  SYS_100102("SYS_100102", "组织授权"),
  SYS_100103("SYS_100103", "执行了：推送日志信息给日志平台dmpJobPushDmpLogToLogging发生了异常"),
  SYS_100104("SYS_100104", "产品ID不允许为空"),
  SYS_100105("SYS_100105", "名称长度不规范！"),
  SYS_100106("SYS_100106", "组织ID不允许为空"),
  SYS_100107("SYS_100107", "组织ID不能为空"),
  SYS_100108("SYS_100108", "机械大类不存在！"),
  SYS_100109("SYS_100109", "名称: "),
  SYS_100110("SYS_100110", " 已重复!"),
  SYS_100111("SYS_100111", "产品不存在！"),
  SYS_100112("SYS_100112", "该分类下有子集不能删除！"),
  SYS_100113("SYS_100113", "机械大类或机型不存在！"),
  SYS_100114("SYS_100114", "机型不存在！"),
  SYS_100115("SYS_100115", "设备型号不存在！"),
  SYS_100116("SYS_100116", "系统默认"),
  SYS_100117("SYS_100117", "机型不存在!"),
  SYS_100118("SYS_100118", "租户不存在!"),
  SYS_100119("SYS_100119", "租户管理员不能移除"),
  SYS_100120("SYS_100120", "注销成功"),
  SYS_100121("SYS_100121", "执行成功"),
  SYS_100122("SYS_100122", "用户请求参数错误"),
  SYS_100123("SYS_100123", "请求资源不存在"),
  SYS_100124("SYS_100124", "请求必填参数为空"),
  SYS_100125("SYS_100125", "访问权限异常"),
  SYS_100126("SYS_100126", "系统执行出错"),
  SYS_100127("SYS_100127", "身份证"),
  SYS_100128("SYS_100128", "军官证"),
  SYS_100129("SYS_100129", "驾驶证"),
  SYS_100130("SYS_100130", "用户名"),
  SYS_100131("SYS_100131", "手机号"),
  SYS_100132("SYS_100132", "开放式认证系统唯一身份标识"),
  SYS_100133("SYS_100133", "男"),
  SYS_100134("SYS_100134", "女"),
  SYS_100135("SYS_100135", "其它"),
  SYS_100136("SYS_100136", "正常"),
  SYS_100137("SYS_100137", "删除"),
  SYS_100138("SYS_100138", "开启"),
  SYS_100139("SYS_100139", "冻结"),
  SYS_100140("SYS_100140", "停用"),
  SYS_100141("SYS_100141", "递增因子必须大于0"),
  SYS_100142("SYS_100142", "递减因子必须大于0"),
  SYS_100143("SYS_100143", "协议建模工具"),
  SYS_100144("SYS_100144", "请传入认证头"),
  SYS_100145("SYS_100145", "获取登录用户信息失败"),
  SYS_100146("SYS_100146", "参数校验失败"),
  SYS_100147("SYS_100147", "类型错误"),
  SYS_100148("SYS_100148", "请求体不可为空"),
  SYS_100149("SYS_100149", "微服务调用异常"),
  SYS_100150("SYS_100150", "字段类型错误"),
  SYS_100151("SYS_100151", "自己加入的组织"),
  SYS_100152("SYS_100152", "自己的客户"),
  SYS_100153("SYS_100153", "用户名密码模式"),
  SYS_100154("SYS_100154", "刷新token"),
  SYS_100155("SYS_100155", "客户端模式"),
  SYS_100156("SYS_100156", "授权码模式"),
  SYS_100157("SYS_100157", "IAM调用路径："),
  SYS_100158("SYS_100158", "J1939"),
  SYS_100159("SYS_100159", "OTA"),
  SYS_100160("SYS_100160", "描述说明"),
  SYS_100161("SYS_100161", "描述说明"),
  SYS_100162("SYS_100162", "描述说明"),
  ;

  // i18n配置文件中的key
  private final String code;

  // i18n配置文件中的value.
  @Getter
  private final String desc;

  public String getCode() {
    return code;
  }

  I18nCode(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public String message() {
    return I18nUtil.message(this);
  }
}
