package com.rootcloud.ids.common.mybatis.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.github.pagehelper.PageInterceptor;
import com.rootcloud.ids.common.mybatis.handler.IntegerArrayJsonTypeHandler;
import com.rootcloud.ids.common.mybatis.handler.LongArrayJsonTypeHandler;
import com.rootcloud.ids.common.mybatis.handler.StringArrayJsonTypeHandler;
import com.rootcloud.esmp.common.dto.cache.UserDTO;
import com.rootcloud.esmp.common.utils.SecurityUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.aop.interceptor.PerformanceMonitorInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;


@Configuration
@EnableTransactionManagement
public class MybatisPlusConfig implements MetaObjectHandler {

    /**
     * pagehelper的分页插件
     */
    @Bean
    public PageInterceptor pageInterceptor() {
        return new PageInterceptor();
    }

    /**
     * 分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.POSTGRE_SQL));
        return interceptor;
    }

    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> {
            // 全局注册自定义TypeHandler
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            typeHandlerRegistry.register(String[].class, JdbcType.OTHER, StringArrayJsonTypeHandler.class);
            typeHandlerRegistry.register(Long[].class, JdbcType.OTHER, LongArrayJsonTypeHandler.class);
            typeHandlerRegistry.register(Integer[].class, JdbcType.OTHER, IntegerArrayJsonTypeHandler.class);
        };
    }

    /**
     * SQL执行效率插件
     * //@Profile({"dev", "test"})// 设置 dev test 环境开启
     */
    /*@Bean
    @Profile({"dev", "test"})
    public PerformanceMonitorInterceptor performanceInterceptor() {
        return new PerformanceMonitorInterceptor();
    }*/


    /**
     * 新增填充创建时间
     *
     * @param metaObject obj
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        System.out.println("insert init start ...");
        UserDTO userDetailsDto = SecurityUtils.getCurrentUser();
        this.strictInsertFill(metaObject, "creator", String.class, userDetailsDto == null ? "UNKNOW" : userDetailsDto.getUsername());
        this.strictInsertFill(metaObject, "createTime", LocalDateTime::now, LocalDateTime.class);
        this.strictInsertFill(metaObject, "deleteFlag", Integer.class, 0);
        this.strictInsertFill(metaObject, "version", Integer.class, 0);
    }

    /**
     * 更新填充更新时间
     *
     * @param metaObject obj
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        UserDTO userDetailsDto = SecurityUtils.getCurrentUser();
        this.strictUpdateFill(metaObject, "modifier", String.class, userDetailsDto == null ? "UNKNOW" : userDetailsDto.getUsername());
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime::now, LocalDateTime.class);
    }

    /**
     * 解决druid 控制台报错discard long time none received connection的问题。
     */
    @PostConstruct
    public void setProperties() {
        System.setProperty("druid.mysql.usePingMethod", "false");
    }

}
