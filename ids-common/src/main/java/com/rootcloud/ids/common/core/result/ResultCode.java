package com.rootcloud.ids.common.core.result;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@NoArgsConstructor
public enum ResultCode implements IResultCode, Serializable {

    /**
     * 定义返回码
     */
    SUCCESS(2000, I18nCode.SYS_100121),
    PARAM_ERROR(30000, I18nCode.SYS_100122),
    RESOURCE_NOT_FOUND(40000, I18nCode.SYS_100123),
    PARAM_IS_NULL(50000, I18nCode.SYS_100124),
    AUTHORIZED_ERROR(60000, I18nCode.SYS_100125),

    SYSTEM_EXECUTION_ERROR(70000, I18nCode.SYS_100126);

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return I18nUtil.message(desc);
    }

    private Integer code;

    private I18nCode desc;

    @Override
    public String toString() {
        return "{" +
                "\"code\":\"" + code + '\"' +
                ", \"msg\":\"" + getDesc() + '\"' +
                '}';
    }


    public static ResultCode getValue(Integer code) {
        for (ResultCode value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        // 默认系统执行错误
        return SYSTEM_EXECUTION_ERROR;
    }
}
