package com.rootcloud.ids.common.web.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description jwt信息 很多字段不知道什么意思，我也不清楚，后面用到再去研究
 * @since 2022/2/21 3:43 下午
 */
@Data
public class IamJwtParseDTO {

    private Boolean isLogAs;

    private Boolean isSystemOrganization;

    /**
     * 用户名-不是姓名，系统生成的标识
     */
    private String userName;

    private String nonce;

    /**
     * 角色，有多个
     */
    private List<String> authorities;

    private String clientId;

    private Boolean isSystemAdmin;

    private Date createdAt;

    private List<String> scope;

    private String dataCenterId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 区域
     */
    private String zoneId;

    /**
     * 过期时间（秒）
     */
    private Long exp;

    private Long iat;

    /**
     * 用户信息
     */
    private IamUserInfoDTO user;

}
