package com.rootcloud.ids.common.core.enums;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @Description 用户状态
 * @ClassName UserState
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public enum UserStateEnum {

    /**
     * OPEN:开启、CLOSE:关闭、STOP:停用
     */
    OPEN(1, I18nCode.SYS_100138),
    CLOSE(5, I18nCode.SYS_100139),
    STOP(0, I18nCode.SYS_100140);

    UserStateEnum(int code, I18nCode descp) {
        this.code = code;
        this.descp = descp;
    }

    @EnumValue
    private final int code;
    private final I18nCode descp;

    public int getCode() {
        return code;
    }

    public String getDescp() {
        return I18nUtil.message(descp);
    }

}
