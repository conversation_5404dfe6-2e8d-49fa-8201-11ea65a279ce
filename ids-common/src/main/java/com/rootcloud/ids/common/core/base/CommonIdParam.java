package com.rootcloud.ids.common.core.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since  2022/03/01
 */
@Data
@ApiModel(value = "CommonIdParam")
public class CommonIdParam implements Serializable {

    @ApiModelProperty(value = "数据ID", required = true)
    @NotNull(message = "数据ID不允许为空")
    private Long id;

}
