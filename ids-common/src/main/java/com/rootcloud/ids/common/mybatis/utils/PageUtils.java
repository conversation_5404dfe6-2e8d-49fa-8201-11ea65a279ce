package com.rootcloud.ids.common.mybatis.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/14
 */
@Slf4j
public class PageUtils {

    public static <T, R> Page<R> convert(IPage<T> pageInfo, Class<R> beanClass) {
        Page<R> pageResp = buildPage(pageInfo);
        for (T record : pageInfo.getRecords()) {
            try {
                R r = beanClass.newInstance();
                BeanUtils.copyProperties(record, r);
                pageResp.getRecords().add(r);
            } catch (Exception e) {
                log.error("构建Page返回对象失败", e);
            }
        }
        return pageResp;
    }

    public static <T, R> Page<R> convert(IPage<T> pageInfo, Function<T, R> convertFun) {
        Page<R> pageResp = new Page<>(pageInfo.getCurrent(), pageInfo.getSize(), pageInfo.getTotal());
        pageResp.setRecords(pageInfo.getRecords().stream().map(convertFun).collect(Collectors.toList()));
        return pageResp;
    }

    public static <T, R> Page<R> buildPage(IPage<T> pageInfo) {
        Page<R> pageResp = new Page<>(pageInfo.getCurrent(), pageInfo.getSize(), pageInfo.getTotal());
        pageResp.setRecords(new LinkedList<>());
        return pageResp;
    }

    public static <T, R> Page<R> buildPage(IPage<T> pageInfo, List<R> respList) {
        Page<R> pageResp = new Page<>(pageInfo.getCurrent(), pageInfo.getSize(), pageInfo.getTotal());
        pageResp.setRecords(respList);
        return pageResp;
    }
}
