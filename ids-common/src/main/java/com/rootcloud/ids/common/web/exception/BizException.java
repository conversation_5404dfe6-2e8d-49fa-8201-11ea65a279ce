package com.rootcloud.ids.common.web.exception;

import com.rootcloud.ids.common.core.result.IResultCode;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class BizException extends RuntimeException {

    public IResultCode resultCode;

    public BizException(IResultCode errorCode) {
        super(errorCode.getDesc());
        this.resultCode = errorCode;
    }

    public BizException(String message){
        super(message);
    }

    public BizException(String message, Throwable cause){
        super(message, cause);
    }

    public BizException(Throwable cause){
        super(cause);
    }

}
