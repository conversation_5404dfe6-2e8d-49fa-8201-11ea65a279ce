package com.rootcloud.ids.common.mybatis.utils;

import com.rootcloud.ids.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public final class CodeUtils {

    public static void autoGenerator(String projectPath, List<String> tables){
        FastAutoGenerator.create("***************************************","postgres","hello1234")
                .globalConfig(builder -> {
                    builder.author("huiming.yin")               //作者
                            .outputDir(projectPath+"/src/main/java")    //输出路径(写到java目录)
                            .enableSwagger()           //开启swagger
                            .commentDate("yyyy-MM-dd")
                            .fileOverride();            //开启覆盖之前生成的文件

                })
                .packageConfig(builder -> {
                    builder.parent("com.rootcloud.ids")
                            .moduleName("ids-ucenter")
                            .entity("entity")
                            .service("service")
//                            .serviceImpl("serviceImpl")
                            .controller("controller")
                            .mapper("mapper")
                            .xml("mapper")
                            .pathInfo(Collections.singletonMap(OutputFile.mapperXml,projectPath +"/src/main/resources/mapper"));
                })
                .strategyConfig(builder -> {
                    builder.addInclude(tables)
                            .addTablePrefix("ucenter_")
                            .serviceBuilder()
                            .formatServiceFileName("I%sService")
                            .formatServiceImplFileName("%sServiceImpl")
                            .entityBuilder()
                            .enableLombok()
                            .disableSerialVersionUID()
                            .superClass(BaseEntity.class)
                            .logicDeleteColumnName("deleted")
                            .enableTableFieldAnnotation()
                            .controllerBuilder()
                            .formatFileName("%sController")
                            .enableRestStyle()
                            .mapperBuilder()
                            .superClass(BaseMapper.class)
                            .formatMapperFileName("%sMapper")
                            .enableMapperAnnotation()
                            .formatXmlFileName("%sMapper");
                })
                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }

//    public static void main(String[] args) {
//        String projectPath = "/Users/<USER>/ideaProjects/ids/ids-ucenter";
//        List<String> tables = new ArrayList<>();
////        tables.add("cloud_user");
//      tables.add("ucenter_group_permission");
//      tables.add("ucenter_user_permission");
//
//      //代码生成
//        CodeUtils.autoGenerator(projectPath,tables);
//    }

}
