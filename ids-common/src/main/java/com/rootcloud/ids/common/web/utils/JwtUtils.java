package com.rootcloud.ids.common.web.utils;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import com.rootcloud.ids.common.core.constant.SecurityConstants;
import com.rootcloud.ids.common.web.dto.IamJwtParseDTO;
import com.rootcloud.ids.common.web.exception.BizException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.List;

/**
 * JWT工具类
 */
@Slf4j
public class JwtUtils {

    @SneakyThrows
    public static JSONObject getJwtPayload() {
        String payload = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest().getHeader(
            SecurityConstants.JWT_PAYLOAD_KEY);
        if (null == payload) {
            throw new BizException(I18nUtil.message(I18nCode.SYS_100144));
        }
        JSONObject jsonObject = JSONUtil.parseObj(URLDecoder.decode(payload,StandardCharsets.UTF_8.name()));
        return jsonObject;
    }

    /**
     * 解析JWT获取用户ID
     *
     * @return
     */
    public static Long getUserId() {
        Long id = getJwtPayload().getLong(SecurityConstants.USER_ID_KEY);
        return id;
    }

    /**
     * 解析JWT获取获取用户名
     *
     * @return
     */
    public static String getUsername() {
        String username = getJwtPayload().getStr(SecurityConstants.USER_NAME_KEY);
        return username;
    }



    /**
     * JWT获取用户角色列表
     *
     * @return 角色列表
     */
    public static List<String> getRoles() {
        List<String> roles = null;
        JSONObject payload = getJwtPayload();
        if (payload.containsKey(SecurityConstants.JWT_AUTHORITIES_KEY)) {
            roles = payload.getJSONArray(SecurityConstants.JWT_AUTHORITIES_KEY).toList(String.class);
        }
        return roles;
    }

    /**
     * 从token中获取payload字符串信息
     *
     * @param token token
     * @return String
     */
    public static IamJwtParseDTO parseJwt(String token) throws ParseException {
        SignedJWT signed = SignedJWT.parse(token);
        JWTClaimsSet claims = signed.getJWTClaimsSet();
        return claims != null ? JSONUtil.toBean(claims.toString(), IamJwtParseDTO.class) : null;
    }
}
