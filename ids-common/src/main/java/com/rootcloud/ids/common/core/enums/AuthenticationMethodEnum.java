package com.rootcloud.ids.common.core.enums;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;

import lombok.Getter;

/**
 * 认证方式枚举
 *
 * <AUTHOR> href="mailto:<EMAIL>">xianrui</a>
 * @date 2021/10/4
 */
public enum AuthenticationMethodEnum {

    USERNAME("username", I18nCode.SYS_100130),
    MOBILE("mobile", I18nCode.SYS_100131),
    OPENID("openId", I18nCode.SYS_100132);

    @Getter
    private String value;


    private I18nCode label;

    AuthenticationMethodEnum(String value, I18nCode label) {
        this.value = value;
        this.label = label;
    }

    public static AuthenticationMethodEnum getByValue(String value) {
        AuthenticationMethodEnum authenticationMethodEnum = null;
        for (AuthenticationMethodEnum item : values()) {
            if (item.getValue().equals(value)) {
                authenticationMethodEnum = item;
                continue;
            }
        }
        return authenticationMethodEnum;
    }

    public String getLabel() {
        return I18nUtil.message(label);
    }
}
