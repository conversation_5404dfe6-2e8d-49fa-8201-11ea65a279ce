package com.rootcloud.ids.common.core.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @Description 用户注册来源
 * @ClassName UserRegFrom
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public enum UserRegFromEnum {

    /**
     * APP:手机客户端、WEB:网页、WECHAT:微信、UNKNOWN:未知
     */
    APP(1, "App"),
    WEB(2, "Web"),
    WECHAT(3, "Wechat"),
    UNKNOWN(4, "Unknown");

    UserRegFromEnum(int code, String descp) {
        this.code = code;
        this.descp = descp;
    }

    @EnumValue
    private final int code;
    private final String descp;

    public int getCode() {
        return code;
    }

    public String getDescp() {
        return descp;
    }

}
