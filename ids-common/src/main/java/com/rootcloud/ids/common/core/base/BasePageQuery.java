package com.rootcloud.ids.common.core.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BasePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "当前页", example = "1")
    private int page = 1;

    @ApiModelProperty(value = "每页记录数", example = "10")
    private int pageSize = 10;

    public int skip() {
        return page > 1 ? (page - 1) * pageSize : 0;
    }

    public int limit() {
        return pageSize;
    }

    public <T> Page<T> batisPage() {
        return new Page(this.page, this.pageSize);
    }


}
