/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.ids.common.web.config;

import static com.rootcloud.esmp.common.Constants.LOCALE_HEADER;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.rootcloud.esmp.common.i18n.I18nUtil;
import java.util.Locale;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;

//@Configuration
public class LanguageLocalConfig implements LocaleResolver {

  @Override
  public Locale resolveLocale(HttpServletRequest request) {
    Locale locale = I18nUtil.getDefaultLocale();
    String language = request.getHeader(LOCALE_HEADER);
    if (StringUtils.isBlank(language)) {
      return locale;
    }
    //if (StringUtils.isNotBlank(language)) {
    //  String[] splitLanguage = language.split("-");
    //  if (splitLanguage.length > 1) {
    //    locale = new Locale(splitLanguage[0], splitLanguage[1]);
    //  } else {
    //    locale = new Locale(splitLanguage[0]);
    //  }
    //}
    return new Locale(language);
  }

  @Override
  public void setLocale(HttpServletRequest httpServletRequest,
                        HttpServletResponse httpServletResponse, Locale locale) {
    throw new UnsupportedOperationException("Cannot change HTTP accept header - use a different locale resolution strategy");
  }

  //@Bean
  public LocaleResolver localeResolver() {
    return new LanguageLocalConfig();
  }
}
