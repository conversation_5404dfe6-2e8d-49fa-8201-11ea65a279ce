package com.rootcloud.ids.common.core.enums;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;


import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * <p> dmp  delete_flag枚举 </p> #记录删除标志#ENUM#0:正常:normal,1:删除:remove#
 *
 * <AUTHOR>
 * @since 2022-03-02
 */

public enum DeleteFlagEnum implements IEnum<Integer> {
  /**
   * 正常
   */
  NORMAL(0, I18nCode.SYS_100136),
  /**
   * 删除
   */
  DELETE(1, I18nCode.SYS_100137),;
  private int code;
  private I18nCode label;

  private DeleteFlagEnum(int code, I18nCode label) {
    this.code = code;
    this.label = label;
  }

  public Integer getCode() {
    return code;
  }

  public String getLabel() {
    return I18nUtil.message(label);
  }

  public static String getLabel(Integer code) {
    if (code != null) {
      for (DeleteFlagEnum value : DeleteFlagEnum.values()) {
        if (value.code == code) {
          return I18nUtil.message(value.label);
        }
      }
    }
    return null;
  }

  public static DeleteFlagEnum codeOf(int code) {
    for (DeleteFlagEnum value : DeleteFlagEnum.values()) {
      if (value.getCode() == code) {
        return value;
      }
    }
    throw new RuntimeException("cant not change code: " + code + " to DeleteFlagEnum.");
  }

  @Override
  public Integer getValue() {
    return code;
  }
}
