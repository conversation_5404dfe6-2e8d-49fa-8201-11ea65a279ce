package com.rootcloud.ids.common.mybatis.utils;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;

/**
 * <AUTHOR>
 * @description
 * @since 2022/3/7 7:00 下午
 */
public class SqlUtils {

    /**
     * 替换特殊字符串
     * MP使用like的时候，一些特殊字符会导致sql无效
     *
     * @param target 目标
     * @return String
     */
    public static String likeEscape(Object target) {
        String val = "";
        if (ObjectUtils.isNotEmpty(target)) {
            val = target + val;
            val = val.replaceAll("/", "//");
            val = val.replaceAll("_", "/_");
            val = val.replaceAll("%", "/%");
        } else {
            val = null;
        }
        return val;
    }
}
