package com.rootcloud.ids.common.core.enums;
import com.rootcloud.ids.common.i18n.I18nCode;
import com.rootcloud.esmp.common.i18n.I18nUtil;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @Description 用户证件类型
 * @ClassName UserCardType
 * <AUTHOR>
 * @Date 2021/12/16
 * @Version 1.0
 */
public enum UserCardTypeEnum {

    /**
     * IDENTITY:身份证、OFFICER:军官证、DRIVER:驾驶证
     */
    IDENTITY(1, I18nCode.SYS_100127),
    OFFICER(2, I18nCode.SYS_100128),
    DRIVER(3, I18nCode.SYS_100129);

    UserCardTypeEnum(int code, I18nCode descp) {
        this.code = code;
        this.descp = descp;
    }

    @EnumValue
    private final int code;
    private final I18nCode descp;

    public int getCode() {
        return code;
    }

    public String getDescp() {
        return I18nUtil.message(descp);
    }

}
