package com.rootcloud.ids.common.core.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since  2022/03/01
 */
@Data
@ApiModel(value = "CommonIdsParam")
public class CommonIdsParam implements Serializable {
    @ApiModelProperty(value = "数据ID", required = true)
    @NotNull(message = "数据ids不允许为空")
    private List<Long> ids;
}
