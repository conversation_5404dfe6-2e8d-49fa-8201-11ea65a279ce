package com.rootcloud.ids.common.core.result;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/3/9
 */
@Data
public class PageResult<T> {

    /**
     * 请求结果状态
     */
    private boolean success;

    /**
     * 请求结果编码
     */
    private int code;
    /**
     * 请求结果描述
     */
    private String desc;
    /**
     * 请求结果数据
     */
    private List<T> data;
    /**
     * 是否第一页
     */
    private boolean first;
    /**
     * 是否最后一页
     */
    private boolean last;
    /**
     * 当前页
     */
    private long number;
    /**
     * 当前页大小
     */
    private long size;
    /**
     * 总页数
     */
    private long totalPages;
    /**
     * 总条数
     */
    private long totalSize;

    public PageResult(int code, String desc, List<T> data, long totalSize, long totalPage, long number, long size,
                      boolean isFirst, boolean isLast) {
        this.code = code;
        if (code == ResultCode.SUCCESS.getCode()) {
            this.success = true;
        }
        this.desc = desc;
        this.data = data;
        this.first = isFirst;
        this.last = isLast;
        this.number = number;
        this.size = size;
        this.totalPages = totalPage;
        this.totalSize = totalSize;

    }

    public PageResult(int code, String desc, Page<T> data) {
        this.code = code;
        if (code == ResultCode.SUCCESS.getCode()) {
            this.success = true;
        }
        this.desc = desc;
        if (data != null) {
            this.data = data.getRecords();
            this.first = !data.hasPrevious();
            this.last = !data.hasNext();
            this.number = data.getCurrent();
            this.size = data.getSize();
            this.totalPages = data.getPages();
            this.totalSize = data.getTotal();
        } else {
            this.data = null;
        }
    }

    public static <T, R extends ResultCode> PageResult<T> success(Page<T> data) {
        return new PageResult<>(R.SUCCESS.getCode(), R.SUCCESS.getDesc(), data);
    }

    public static <T, R extends ResultCode> PageResult<T> success(Page<T> data, String desc) {
        return new PageResult<>(R.SUCCESS.getCode(), desc, data);
    }

    public static <T, R extends ResultCode> PageResult<T> success(List<T> data, String desc, long totalSize,
                                                                  int totalPage, int number, int size, boolean isFirst, boolean isLast) {
        return new PageResult<>(R.SUCCESS.getCode(), desc, data, totalSize, totalPage, number, size, isFirst, isLast);
    }

    public static <T, R extends ResultCode, S> PageResult<T> success(List<T> data, String desc,
                                                                     Page<S> page) {
        return new PageResult<>(R.SUCCESS.getCode(), desc, data, page.getTotal(), page.getPages(), page.getCurrent(),
                page.getSize(), !page.hasPrevious(), !page.hasNext());
    }

}
