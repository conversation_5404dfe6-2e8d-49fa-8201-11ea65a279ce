#!/bin/bash

set -ex

pwd

# 获取 mode 参数
mode=
while :; do
  case $1 in
  --mode=?*)
    mode=${1#*=}
    ;;
  *)
    break
    ;;
  esac

  shift
done

echo "$mode"
if [[ "$mode" != "master" && "$mode" != "release" ]]; then
  echo "mode must be master or release"
  exit 1
fi

DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$DIR"
DOCKER_REPO="${DOCKER_REGISTRY}/sanyic"
PROJECT_NAME="ids-ucenter"

DOCKER_IMAGE_NAME="$DOCKER_REPO/$PROJECT_NAME"

echo "----> Start Build Docker Images at $PROJECT_ROOT"

cd $PROJECT_ROOT

## 根据 mode 获取版本号
VERSION=

if [[ "$mode" == "master" ]]; then
  VERSION=$(node -p "require('$PROJECT_ROOT/package.json').version")
  VERSION="$VERSION"
else
  RELEASE_VERSION=$(node -p "require('$PROJECT_ROOT/package.json').releaseVersion")
  RELEASE_SUBVERSION=$(node -p "require('$PROJECT_ROOT/release.json').version")
  VERSION="$RELEASE_VERSION.$RELEASE_SUBVERSION"
fi

DOCKER_IMAGE_NAME_VERSION="$DOCKER_IMAGE_NAME:$VERSION"

echo "----> Build Docker Image $DOCKER_IMAGE_NAME_VERSION"

docker build -t ${DOCKER_IMAGE_NAME_VERSION} --build-arg BUILD_VERSION=${VERSION} .

echo "----> Push Docker Image to remote repository"

docker push ${DOCKER_IMAGE_NAME_VERSION}

echo "----> Clean $PROJECT_NAME Image"
docker rmi ${DOCKER_IMAGE_NAME_VERSION}
