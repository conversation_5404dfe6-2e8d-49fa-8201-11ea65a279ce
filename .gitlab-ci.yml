image: rootcloudplat/gitlab-ci-java:0.1.3

variables:
  # When using dind service we need to instruct docker, to talk with the
  # daemon started inside of the service. The daemon is available with
  # a network connection instead of the default /var/run/docker.sock socket.
  #
  # The 'docker' hostname is the alias of the service container as described at
  # https://docs.gitlab.com/ee/ci/docker/using_docker_images.html#accessing-the-services
  #
  # If you're using GitLab Runner 12.7 or earlier with the Kubernetes executor and Kubernetes 1.6 or earlier,
  # the variable must be set to tcp://localhost:2375 because of how the
  # Kubernetes executor connects services to the job container
  # DOCKER_HOST: tcp://localhost:2375
  #
  DOCKER_HOST: tcp://docker:2375
  #
  # This will instruct Dock<PERSON> not to start over TLS.
  DOCKER_TLS_CERTDIR: ""
  # This will suppress any download for dependencies and plugins or upload messages which would clutter the console log.
  # `showDateTime` will show the passed time in milliseconds. You need to specify `--batch-mode` to make this work.
  MAVEN_OPTS: "-Dhttps.protocols=TLSv1.2 -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true"
  # As of Maven 3.3.0 instead of this you may define these options in `.mvn/maven.config` so the same config is used
  # when running from the command line.
  # `installAtEnd` and `deployAtEnd` are only effective with recent version of the corresponding plugins.
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version -DinstallAtEnd=true -DdeployAtEnd=true"


# Cache downloaded dependencies and plugins between builds.
# To keep cache across branches add 'key: "$CI_JOB_NAME"'
cache:
  paths:
    - .m2/repository
    - .yarn/
    - .npm/
    - node_modules/

before_script:
  - echo "before script"

stages:
  - deploy
  - update-oam
  - send_failure



# sonar task
#sonarqube-check:
#  tags:
#    - k8s
#  # Jobs in the same stage run in parallel.
#  # Run sonarqube check at every test stage
#  stage: check
#  script:
#    - echo "sonarqube-check"
#    - env | grep CI_
#    - wget https://mirrors.cnnic.cn/apache/maven/maven-3/3.6.3/binaries/apache-maven-3.6.3-bin.tar.gz
#    - tar -xzvf apache-maven-3.6.3-bin.tar.gz
#    #拷贝maven到安装目录
#    - cp -rf apache-maven-3.6.3 /root/
#    - /root/apache-maven-3.6.3/bin/mvn org.jacoco:jacoco-maven-plugin:report clean install  test
#    - /root/apache-maven-3.6.3/bin/mvn sonar:sonar   -Dsonar.projectKey=sanyic_ids-ucenter_AYAXTt1WUJQ98BBq8iEs -Dsonar.projectName=esmp-ids-ucenter  -Dsonar.host.url=http://sonarqube.devops.rootcloudapp.com   -Dsonar.login=****************************************
#
#  only:
#    - /^release-.*$/
#  except:
#    variables:
#      - $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME == "renovate/configure"
#  allow_failure: true
deploy:
  stage: deploy
  services:
    - docker:19.03.12-dind
  tags:
    - k8s
  script:
    - echo "Start deploy"
    - docker login -u ${DOCKER_REGISTRY_USERNAME} -p ${DOCKER_REGISTRY_PASSWORD} ${DOCKER_REGISTRY}
    - docker info
    - yarn install --cache-folder .yarn --prefer-offline
    # bump version and publish npm package
    - npx semantic-release
    - env | grep CI_
#    - wget https://mirrors.cloud.tencent.com/apache/maven/maven-3/3.6.3/binaries/apache-maven-3.6.3-bin.tar.gz
#    - tar -xzvf apache-maven-3.6.3-bin.tar.gz
    - wget https://mirrors.huaweicloud.com/apache/maven/maven-3/3.6.3/binaries/apache-maven-3.6.3-bin.tar.gz
    - tar -xzvf apache-maven-3.6.3-bin.tar.gz
    #拷贝maven到安装目录
    - cp -rf apache-maven-3.6.3 /root/
    - cp -rf settings.xml /root/apache-maven-3.6.3/conf/
    - /root/apache-maven-3.6.3/bin/mvn package -Dmaven.test.skip=true
    # build docker image and push image to docker repository
    - bash +x  docker.sh --mode=master
    # publish npm pkgs without dependency
    # - cd npm_publish && node copyVersion.js && npm publish && cd -
    - echo $PWD
    # - renovate rootcloud-platform/0-devops/integration-env
  only:
    - master
    - /^release-.*$/
  except:
    - schedules
  artifacts:
    paths:
      - package.json #重要,若不配置则会影响下面步骤读取不到最新版本


update-oam:
  stage: update-oam
  tags:
    - k8s
  image: registry.rootcloud.com/devops/ci-appstand-version:v0.7
  script:
    - |
      export APP_NAME=newc
      export SRV_NAME=ids-ucenter
      export COMARKET_BRANCH=master
      ORG_VERSION=$(/usr/bin/tool/bin/node -p "require('./package.json').version") 
      curl "http://gitlab.irootech.com/api/v4/projects/9127/ref/main/trigger/pipeline?token=${APP_STANDARD_TRIGGER_TOKEN}" --header 'Content-Type: application/json' --data "{\"component\": \"${SRV_NAME}\",\"tag\": \"${ORG_VERSION}\",\"envs\": \"nc-qa-master,nc-qa,nc-qa-area2,nc-qa-area3\"}"
  only:
    - master

send_failure:
  stage: send_failure
  tags:
    - k8s
  services:
    - name: docker:19.03.13-dind
      command: [ "--registry-mirror=https://mirror.ccs.tencentyun.com" ]
  script:
    - chmod +x ./scripts/wechatRobot.sh
    - ./scripts/wechatRobot.sh fail
  when: on_failure


after_script:
  - echo "End CI"
