#!/bin/sh

set -ex

STATUS_MESSAGE='😊构建成功✅'
if [ -n "${1}" ]; then
  STATUS_MESSAGE='😭构建失败❌'
  FAIL='true'
fi

CONTENT="[${CI_PROJECT_NAME}](${CI_PROJECT_URL})的${CI_COMMIT_REF_NAME}分支 ${STATUS_MESSAGE}\n\
> 查看pipeline: [${CI_COMMIT_TITLE}](${CI_PIPELINE_URL})\n\
> 提交者: ${GITLAB_USER_NAME}"

PACKAGE_VERSION=$(cat package.json \
  | grep version \
  | head -1 \
  | awk -F: '{ print $2 }' \
  | sed 's/[",]//g' \
  | awk '{$1=$1};1')

RELEASE_VERSION=$(cat release.json \
  | grep version \
  | head -1 \
  | awk -F: '{ print $2 }' \
  | sed 's/[",}]//g' \
  | awk '{$1=$1};1')

if [ "${CI_JOB_NAME}" = "deploy" ] && [ "${FAIL}" = "" ]; then
  CONTENT="${CONTENT}\n> 版本: [${CI_COMMIT_REF_NAME}-${PACKAGE_VERSION}](${CI_PROJECT_URL}/blob/master/CHANGELOG.md)"
fi

curl "${WECHAT_ROBOT_URL}" \
  -H 'Content-Type: application/json' \
  -d "{ \
    \"msgtype\": \"markdown\", \
    \"markdown\": {\"content\": \"${CONTENT}\"} \
    }"

