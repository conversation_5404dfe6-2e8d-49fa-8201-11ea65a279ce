{"name": "@rootcloud/ids-ucenter", "group": "com.rootcloud", "version": "1.6.28", "description": "Rootcloud ids ucenter", "main": "index.js", "scripts": {"semantic-release": "semantic-release --no-ci"}, "config": {"unsafe-perm": true}, "files": ["index.js"], "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/npm", {"npmPublish": false}], "@semantic-release/git", ["@semantic-release/gitlab", {"gitlabUrl": "http://gitlab.irootech.com"}]]}, "repository": {"type": "git", "url": "http://gitlab.irootech.com/sanyic/ids-ucenter.git"}, "keywords": ["Emqx", "Kafka"], "author": "Rootcloud", "license": "ISC", "devDependencies": {"@semantic-release/changelog": "3.0.4", "@semantic-release/commit-analyzer": "6.3.0", "@semantic-release/git": "8.0.0", "@semantic-release/gitlab": "6.0.9", "@semantic-release/npm": "5.1.15", "@semantic-release/release-notes-generator": "7.3.0", "semantic-release": "16.0.4"}}