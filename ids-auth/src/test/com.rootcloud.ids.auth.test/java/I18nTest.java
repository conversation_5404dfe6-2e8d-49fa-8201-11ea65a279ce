import com.rootcloud.ids.common.i18n.ChineseFinder;
import java.util.Collections;
import java.util.List;

public class I18nTest {

  public static void main(String[] args) throws Exception {
    List<String> modules = Collections.singletonList("ids-auth");
    String codeStart = "SYS_100120";
    int enumStartRowNum = 126;
    int zhPropertiesInsertRow = 120;
    int enPropertiesInsertRow = 120;
    ChineseFinder.multiModuleScan(
        codeStart,
        enumStartRowNum,
        zhPropertiesInsertRow,
        enPropertiesInsertRow,
        "/ids-common",
        modules);
  }
}
