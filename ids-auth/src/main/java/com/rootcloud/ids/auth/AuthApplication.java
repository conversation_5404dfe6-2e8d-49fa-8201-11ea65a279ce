package com.rootcloud.ids.auth;

import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;


@SpringBootApplication
@EnableFeignClients(basePackages = "com.rootcloud.ids.auth.feign")
public class AuthApplication {

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(AuthApplication.class);
        springApplication.setBannerMode(Banner.Mode.OFF);
        springApplication.run(args);
    }

}
