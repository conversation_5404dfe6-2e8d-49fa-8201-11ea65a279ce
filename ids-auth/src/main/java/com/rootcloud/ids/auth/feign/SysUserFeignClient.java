package com.rootcloud.ids.auth.feign;

import com.rootcloud.ids.common.core.result.Result;
import com.rootcloud.ids.ucenter.vo.backup.SysPermissionVO;
import com.rootcloud.ids.ucenter.vo.backup.SysUserVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * @Description 系统用户实现远程调用接口
 * @InterfaceName ISysUserService
 * <AUTHOR>
 * @Date 2022/1/10
 * @Version 1.0
 */

@FeignClient(name = "cloud-ucenter")
public interface SysUserFeignClient {

    @GetMapping("/user/findByAccount/{account}")
    Result<SysUserVO> findByAccount(@PathVariable String account);

    @GetMapping(value = "/permission/findPermissionByUserId/{userId}")
    Result<List<SysPermissionVO>> findPermissionByUserId(@PathVariable Long userId);
}
