package com.rootcloud.ids.auth.service.impl;

import static java.util.stream.Collectors.toSet;

import com.rootcloud.ids.auth.dto.SysUserDetails;
import com.rootcloud.ids.auth.feign.SysUserFeignClient;
import com.rootcloud.ids.common.core.result.Result;
import com.rootcloud.ids.ucenter.vo.backup.SysPermissionVO;
import com.rootcloud.ids.ucenter.vo.backup.SysUserVO;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * @Description
 * @ClassName UserDetailsServiceImpl
 * <AUTHOR>
 * @Date 2021/12/18
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private SysUserFeignClient userFeignClient;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        SysUserDetails userDetails = new SysUserDetails();
        Result<SysUserVO> result = userFeignClient.findByAccount(username);
        if (Result.isSuccess(result)) {
            SysUserVO userVO = result.getData();
            userDetails.setUserId(userVO.getUserId());
            userDetails.setNickName(userVO.getNickName());
            userDetails.setUserName(userVO.getUserName());
            userDetails.setPassword(userVO.getPassword());
            userDetails.setPhone(userVO.getPhone());
            userDetails.setSex(userVO.getSex());
            userDetails.setPortrait(userVO.getPortrait());
            Result<List<SysPermissionVO>> rs = userFeignClient.findPermissionByUserId(userVO.getUserId());
            if (Result.isSuccess(rs)) {
                Set<SysPermissionVO> sysPermissionVOSet = rs.getData().stream().collect(toSet());
                userDetails.setPermission(sysPermissionVOSet);
            }
        }

        return userDetails;
    }
}
