package com.rootcloud.ids.auth.dto;

import com.rootcloud.ids.ucenter.vo.backup.SysPermissionVO;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.io.Serializable;
import java.util.Collection;
import java.util.Set;

/**
 * @Description
 * @ClassName UserDTO
 * <AUTHOR>
 * @Date 2021/12/18
 * @Version 1.0
 */
@Data
public class SysUserDetails implements Serializable, UserDetails {

    private static final long serialVersionUID = 5538522337801286424L;

    private Long userId;
    private String nickName;
    private String userName;
    private String password;
    private String phone;
    private Integer sex;
    private String portrait;
    private Set<SysPermissionVO> permission;
    private Set<SimpleGrantedAuthority> authorities;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities(){
        return this.authorities;
    }


    @Override
    public String getPassword() {
        return this.password;
    }

    @Override
    public String getUsername() {
        return this.userName;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }
}
