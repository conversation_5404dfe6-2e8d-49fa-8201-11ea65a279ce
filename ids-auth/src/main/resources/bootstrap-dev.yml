#spring:
#  application:
#    name: ids-auth
#  cloud:
#    nacos:
#      # 注册中心
#      discovery:
#        server-addr: http://10.70.21.14:31909
#      # 配置中心
#      config:
#        server-addr: ${spring.cloud.nacos.discovery.server-addr}
#        file-extension: yaml
##        shared-configs[0]:
##          data-id: cloud-common.yaml
##          refresh: true
#
#jwt:
#  key:
#    alias: weihong
#    password: lanweihong
#    storepass: lanweihong
#    path: jwt.jks
