#!groovy
library 'sharelib@test'

// 确认服务名称与项目归属,通常情况下服务名与工程名一致，命名规范与k8s内部一致
def configMap = [
        "service": "ids-ucenter",
        "project": "sanyic"
]
properties([
        disableConcurrentBuilds(),
        disableResume(),
        parameters([
                imageTag(credentialId: 'docker', description: '',
                        filter: '.*', image: "${configMap.project}/${configMap.service}/${env.BRANCH_NAME}",
                        name: 'DOCKER_IMAGE', registry: 'https://private-registry.rootcloud.com', tagOrder: 'REV_NATURAL'),
                choice(choices: ['false', 'true'], description: '当分支为dev，参数为true，才会运行开发环境CI/CD', name: 'autodevops'),
                choice(choices: ['false', 'true'], description: '当为true时,只运行CI流程', name: 'only_ci'),
                choice(choices: ['false', 'true'], description: '当为true时,只运行CD流程，DOCKER_IMAGE参数生效', name: 'only_cd')
        ])
])
configMap.put('autodevops',"${autodevops}")
configMap.put('only_ci',"${only_ci}")
configMap.put('only_cd',"${only_cd}")
run(configMap)
